import { useTheme } from '@/context/theme-provider';
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Icon } from './icon';

export function ModeToggle() {
  const { setTheme, theme } = useTheme();

  return (
    <Tabs defaultValue={theme} className="rtl:text-right">
      <TabsList>
        <TabsTrigger onClick={() => setTheme('light')} value="light">
          <Icon icon="hugeicons:sun-03" width={20} />
        </TabsTrigger>
        <TabsTrigger onClick={() => setTheme('dark')} value="dark">
          <Icon icon="hugeicons:moon-02" width={20} />
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
