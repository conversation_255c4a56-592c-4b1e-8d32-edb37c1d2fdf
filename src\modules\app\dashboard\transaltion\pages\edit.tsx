import { Form, Modal, useForm, TextInput } from '@/index';
import { useGetLanguages, useUpdateLangugaeKey } from '@/modules/app/dashboard/transaltion/apis/queries';
import i18next from 'i18next';
import { Button } from '@/components/ui/button';

import { useTranslation } from 'react-i18next';
const EditKeyDialog = ({ isOpen, onOpenChange, keyBeingEdited }: any) => {
  const { data: languages } = useGetLanguages();
  const { mutate: updateLanguageKey, isPending } = useUpdateLangugaeKey();
  const { t } = useTranslation();
  const initialFormState = languages?.reduce((acc: any, lang: any) => {
    acc[lang.code] = keyBeingEdited[lang.code] || '';
    return acc;
  }, {});

  const { form, setFieldValue } = useForm(initialFormState || {});

  const handleSubmit = () => {
    const translations = languages?.map((lang) => ({
      language_id: lang.id,
      key_id: keyBeingEdited.id,
      value: form[lang.code],
    }));

    updateLanguageKey(translations, {
      onSuccess: async () => {
        await i18next.reloadResources();
        onOpenChange(false);
      },
    });
  };

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('dashboardPage.keys.editKeyDialog.title')}>
      <Form onSubmit={handleSubmit}>
        <div className="space-y-4">
          {languages?.map((lang: any) => (
            <TextInput
              key={lang.id}
              name={lang.name}
              label={lang.name}
              value={form[lang.code]}
              onChange={setFieldValue(lang.code)}
            />
          ))}
        </div>
        <div className="flex justify-end gap-3 mt-3">
          <Button onClick={() => onOpenChange(false)} variant={'outline'} className="min-w-[100px] mt-4">
            {t('cancel')}
          </Button>
          <Button loading={isPending} disabled={isPending} type="submit" className="min-w-[100px] mt-4">
            {t('update')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default EditKeyDialog;
