import { Button } from '@/components/ui/button';
import { Icon } from '@/index';
import LanguageLink from './components/language-link';

const NotFound = () => {
  return (
    <div className="w-full h-[100dvh] text-center flex flex-col gap-1 justify-center items-center container">
      <h1 className="text-8xl md:text-9xl font-black text-primary">404</h1>
      <h4 className="text-xl md:text-2xl text-primary  mt-6 md:mt-10 mb-1.5 md:mb-3 font-semibold">
        Sorry, we couldn&apos;t find this page
      </h4>
      <p className="text-slate-500 mb-5 text-sm md:text-base">
        But dont worry, you can find penlty of other things to do in our home page
      </p>

      <div className="flex gap-2 items-center">
        {/* <Button onClick={() => navigate(-2)} variant="ghost" className="flex items-center gap-2">
          <Icon className="text-xl" icon="solar:arrow-left-broken" />
          Go back
        </Button> */}
        <LanguageLink to="/app">
          <Button variant={'default'} className="flex items-center gap-2">
            <Icon className="text-xl" icon="carbon:home" />
            Home page
          </Button>
        </LanguageLink>
      </div>
    </div>
  );
};

export default NotFound;
