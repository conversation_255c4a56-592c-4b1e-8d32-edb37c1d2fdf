/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ['class'],
  content: [
    './pages/**/*.{ts,tsx}',
    './index.html',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
    './src/**/*.{js,ts,jsx,tsx}',
    './src/**/*.{html,js,jsx,ts,tsx}',
    './src/**/*.css',
  ],

  prefix: '',
  theme: {
    container: {
      center: 'true',
      padding: '2rem',
      screens: {
        '2xl': '1400px',
      },
    },
    extend: {
      colors: {
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          50: 'hsl(var(--primary-50) / <alpha-value>)',
          100: 'hsl(var(--primary-100) / <alpha-value>)',
          200: 'hsl(var(--primary-200) / <alpha-value>)',
          300: 'hsl(var(--primary-300) / <alpha-value>)',
          400: 'hsl(var(--primary-400) / <alpha-value>)',
          500: 'hsl(var(--primary-500) / <alpha-value>)',
          600: 'hsl(var(--primary-600) / <alpha-value>)',
          700: 'hsl(var(--primary-700) / <alpha-value>)',
          800: 'hsl(var(--primary-800) / <alpha-value>)',
          900: 'hsl(var(--primary-900) / <alpha-value>)',
        },
        purple: {
          50: 'hsl(var(--purple-50) / <alpha-value>)',
          100: 'hsl(var(--purple-100) / <alpha-value>)',
          200: 'hsl(var(--purple-200) / <alpha-value>)',
          300: 'hsl(var(--purple-300) / <alpha-value>)',
          400: 'hsl(var(--purple-400) / <alpha-value>)',
          500: 'hsl(var(--purple-500) / <alpha-value>)',
          600: 'hsl(var(--purple-600) / <alpha-value>)',
          700: 'hsl(var(--purple-700) / <alpha-value>)',
          800: 'hsl(var(--purple-800) / <alpha-value>)',
          900: 'hsl(var(--purple-900) / <alpha-value>)',
        },
        black: {
          DEFAULT: 'hsl(var(--black-900))',

          50: 'hsl(var(--black-50) / <alpha-value>)',
          100: 'hsl(var(--black-100) / <alpha-value>)',
          200: 'hsl(var(--black-200) / <alpha-value>)',
          300: 'hsl(var(--black-300) / <alpha-value>)',
          400: 'hsl(var(--black-400) / <alpha-value>)',
          500: 'hsl(var(--black-500) / <alpha-value>)',
          600: 'hsl(var(--black-600) / <alpha-value>)',
          700: 'hsl(var(--black-700) / <alpha-value>)',
          800: 'hsl(var(--black-800) / <alpha-value>)',
          900: 'hsl(var(--black-900) / <alpha-value>)',
        },
        gray: {
          50: 'hsl(var(--gray-50) / <alpha-value>)',
          100: 'hsl(var(--gray-100) / <alpha-value>)',
          200: 'hsl(var(--gray-200) / <alpha-value>)',
          300: 'hsl(var(--gray-300) / <alpha-value>)',
          400: 'hsl(var(--gray-400) / <alpha-value>)',
          500: 'hsl(var(--gray-500) / <alpha-value>)',
          600: 'hsl(var(--gray-600) / <alpha-value>)',
          700: 'hsl(var(--gray-700) / <alpha-value>)',
          800: 'hsl(var(--gray-800) / <alpha-value>)',
          900: 'hsl(var(--gray-900) / <alpha-value>)',
        },
        red: {
          50: 'hsl(var(--red-50) / <alpha-value>)',
          100: 'hsl(var(--red-100) / <alpha-value>)',
          200: 'hsl(var(--red-200) / <alpha-value>)',
          300: 'hsl(var(--red-300) / <alpha-value>)',
          400: 'hsl(var(--red-400) / <alpha-value>)',
          500: 'hsl(var(--red-500) / <alpha-value>)',
          600: 'hsl(var(--red-600) / <alpha-value>)',
          700: 'hsl(var(--red-700) / <alpha-value>)',
          800: 'hsl(var(--red-800) / <alpha-value>)',
          900: 'hsl(var(--red-900) / <alpha-value>)',
        },
        orange: {
          50: 'hsl(var(--orange-50) / <alpha-value>)',
          100: 'hsl(var(--orange-100) / <alpha-value>)',
          200: 'hsl(var(--orange-200) / <alpha-value>)',
          300: 'hsl(var(--orange-300) / <alpha-value>)',
          400: 'hsl(var(--orange-400) / <alpha-value>)',
          500: 'hsl(var(--orange-500) / <alpha-value>)',
          600: 'hsl(var(--orange-600) / <alpha-value>)',
          700: 'hsl(var(--orange-700) / <alpha-value>)',
          800: 'hsl(var(--orange-800) / <alpha-value>)',
          900: 'hsl(var(--orange-900) / <alpha-value>)',
        },
        yellow: {
          50: 'hsl(var(--yellow-50) / <alpha-value>)',
          100: 'hsl(var(--yellow-100) / <alpha-value>)',
          200: 'hsl(var(--yellow-200) / <alpha-value>)',
          300: 'hsl(var(--yellow-300) / <alpha-value>)',
          400: 'hsl(var(--yellow-400) / <alpha-value>)',
          500: 'hsl(var(--yellow-500) / <alpha-value>)',
          600: 'hsl(var(--yellow-600) / <alpha-value>)',
          700: 'hsl(var(--yellow-700) / <alpha-value>)',
          800: 'hsl(var(--yellow-800) / <alpha-value>)',
          900: 'hsl(var(--yellow-900) / <alpha-value>)',
        },
        green: {
          50: 'hsl(var(--green-50) / <alpha-value>)',
          100: 'hsl(var(--green-100) / <alpha-value>)',
          200: 'hsl(var(--green-200) / <alpha-value>)',
          300: 'hsl(var(--green-300) / <alpha-value>)',
          400: 'hsl(var(--green-400) / <alpha-value>)',
          500: 'hsl(var(--green-500) / <alpha-value>)',
          600: 'hsl(var(--green-600) / <alpha-value>)',
          700: 'hsl(var(--green-700) / <alpha-value>)',
          800: 'hsl(var(--green-800) / <alpha-value>)',
          900: 'hsl(var(--green-900) / <alpha-value>)',
        },
        teal: {
          50: 'hsl(var(--teal-50) / <alpha-value>)',
          100: 'hsl(var(--teal-100) / <alpha-value>)',
          200: 'hsl(var(--teal-200) / <alpha-value>)',
          300: 'hsl(var(--teal-300) / <alpha-value>)',
          400: 'hsl(var(--teal-400) / <alpha-value>)',
          500: 'hsl(var(--teal-500) / <alpha-value>)',
          600: 'hsl(var(--teal-600) / <alpha-value>)',
          700: 'hsl(var(--teal-700) / <alpha-value>)',
          800: 'hsl(var(--teal-800) / <alpha-value>)',
          900: 'hsl(var(--teal-900) / <alpha-value>)',
        },
        indigo: {
          50: 'hsl(var(--indigo-50) / <alpha-value>)',
          100: 'hsl(var(--indigo-100) / <alpha-value>)',
          200: 'hsl(var(--indigo-200) / <alpha-value>)',
          300: 'hsl(var(--indigo-300) / <alpha-value>)',
          400: 'hsl(var(--indigo-400) / <alpha-value>)',
          500: 'hsl(var(--indigo-500) / <alpha-value>)',
          600: 'hsl(var(--indigo-600) / <alpha-value>)',
          700: 'hsl(var(--indigo-700) / <alpha-value>)',
          800: 'hsl(var(--indigo-800) / <alpha-value>)',
          900: 'hsl(var(--indigo-900) / <alpha-value>)',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        'semi-primary': 'hsl(var(--semi-primary))',
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        sidebar: {
          DEFAULT: 'hsl(var(--sidebar-background))',
          foreground: 'hsl(var(--sidebar-foreground))',
          primary: 'hsl(var(--sidebar-primary))',
          'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
          accent: 'hsl(var(--sidebar-accent))',
          'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
          border: 'hsl(var(--sidebar-border))',
          ring: 'hsl(var(--sidebar-ring))',
        },
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        shine: {
          '0%': { backgroundPosition: '-100% 0' },
          '100%': { backgroundPosition: '100% 0' },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        shine: 'shine 2s linear infinite',
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [require('tailwindcss-animate'), require('@tailwindcss/typography')],
};
