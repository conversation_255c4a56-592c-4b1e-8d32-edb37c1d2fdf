import React from 'react';
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Icon } from '@iconify/react';
import { useTranslation } from 'react-i18next';

interface IProps {
  description: string;
}

const AlertCopmponent = ({ description }: IProps) => {
  const { t } = useTranslation();
  return (
    <Alert className="bg-blue-50 dark:bg-background text-indigo-700">
      <AlertTitle className="text-xl font-medium">
        <div className="flex items-center gap-1">
          <Icon className="mt-1 text-indigo-700" icon="mdi:alert-circle" width={18} />
          <p>{t('dnaSinglePage.dnaAlertTitle')}</p>
        </div>
      </AlertTitle>
      <AlertDescription className="text-sm ml-2">• {description}</AlertDescription>
    </Alert>
  );
};

export default AlertCopmponent;
