import { IDNA } from '../dna/types';
import { IStatus } from '../dashboard/modules-status/types';
export interface ITopicPayload {
  version: string;
  topic: string;
  subject: string;
  other_subject: string;
  audience: string;
  language: string;
  file_path: string;
  long_text: string;
  url: string;
  txt: string;
  remembering: number;
  understanding: number;
  applying: number;
  analyzing: number;
  at_topic: number;
}

// Interface for the main Course Plan data
export interface ITopic {
  id: number;
  created_at: string; // ISO date string
  updated_at: string; // ISO date string
  title: string;
  course_id: number | null;
  dnas_counts: string;
  subject_id: number;
  audience_id: number;
  language_id: number;
  user_id: number;
  status: string;
  subject: IMetadata;
  audience: IMetadata;
  model: string;
  other_subject: string;
  language: IMetadata;
  long_text: string;
  file_path: string;
  audio_length: string;
  url: string;
  txt: string;
  version: string;
  deleted_at: string | null;
  learning_objectives: string | null;
  dnas: IDNA[]; // Array of DNA objects
  user: User;
  topic_status: IStatus;
  dna_status: IStatus;
  dna_content: string;
  prompt: string | null;
}

// Interface for each DNA object within the Course Plan

// Interface for the user data in the Course Plan and DNA
interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  account_status: string;
  created_at: string;
  updated_at: string;
}

// Interface for Multiple Choice Questions (MCQs)
export interface Mcq {
  // Define the structure based on your actual data
}

// Interface for Fill-in-the-Blank Questions
export interface FillBlank {
  // Define the structure based on your actual data
}

// Interface for Case Studies
export interface CaseStudy {
  // Define the structure based on your actual data
}

// Interface for True/False Questions
export interface TrueFalse {
  // Define the structure based on your actual data
}
