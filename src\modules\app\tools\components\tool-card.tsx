import { Card, Icon } from '@/index';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
export const ToolCard = ({ title, icon, route }: any) => {
  const navigate = useLanguageNavigate();

  const handleNavigate = () => {
    navigate(route);
  };

  return (
    <Card
      onClick={handleNavigate}
      className="w-[180px] h-[180px] cursor-pointer flex justify-center items-center hover:bg-secondary"
    >
      <div className="space-y-3">
        <Icon icon={icon} width={26} />

        <p className="font-medium tracking-tight text-center whitespace-pre-line">{title}</p>
      </div>
    </Card>
  );
};
