import { IPermission, IRole } from '../types';
import { Api } from '@/services';
import { ROLES, GET_PERMISSIONS } from '@/services';
// get roles

export const getRoles = async (): Promise<IRole[]> => {
  const { data } = await Api.get(ROLES);
  return data.data.items;
};

// Get role by ID
export const getRoleById = async (id: string): Promise<IRole> => {
  const { data } = await Api.get(`${ROLES}/${id}`);
  return data.data;
};

// Create a new role
export const createRole = async (payload: { name: string; permissions: IPermission }): Promise<IRole> => {
  const { data } = await Api.post(ROLES, payload);
  return data;
};

// Delete a role by ID
export const deleteRole = async (id: string): Promise<void> => {
  await Api.delete(`${ROLES}/${id}`);
};

// Update a role by ID
export const updateRole = async (id: string, payload: { name: string; permissions: string[] }): Promise<IRole> => {
  const { data } = await Api.put(`${ROLES}/${id}`, payload);
  return data;
};

// Get all permissions
export const getPermissions = async (): Promise<IPermission[]> => {
  const { data } = await Api.get(GET_PERMISSIONS);
  return data.data;
};
