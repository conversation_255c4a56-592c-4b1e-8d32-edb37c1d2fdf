import { Input } from '@/components/ui/input';
import { asField } from '../hocs/field';
import { Label } from '@/components/ui/label';

export const TextInput = asField(
  ({ name = '', label = '', onChange = () => {}, validatorsScroll, errorMessage, autoComplete, ...props }: any) => {
    // Methods
    const handleChange = (e: any) => {
      onChange(e.target.value);
    };

    return (
      <div>
        {label && (
          <div className="mb-2 block">
            <Label>{label}</Label>
          </div>
        )}
        <Input
          className={`${errorMessage ? 'border-red-600' : ''}`}
          id={name}
          onChange={handleChange}
          {...props}
          scrolltoerror={
            validatorsScroll &&
            errorMessage &&
            (document.getElementById(name) as any).scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        />
        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);

// Types
// TextInput.propTypes = {
//   name: PropTypes.string.isRequired,
//   label: PropTypes.string,
//   value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
//   validatorsScroll: PropTypes.bool,
//   onChange: PropTypes.func,
// };
