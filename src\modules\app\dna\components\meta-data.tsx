import { dateAndTimeFormat, formatDateByYear } from '@/utils/helpers';
import { ProtectedComponent } from '@/index';
import { IContributor, IDNA } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/index';
import { memo, useMemo, useState } from 'react';
import PromptViewerDialog from '../../components/prompt-viewer-dialog';
import { TableContentBody, Table, TableContent } from '@/components/theTable';
import { useGetDnaContributors } from '../apis/queries';
import { Badge } from '@/components/ui/badge';
import LanguageLink from '@/components/language-link';
import { Button } from '@/components/ui/button';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
interface MetaDataProps {
  details: IDNA;
}

const LoadingState = () => (
  <div className="flex justify-center w-full p-4">
    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary" width={30} />
  </div>
);

const MetaDataItem = ({ label, value }: { label: string; value: string | number | React.ReactNode }) => (
  <div>
    <p className="text-sm text-black-600">{label}</p>
    <p className="font-medium">{value}</p>
  </div>
);

const ResourcesSection = ({ resources }: { resources: string[] | { title: string; link: string }[] }) => {
  if (!resources) return null;
  if (typeof resources === 'string') return <p>{resources}</p>;
  return (
    <div className="mt-1">
      {resources?.map((resource, index) => (
        <div key={index} className="flex flex-col gap-2 px-3">
          {typeof resource == 'string' ? (
            resource?.includes('https://') ? (
              <a
                href={resource}
                className="pl-4 underline transition-colors text-primary hover:text-primary-dark"
                target="_blank"
                rel="noopener noreferrer"
              >
                - {resource}
              </a>
            ) : (
              <p className="pl-4">- {resource}</p>
            )
          ) : (
            <a
              href={resource.link}
              className="pl-4 underline transition-colors text-primary hover:text-primary-dark"
              target="_blank"
              rel="noopener noreferrer"
            >
              - {resource.title}
            </a>
          )}
        </div>
      )) || '-'}
    </div>
  );
};
// Helper function to calculate reading time
const calculateReadingTime = (wordCount: number): string => {
  const minutes = Math.ceil(wordCount / 90);
  if (minutes <= 0) return 'Less than 1 min';
  if (minutes >= 7) return '7+ min';
  return `${minutes} min`;
};

const MetaData = memo(({ details }: MetaDataProps) => {
  const [contributorsAccordion, setContributorsAccordion] = useState<string>('contributors');
  const { data: contributors, isPending } = useGetDnaContributors(details?.id?.toString(), !!contributorsAccordion);
  const { t, i18n } = useTranslation();

  if (!details) {
    return <LoadingState />;
  }

  const DnaSourse = () => {
    if (details?.text) {
      return t('Text');
    } else if (details.url) {
      return t('URL');
    } else {
      return t('Web');
    }
  };

  const subData = [
    {
      label: t('dnaSinglePage.metaData.createdAt'),
      value: dateAndTimeFormat(details.created_at, i18n.language),
    },
    {
      label: t('dnaSinglePage.metaData.updatedAt'),
      value: dateAndTimeFormat(details.updated_at, i18n.language),
    },
    {
      label: t('model'),
      value: details.model,
    },
    {
      label: t('dnaCreationPage.form.sources'),
      value: DnaSourse(),
    },
  ];

  const mainMetaData = [
    {
      label: t('dnaSinglePage.metaData.subject'),
      value: i18n.dir() === 'ltr' ? details.subject?.name_en : details.subject?.name_ar,
    },
    {
      label: t('dnaSinglePage.metaData.length'),
      value: `${details.word_count} ${t('editDnaModal.word')} = ${calculateReadingTime(details?.word_count || 0)}`,
    },
    {
      label: t('dnaConentPgae.table.topic'),
      value: details?.topic?.title || '-',
    },
    {
      label: t('breadcrumb.myContentPage.allTasks.course'),
      value: details?.topic?.course?.title || '-',
    },
    {
      label: t('dnaSinglePage.metaData.learning level'),
      value: i18n.language === 'en' ? details.bloom_tax?.name_en : details.bloom_tax?.name_ar,
    },
    {
      label: t('dnaSinglePage.metaData.language'),
      value: i18n.language === 'en' ? details.language?.name_en : details.language?.name_ar,
    },
    { label: t('dnaSinglePage.metaData.context'), value: details.long_text || ' -' },
    { label: t('dnaSinglePage.metaData.learning objective'), value: details.learning_objectives },
  ];

  const columns: ITableColumn<IContributor>[] = useMemo(() => {
    return [
      {
        accessorKey: 'name',
        header: t('dnaConentPgae.table.name'),
        cell: ({ row }) => (
          <LanguageLink to={`app/users/${row.id}`} className="flex flex-col gap-1 text-primary hover:underline">
            <p>{row.name}</p>
            <p>{row.email}</p>
          </LanguageLink>
        ),
      },
      {
        accessorKey: 'roles',
        header: t('dnaConentPgae.table.roles'),
        cell: ({ row }) => <div className="flex gap-2">{t('roles.' + row.roles)}</div>,
      },
      {
        accessorKey: 'last_interaction',
        header: t('dnaConentPgae.table.dateTime'),
        cell: ({ row }) => dateAndTimeFormat(row.last_interaction, i18n.language),
      },
      {
        accessorKey: 'contribute_in',
        header: t('dnaConentPgae.table.contribute_in'),
        cell: ({ row }) => (
          <div className="flex gap-2">
            {row.contribute_in.map((contribute, idx) => (
              <Badge key={contribute + idx} className="p-1.5 px-4" variant={'outline'}>
                {contribute}
              </Badge>
            ))}
          </div>
        ),
      },
    ];
  }, [t]);

  const proceduresData = [
    'editor',
    'edtidingApprovedBy',
    'reviwer',
    'ReviweringApprovedBy',
    'ActiviteCreatedBy',
    'ActiviteApprovedBy',
    'SlideShowCreatedBy',
    'SlideShowApprovedBy',
    'ArabicLocalizedBy',
    'ArabicApprovedBy',
    'EnglishLocalizedBy',
    'EnglishApprovedBy',
    'ReadingViewBy',
    'ReadingApproveBy',
    'VideoProducedBy',
    'VideoProductionApprovedBy',
    'VideoUpLoadedApprovedBy',
  ].map((key) => ({
    label: t(`dnaSinglePage.metaData.${key}`),
    value: key === 'editor' ? `${details.user.name} :${formatDateByYear(details.created_at, i18n.dir())}` : '-',
  }));

  return (
    <div className="grid grid-cols-3 gap-6">
      <div className="grid grid-cols-2 col-span-2 gap-6">
        {mainMetaData.slice(0, 3).map((item, index) => (
          <MetaDataItem key={index} label={item.label} value={item.value} />
        ))}
        {mainMetaData.slice(3, 5).map((item, index) => (
          <div key={index}>
            <MetaDataItem label={item.label} value={item.value} />
          </div>
        ))}
        <div className="col-span-2">
          {mainMetaData.slice(5).map((item, index) => (
            <div key={index} className="col-span-2">
              <MetaDataItem label={item.label} value={item.value} />
            </div>
          ))}
        </div>
      </div>
      <ProtectedComponent requiredPermissions={'for_administration'}>
        <MetaDataItem
          label={t('dnaSinglePage.metaData.prompt')}
          value={<PromptViewerDialog prompt={details.prompt} model={details.model} />}
        />
      </ProtectedComponent>
      <ProtectedComponent requiredPermissions={'dna_sub_data'}>
        <div className="col-span-2">
          <p className="text-sm opacity-[0.5]">{t('dnaSinglePage.metaData.keywords')}</p>
          <div className="grid grid-cols-2 gap-2 xl:grid-cols-6">
            {details?.tags?.map((tag: any, index: number) => (
              <p key={index} className={`${index === 4 ? 'col-start-1' : ''} font-medium `}>
                • {tag.name}
              </p>
            )) || '-'}
          </div>
        </div>
      </ProtectedComponent>
      <ProtectedComponent requiredPermissions={'dna_sub_data'}>
        <div className="grid grid-cols-3 col-span-3 gap-6">
          {subData.map((item, index) => (
            <MetaDataItem key={index} label={item.label} value={item.value} />
          ))}
        </div>
      </ProtectedComponent>
      <ProtectedComponent requiredPermissions={'dna_resource_data'}>
        <div className="col-span-2">
          <p className="text-sm opacity-[0.5]">{t('dnaSinglePage.metaData.resources')}</p>
          <ResourcesSection resources={(details?.resources as string[]) || []} />
        </div>
      </ProtectedComponent>

      <ProtectedComponent requiredPermissions={'for_administration'}>
        <div className="col-span-3">
          <Accordion
            value={contributorsAccordion}
            collapsible
            onValueChange={setContributorsAccordion}
            type="single"
            className="w-full"
          >
            <AccordionItem value="contributors">
              <AccordionTrigger>
                <h5 className="mb-2 text-sm capitalize text-black-600">{t('contributors')}</h5>
              </AccordionTrigger>
              <AccordionContent>
                <Table rows={contributors || []} columns={columns} loading={isPending}>
                  <TableContent>
                    <TableContentBody />
                  </TableContent>
                </Table>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </ProtectedComponent>
    </div>
  );
});

MetaData.displayName = 'MetaData';

export default MetaData;
