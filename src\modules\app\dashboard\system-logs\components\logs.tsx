import { Modal } from '@/index';
import { diffWords } from 'diff';
import { dateAndTimeFormat, getLogStatus } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import LanguageLink from '@/components/language-link';
import { Button } from '@/components/ui/button';
import { IDnaLogData } from '../types';

interface IProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  data: IDnaLogData;
}

const TextDiffHighlighter = ({ originalText, modifiedText }: any) => {
  const differences = diffWords(originalText || '', modifiedText || '');

  return (
    <div className="border p-4 rounded-md">
      {differences.map((part, index) => {
        const className = part.added
          ? 'bg-green-200 text-green-800 mx-1'
          : part.removed
          ? 'bg-red-200 text-red-800 mx-1'
          : '';
        return <span key={index} className={className} dangerouslySetInnerHTML={{ __html: part?.value || '' }} />;
      })}
    </div>
  );
};

const LogsData = ({ isOpen, onOpenChange, data }: IProps) => {
  const { t, i18n } = useTranslation();

  const displayKeys = [
    'title',
    'dna status',
    'description',
    'dna content',
    'bloom taxonomy',
    'learning objectives',
    'dna phase',
  ];

  let url;
  const modalType = data.model_type.includes('\\') ? data.model_type.split('\\').pop() : data.model_type;

  if (modalType === 'Dna') {
    url = `/app/my-content/DNAs/${data.model_id}`;
  } else if (modalType === 'Course') {
    url = `/app/my-content/courses/${data.model_id}`;
  }

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} width={1000} modalHeader={t('metadataPage.logs')}>
      <div className="p-6 rounded-lg mb-6 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="flex items-center gap-3">
            <div>
              <p className="text-sm text-gray-600 font-medium">{t('systemLogs.actionType')}</p>
              <div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getLogStatus(data?.action)}`}>
                {data?.action}
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div>
              <p className="text-sm text-gray-600 font-medium">{t('systemLogs.created_at')}</p>
              <p className="text-sm">{dateAndTimeFormat(data.created_at, i18n.language)}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div>
              <p className="text-sm text-gray-600 font-medium">{t('systemLogs.modelType')}</p>
              <p className="text-sm font-medium">
                {data.model_type.includes('\\') ? data.model_type.split('\\').pop() : data.model_type}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <div>
              <p className="text-sm text-gray-600 font-medium">{t('systemLogs.created_by')}</p>
              <div className="text-sm">
                <p className="font-medium">{data?.user?.name}</p>
                <p className="text-gray-600">{data?.user?.email}</p>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3 md:col-span-2">
            <div>
              <p className="text-sm text-gray-600 font-medium">{t('systemLogs.link')}</p>
              {modalType !== 'Topic' ? (
                <LanguageLink to={url}>
                  <Button size="sm" className="mt-1">
                    {`${t(`dashboard.seeThis${modalType}`)}`}
                  </Button>
                </LanguageLink>
              ) : (
                <p className="text-sm text-gray-500">{t('noLinkForThisTopic')}</p>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="space-y-6">
        {data.logs.map((logEntry, logIndex) => {
          // Handle the new format where each log entry has an 'attribute' field
          if (logEntry.attribute) {
            const keyLower = logEntry.attribute.toLowerCase();
            const keyWithCapitalizedDna = keyLower
              .split(' ')
              .map((word) => (word === 'dna' ? 'DNA' : word))
              .join(' ');

            if (displayKeys.includes(keyLower)) {
              const containerClass =
                keyLower === 'dna content'
                  ? 'grid grid-cols-2 gap-2 max-h-[400px] overflow-scroll'
                  : 'grid grid-cols-2 gap-2';

              return (
                <div key={logIndex} className="space-y-2">
                  <p className="font-medium capitalize">{t(`systemlogs.popup.type.${keyWithCapitalizedDna}`)}</p>
                  <div className={containerClass}>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.oldValue')}</p>
                      <div
                        className="border p-4 rounded-md "
                        dangerouslySetInnerHTML={{
                          __html: logEntry.old || '<p>No previous content</p>',
                        }}
                      />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.chanages')}</p>
                      <TextDiffHighlighter originalText={logEntry.old || ''} modifiedText={logEntry.new || ''} />
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            return (
              <div className="space-y-4 overflow-hidden" key={logIndex}>
                {Object.entries(logEntry).map(([key, values]: [string, any], entryIndex) => {
                  const keyLower = key.toLowerCase();
                  const keyWithCapitalizedDna = keyLower
                    .split(' ')
                    .map((word) => (word === 'dna' ? 'DNA' : word))
                    .join(' ');

                  if (displayKeys.includes(keyLower)) {
                    const containerClass =
                      keyLower === 'dna content'
                        ? 'grid grid-cols-2 gap-2 max-h-[400px] overflow-scroll'
                        : 'grid grid-cols-2 gap-2';

                    return (
                      <div key={entryIndex} className="space-y-2">
                        <p className="font-medium capitalize">{t(`systemlogs.popup.type.${keyWithCapitalizedDna}`)}</p>
                        <div className={containerClass}>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.oldValue')}</p>
                            <div
                              className="border p-4 rounded-md bg-gray-50"
                              dangerouslySetInnerHTML={{ __html: values?.old || '' }}
                            />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.chanages')}</p>
                            <TextDiffHighlighter originalText={values?.old || ''} modifiedText={values?.new || ''} />
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                })}
              </div>
            );
          }
          return null;
        })}
      </div>
    </Modal>
  );
};

export default LogsData;
