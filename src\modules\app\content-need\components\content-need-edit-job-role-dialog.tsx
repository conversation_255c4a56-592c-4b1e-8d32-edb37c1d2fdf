import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useEditContentNeedRole } from '@/modules/app/content-need/apis/queries';

const EditRoleDialog = ({
  isOpen,
  setIsOpen,
  roleBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  roleBeingEdited: { id: number; title: string };
}) => {
  const { t } = useTranslation();
  const { form, setFieldValue } = useForm({
    title: roleBeingEdited?.title || '',
    id: roleBeingEdited?.id || 0,
  });
  const { mutate: editRole, isPending } = useEditContentNeedRole();
  const handleSubmit = () => {
    editRole(form, { onSuccess: () => setIsOpen(false) });
  };
  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('editRole.title')}>
      <Form onSubmit={handleSubmit} className="flex flex-col">
        <TextInput name="role" label={t('editRole.role')} value={form.title} onChange={setFieldValue('title')} />
        <Button loading={isPending} disabled={isPending} type="submit" className="mt-4 ml-auto w-fit">
          {t('save')}
        </Button>
      </Form>
    </Modal>
  );
};

export default EditRoleDialog;
