import { useState } from 'react';
import { Form, Modal, useForm, TextInput, ComboboxInput, useValidate } from '@/index';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

const RewriteContentDialog = ({ onOpen, onClose, details }: any) => {
  // State
  const [loading, setLoading] = useState(false);

  // Hooks
  const { t, i18n } = useTranslation();
  const { data: subjectOptions } = useGetSingleMetadata('subject');
  const { data: audienceOptions } = useGetSingleMetadata('audience');
  const { data: languageOptions } = useGetSingleMetadata('language');
  const { data: blooms_taxonomyOptions } = useGetSingleMetadata('bloom"s_taxonomy');
  const { isRequired } = useValidate();

  const { form, setFieldValue } = useForm({
    dna: 0,
    audience: 0,
    style: 0,
    pre_knowledge_id: 0,
    language_id: 0,
  });

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  // Functions
  // const handelActifity = async () => {
  //   try {
  //     setLoading(true);
  //     const response = await Api.post(GENERATE_ACTIVITY, form);
  //     onFinsh(response.data.data);
  //     onClose();
  //     notify.success('Activity Created!');
  //   } catch (error: any) {
  //     notify.error(error.message);
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  return (
    <Modal open={onOpen} onOpenChange={onClose} modalHeader={'Rewrite Content'} width={'1200'}>
      <Form>
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <ComboboxInput
            name="subject"
            label={t('dnaCreationPage.form.subject')}
            placeholder={t('dnaCreationPage.form.subject')}
            options={generateEnum(subjectOptions || [], 'id', labelKey)}
            value={details.subject_id}
            onChange={setFieldValue('subject_id')}
            dropIcon
            validators={[isRequired()]}
          />
          {form.subject === 'Other (write a subject)' && (
            <TextInput
              name="otherSubject"
              label={t('dnaCreationPage.form.otherSubject')}
              placeholder={t('dnaCreationPage.form.otherSubject')}
              value={details.other_subject}
              onChange={setFieldValue('other_subject')}
              isRequired
              validators={[isRequired()]}
            />
          )}
          <ComboboxInput
            name="audience"
            label={t('dnaCreationPage.form.audience')}
            placeholder={t('dnaCreationPage.form.audience')}
            options={generateEnum(audienceOptions || [], 'id', labelKey)}
            value={details.audience_id}
            onChange={setFieldValue('audience_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="bloom'sTaxonomy"
            label={t("dnaCreationPage.form.bloom'sTaxonomy")}
            placeholder={t("dnaCreationPage.form.bloom'sTaxonomy")}
            options={generateEnum(blooms_taxonomyOptions || [], 'id', labelKey)}
            value={details.bloom_tax_id}
            onChange={setFieldValue('bloom_tax_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="language"
            label={t('dnaCreationPage.form.language')}
            placeholder={t('dnaCreationPage.form.language')}
            options={generateEnum(languageOptions || [], 'id', labelKey)}
            value={details.language_id}
            onChange={setFieldValue('language_id')}
            dropIcon
            validators={[isRequired()]}
          />
        </div>
        <div className="flex justify-end gap-3 mt-3">
          <Button onClick={() => onClose(false)} disabled={loading} variant={'outline'} className="min-w-[100px] mt-4">
            {'Cancel'}
          </Button>
          <Button loading={loading} disabled={loading} className="min-w-[100px] mt-4">
            {'Create'}
          </Button>
        </div>
      </Form>
      <div className="flex gap-3">
        <p>{details.dna}</p>
        <p>{details.dna}</p>
      </div>
    </Modal>
  );
};

export default RewriteContentDialog;
