import { lazy } from 'react';
import { TermsMainLayout } from './layout';

const TermsAndConditions = lazy(() => import('./pages/terms-and-conditions'));

export default [
  {
    path: 'terms',
    element: <TermsMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.termsPage.terms',
        title: 'breadcrumb.termsPage.terms',
      };
    },
    children: [
      // Default
      {
        path: '',
        element: <TermsAndConditions />,
      },
    ],
  },
];
