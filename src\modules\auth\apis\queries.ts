import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  login,
  getUser,
  logout,
  signUp,
  reSendEmail,
  resetPassword,
  forgetPassword,
  getUserStatistics,
} from './endpoints';
import { useNotify } from '@/index';
import { useAtomValue, useSetAtom } from 'jotai';
import { notVerifiedUserAtom, userAtom } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';

interface ILoginPayload {
  email: string;
  password: string;
}

// Hook to fetch user based on token in the local storage without cashing
export const useGetUser = () => {
  const token = localStorage.getItem('token');
  const user = useAtomValue(userAtom);

  return useQuery({
    queryKey: ['user'],
    queryFn: getUser,
    staleTime: 0,
    gcTime: 0,
    retry: 1,
    enabled: !!token,
  });
};
export const useGetUserStatistics = () => {
  const token = localStorage.getItem('token');
  const user = useAtomValue(userAtom);
  return useQuery({
    queryKey: ['user-statistics'],
    queryFn: getUserStatistics,
    enabled: !!token,
  });
};

// Hook to login
export const useLogin = () => {
  const navigate = useLanguageNavigate();
  const { notify } = useNotify();
  const { t } = useTranslation();
  const setUser = useSetAtom(userAtom);
  const setNotVerifiedUser = useSetAtom(notVerifiedUserAtom);
  return useMutation({
    mutationFn: (payload: ILoginPayload) => login(payload),
    onSuccess: (data) => {
      const {
        access_token,
        user,
        user: { account_status, email_verified_at, email },
      } = data;
      // checking if the account is approved or not until the backend fixes it
      if (account_status == 'pending') {
        return;
      }
      if (account_status == 'blocked') {
        return;
      }
      if (email_verified_at === null) {
        setNotVerifiedUser(user);
        localStorage.setItem('not_verified_token', access_token);
        return;
      }
      // setting token in the local storage
      localStorage.setItem('token', access_token);
      if (user.roles.find((role) => role.name == 'super_admin')) {
        localStorage.setItem('cross_validation', 'true');
      } else {
        localStorage.setItem('cross_validation', 'false');
      }
      // setting user in the store
      setUser(user);
      notify.success(t('notify.login'));
      // redirecting to dashboard
      // navigate('/app');
    },
    // onError: (error: any) => {
    //   notify.error(error?.response_message || 'Something went wrong');
    // },
  });
};

export const useLogout = () => {
  const navigate = useLanguageNavigate();
  const queryClient = useQueryClient();
  const setUser = useSetAtom(userAtom);
  const { notify } = useNotify();
  return useMutation({
    mutationFn: logout,
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['user'] });
      localStorage.removeItem('token');
      navigate(`/auth/login`);
      setUser(null);
    },
    onError: (error: any) => {
      notify.error(error?.message || 'Something went wrong');
    },
  });
};

export const useSignUp = () => {
  const { t } = useTranslation();
  const navigate = useLanguageNavigate();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: signUp,
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['user'] });
      notify.success(t('Signup successfully! Please check your email for vrefication link.'));
      navigate(`/auth/login`);
    },
    onError: (error: any) => {
      if (error.message === 'The email has already been taken.') {
        notify.error(t('Email.alreadyExists'));
      } else {
        let errorMessage = '';
        for (const key in error.errors) {
          if (Array.isArray(error.errors[key])) {
            errorMessage = error.errors[key].join(', ');
            break;
          }
        }
        notify.error(t(errorMessage) || 'Something went wrong');
      }
    },
  });
};

export const useEmailVerificationResend = () => {
  const { t } = useTranslation();
  const navigate = useLanguageNavigate();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: reSendEmail,
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['user'] });
      notify('Email sent successfully');
      navigate(`/auth/login`);
    },
    onError: (error: any) => {
      if (error.message === 'You_Entered_Repeated_Email') {
        notify.error(t('Email.alreadyExists'));
      } else {
        notify.error(error?.message || 'Something went wrong');
      }
    },
  });
};

export const useForgetPassword = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: forgetPassword,
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['user'] });
      notify(t('notify.forgetPasswordSuccess'));
    },
    onError: (error: any) => {
      notify(
        error?.message === 'Failed to send reset link'
          ? t('notify.forgetPasswordError')
          : error?.message || 'Something went wrong'
      );
    },
  });
};

export const useResetPassword = () => {
  const navigate = useLanguageNavigate();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: resetPassword,
    onSuccess: () => {
      queryClient.resetQueries({ queryKey: ['user'] });
      notify.success(t('notify.resetPasswordSuccess'));
      navigate(`/auth/login`);
    },
    onError: (error: any) => {
      notify.error(error?.message || 'Something went wrong');
    },
  });
};
