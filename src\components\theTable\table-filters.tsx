import React, { memo } from 'react';
import { Combobox } from '../combo-box';
import { MultiSelect } from '@/components/ui/multi-select-combo-box';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { IFilter } from './types';
import { cn } from '@/lib/utils';
interface TableFiltersProps {
  filters: IFilter[];
  className?: string;
}

const TableFilters: React.FC<TableFiltersProps> = ({ filters, className }) => {
  const { t } = useTranslation();
  return (
    <div className={cn('flex gap-3 items-center', className)}>
      {filters.map((filter) => {
        if (filter.type === 'multi') {
          return (
            <div key={filter.key} className="min-w-[230px] mb-2">
              <MultiSelect
                name={filter.key}
                placeholder={t(filter.placeholder)}
                options={filter.options}
                value={filter.selectedValue || []}
                onChange={filter.onChange}
              />
            </div>
          );
        }
        return (
          <Combobox
            key={filter.key}
            placeholder={t(filter.placeholder)}
            options={filter.options}
            value={filter.selectedValue}
            onChange={filter.onChange}
          />
        );
      })}
      {filters?.length > 0 && (
        <Button
          className="self-start sm:self-center rounded-full border-primary dark:border-border px-4"
          size={'sm'}
          variant={'outline'}
          onClick={() =>
            filters.map((filter) => {
              filter.reset();
            })
          }
        >
          {t('userPage.filters.reset')}
        </Button>
      )}
    </div>
  );
};

export default memo(TableFilters);
