import { lazy } from 'react';
import { Navigate } from 'react-router-dom';
import MetaDataRoutes from './metadata/routes';
import TranslationsRoutes from './transaltion/routes';
import OperationsRoutes from './operation/routes';
import ModuleStatusRoutes from './modules-status/routes';
import SystemLogsRoutes from './system-logs/routes';

const DashboardMainLayout = lazy(() => import('./layout').then((module) => ({ default: module.DashboardMainLayout })));

export default [
  {
    path: 'dashboard',
    element: <DashboardMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.dashboard',
        title: 'breadcrumb.dashboard',
      };
    },
    children: [
      // Default
      {
        path: '',
        element: <Navigate to="keys" />,
      },
      ...MetaDataRoutes,
      ...TranslationsRoutes,
      ...OperationsRoutes,
      ...ModuleStatusRoutes,
      ...SystemLogsRoutes,
    ],
  },
];
