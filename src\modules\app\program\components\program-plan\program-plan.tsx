import LearningOutcomeTable from './learning-outcome/learning-outcome-table';
import SkillsTable from './skills/skills-table';
import CourseTable from './courses/courses-table';
import { Button } from '@/components/ui/button';
import { useAtom } from 'jotai';
import { programPlan<PERSON>tom } from '../../store';
import { useProgramStepTwo } from '../../apis/queries';

const ProgramPlanStepPage = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  const [data] = useAtom(programPlanAtom);

  const { mutate: stepTwo, isPending: pending } = useProgramStepTwo();

  return (
    <div className="space-y-8">
      <LearningOutcomeTable />
      <SkillsTable list={data.program_skills} />
      <CourseTable list={data.courses} />
      <div className="flex justify-center">
        <Button
          disabled={pending}
          loading={pending}
          onClick={() => stepTwo(data.id, { onSuccess: () => moveToNextStep() })}
        >
          Next
        </Button>
      </div>
    </div>
  );
};

export default ProgramPlanStepPage;
