import React, { createContext, forwardRef } from 'react';
import { useImmer as useState } from 'use-immer';

// Inputs
export * from './inputs';

export const FormContext = createContext({});

export const Form = forwardRef(({ children, onSubmit, onKeyDown, ...props }: any, ref) => {
  const [fields, setFields] = useState({});

  // Provider
  const provider = {
    // Methods
    registerField(name: any, field: any) {
      setFields((draft) => {
        (draft as any)[name] = field;
      });
    },
  };

  // Methods
  const validateAllFields = () => {
    const result = Object.values(fields).map((field: any) => {
      return field.runValidations();
    });
    return result.every((isValid) => isValid);
  };

  const handleSubmit = (e: any) => {
    e.preventDefault();
    const isAllFieldsValid = validateAllFields();

    // Make sure that all validations passed
    if (isAllFieldsValid) {
      onSubmit(e);
    }
  };

  // const handleKeyDown = (e: React.KeyboardEvent) => {
  //   // Handle custom onKeyDown if provided

  //   // If Enter is pressed and the target is an input field
  //   if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
  //     handleSubmit(e as any);
  //   }
  // };

  return (
    <FormContext.Provider value={provider}>
      <form ref={ref} onSubmit={handleSubmit} {...props}>
        {children}
      </form>
    </FormContext.Provider>
  );
});

// Default props
Form.defaultProps = {
  className: '',
  onSubmit() {},
  onKeyDown: undefined,
};
