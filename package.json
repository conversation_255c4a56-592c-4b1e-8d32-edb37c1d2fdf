{"name": "login-page", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build:analyze": "vite build --mode analyze", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "serve": "serve dist", "knip": "knip"}, "dependencies": {"@ckeditor/ckeditor5-react": "^9.4.0", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-direction": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.51.23", "@tanstack/react-query-devtools": "^5.51.23", "@webspellchecker/wproofreader-ckeditor5": "^3.1.2", "axios": "^1.6.7", "ckeditor5": "^44.1.0", "ckeditor5-build-classic-mathtype": "^1.0.0", "ckeditor5-premium-features": "^44.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "date-fns": "^3.6.0", "diff": "^7.0.0", "docx": "^8.5.0", "dompurify": "^3.1.4", "file-saver": "^2.0.5", "html-docx-js": "^0.3.1", "html-to-docx": "^1.8.0", "i18next": "^23.14.0", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "iconify-icon": "^1.0.8", "jotai": "^2.9.3", "js-cookie": "^3.0.5", "jsrsasign": "^11.1.0", "laravel-echo": "^2.0.2", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "lucide-react": "^0.424.0", "marked": "^12.0.2", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "next-themes": "^0.4.3", "object-path": "^0.11.8", "pusher-js": "^8.4.0", "query-string": "^9.1.0", "react": "^18.2.0", "react-click-away-listener": "^2.2.3", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-h5-audio-player": "^3.9.3", "react-i18next": "^15.0.1", "react-intersection-observer": "^9.13.1", "react-resizable-panels": "^2.1.2", "react-router-dom": "^6.22.3", "react-toastify": "^10.0.4", "recharts": "^2.15.3", "remark": "^15.0.1", "remark-parse": "^11.0.0", "sonner": "^1.7.0", "swiper": "^11.1.10", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "unified": "^11.0.5", "use-immer": "^0.10.0", "xlsx": "^0.18.5"}, "devDependencies": {"@iconify/react": "^5.0.2", "@types/crypto-js": "^4.2.2", "@types/diff": "^7.0.1", "@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/html-docx-js": "^0.3.4", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.7", "@types/node": "^22.15.32", "@types/object-path": "^0.11.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/reveal.js": "^5.0.3", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^8.0.1", "@typescript-eslint/parser": "^8.0.1", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "knip": "^5.61.2", "postcss": "^8.4.35", "postcss-import": "^16.1.0", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^4.4.0"}}