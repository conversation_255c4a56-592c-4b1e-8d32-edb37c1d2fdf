import axios from 'axios';
import globalHooks from './global-router';
const VITE_API_BASE_URL = (import.meta as any).env.VITE_API_BASE_URL;
const api = axios.create({
  baseURL: VITE_API_BASE_URL,
});

api.interceptors.request.use(
  (config) => {
    const access_token = localStorage.getItem('token');
    if (access_token) {
      config.headers.Authorization = `Bearer ${access_token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => response,
  (error) => {
    const status = error?.response?.status;
    if (status === 401 && !window.location.pathname.includes('/auth')) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      if (globalHooks?.globalRouter?.navigate) {
        const lng = localStorage.getItem('i18nextLng') || 'en';
        globalHooks?.globalRouter?.navigate(`/${lng}/auth/login`);
      }
    }
    if (status === 403) {
      if (globalHooks?.globalRouter?.navigate && !window.location.pathname.includes('/403')) {
        globalHooks.globalNotify('You are not authorized to access this resource.');
        globalHooks?.globalRouter?.navigate('/403', { replace: true });
      }
    }
    return Promise.reject(error.response.data);
  }
);

export { api };
