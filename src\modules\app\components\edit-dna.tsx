import { useState } from 'react';
import { Modal, ProtectedComponent, useConfirmDialog, Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { saveAs } from 'file-saver';
import { Document, Packer } from 'docx';
import TurndownService from 'turndown';
import { parseMarkdownToDocx } from '@/utils/helpers';
import { IDNA } from '../dna/types';
import { useSaveDNA } from '../dna/apis/queries';
import { Editor } from '@/components/CKEditor';
interface Props {
  onOpen: boolean;
  onClose: () => void;
  data: IDNA;
}

const EditDna = ({ onOpen, onClose, data }: Props) => {
  const { showConfirm, hideConfirm } = useConfirmDialog();
  //  State
  const [dnaText, setDnaText] = useState(data.dna_content);
  // Hooks
  const { t, i18n } = useTranslation();
  const { mutate, isPending } = useSaveDNA();
  const queryClient = useQueryClient();
  // Edit Dna
  const handleEdit = async () => {
    mutate(
      { id: data.id, dna: dnaText },
      {
        onSuccess: (res) => {
          queryClient.invalidateQueries();
          onClose();
        },
      }
    );
  };

  const handleEditWithConfirm = async () => {
    showConfirm(ConfirmSaveText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        mutate(
          { id: data.id, dna: dnaText },
          {
            onSuccess: (res) => {
              queryClient.invalidateQueries();
              onClose();
            },
          }
        );
      },
    });
  };
  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationEdit')}</p>
      </div>
    );
  };

  const ConfirmSaveText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>Changing the DNA will update the current status to 'Draft.' Are you sure you want to proceed?</p>
      </div>
    );
  };

  const handleClose = async () => {
    showConfirm(data?.dna_status?.phase_with_status === 'Edit: Ready for Review' ? ConfirmSaveText() : ConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        onClose();
      },
    });
  };
  const downloadWordDocument = (editorContent: string, filename: string, language: string) => {
    const turndownService = new TurndownService();
    // Convert HTML to Markdown
    const markdownContent = turndownService.turndown(editorContent);

    // Determine text direction based on the language
    const isRTL = ['ar', 'he', 'fa', 'ur'].includes(language); // Add RTL languages as needed
    const textAlignment = isRTL ? 'right' : 'left';
    const bidi = isRTL; // Bi-directional text setting for RTL

    // Parse Markdown to docx elements
    const docElements = parseMarkdownToDocx(markdownContent).map((element: any) => {
      if (element.type === 'paragraph') {
        return {
          ...element,
          style: {
            alignment: textAlignment,
            bidi,
          },
        };
      }
      return element;
    });

    // Create a new Word document
    const doc = new Document({
      sections: [
        {
          children: docElements,
        },
      ],
    });

    // Convert the document to a Blob and save it
    Packer.toBlob(doc).then((blob) => {
      saveAs(blob, `${filename}.docx`);
    });
  };

  return (
    <Modal
      width={1200}
      open={onOpen}
      onOpenChange={dnaText === data.dna_content ? onClose : handleClose}
      modalHeader={t('editDnaModal.title')}
      isModal={false}
    >
      <div>
        <div className="mb-4">
          <div className="flex justify-between">
            <div className=" self-center">
              <p className="text-xl">{data.title}</p>
              <p className=" text-sm opacity-[0.5]">{data.learning_objectives}</p>
            </div>
            <div className="flex gap-2">
              <div className="text-[#4B5563] text-sm self-center">
                <p>
                  {data.word_count} {t('editDnaModal.word')}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Editor editorContent={dnaText} setEditorContent={setDnaText} language={data.language} />
          <div className="mt-6 flex flex-row-reverse gap-2">
            <Button
              variant={'outline'}
              className="min-w-[120px] text-gray-900"
              onClick={
                data?.dna_status?.phase_with_status === 'Edit: ready_for_review'
                  ? () => handleEditWithConfirm()
                  : () => handleEdit()
              }
              loading={isPending}
              disabled={isPending || dnaText === data.dna_content}
            >
              {t('editDnaModal.saveDna')}
            </Button>
            <ProtectedComponent requiredPermissions={'dna_download'}>
              <Button variant={'ghost'} onClick={() => downloadWordDocument(dnaText, data.title, i18n.language)}>
                <Icon icon="material-symbols:download-rounded" width={22} />
              </Button>
            </ProtectedComponent>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditDna;
