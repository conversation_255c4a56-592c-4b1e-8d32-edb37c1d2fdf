import EmptyAnalysis from './empty-state';
import { Card } from '@/components';
import { useTranslation } from 'react-i18next';
import { IDNA } from '../../types';
import { faultyValue } from '@/utils/helpers';
import PromptViewerDialog from '@/modules/app/components/prompt-viewer-dialog';

const MediaAnalysis = ({ data }: { data: IDNA }) => {
  const { t } = useTranslation();

  const mediaAnalysisData = [
    {
      label: t(`mediaAnalysis.model`),
      value: data.media_analysis.model,
    },
    {
      label: t(`mediaAnalysis.mandatory`),
      value: t(`mediaAnalysis.mandatory.${data.media_analysis.mandatory}`),
    },
    {
      label: t(`mediaAnalysis.contentDeliveryType`),
      value: data.media_analysis.content_delivery_type,
    },
    {
      label: t(`mediaAnalysis.prompt`),
      value: <PromptViewerDialog model={data.media_analysis.model} prompt={data.media_analysis.prompt} />,
    },
  ];

  if (faultyValue(data.media_analysis)) return <EmptyAnalysis />;
  return (
    <div>
      <div className="p-4 mb-5">
        <div className="flex flex-wrap gap-2 space-x-2 divide-x-2">
          {mediaAnalysisData.map((item, index) => (
            <div key={index} className="flex items-center gap-2 px-4">
              <span className="text-sm font-medium">{item.label}:</span>
              <span className="text-sm text-gray-500">{item.value}</span>
            </div>
          ))}
        </div>
      </div>
      <Card className="p-4 mb-5">
        <p className="pb-2 font-medium text-md">{t('mediaAnalysis.justification')}:</p>
        <p className="text-sm text-gray-800 dark:text-white">{data.media_analysis.justification}</p>
      </Card>
    </div>
  );
};

export default MediaAnalysis;
