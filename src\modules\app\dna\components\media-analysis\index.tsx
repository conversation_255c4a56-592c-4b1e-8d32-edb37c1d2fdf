import EmptyAnalysis from './empty-state';
import { Card, Icon } from '@/components';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { IDNA } from '../../types';
import PromptViewerDialog from '@/modules/app/components/prompt-viewer-dialog';

interface ArticulateMediaAnalysisProps {
  data?: IDNA;
  isPending: boolean;
}

const MediaAnalysis = ({ data, isPending }: ArticulateMediaAnalysisProps) => {
  const { t } = useTranslation();
  const { dnaId } = useParams();

  if (isPending)
    return (
      <div>
        <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
      </div>
    );
  if (true)
    return (
      <div>
        <EmptyAnalysis dnaId={dnaId ? Number(dnaId) : 0} />
      </div>
    );
  return (
    <div>
      <h2 className="mb-3 font-medium">{t('mediaAnalysis.title')}</h2>
      <div className="p-4 mb-5">
        <div className="flex space-x-2 divide-x-2">
          <div className="flex items-center gap-2 px-4">
            <span className="text-sm font-medium">{t('mediaAnalysis.model')}:</span>
            <span className="text-sm text-gray-500">{data.media_analysis.model}</span>
          </div>
          <div className="flex items-center gap-2 px-4">
            <span className="text-sm font-medium">{t('mediaAnalysis.contentDeliveryType')}:</span>
            <span className="text-sm text-gray-500">{data.media_analysis.content_delivery_type}</span>
          </div>
          <div className="flex items-center gap-2 px-4">
            <span className="text-sm font-medium">{t('mediaAnalysis.mandatory')}:</span>
            <span className="text-sm text-gray-500">{data.media_analysis.mandatory}</span>
          </div>
          <div className="flex items-center gap-2 px-4">
            <span className="text-sm font-medium">{t('mediaAnalysis.prompt')}:</span>
            <span className="text-sm text-gray-500">
              <PromptViewerDialog model={data.media_analysis.model} prompt={data.media_analysis.prompt} />
            </span>
          </div>
        </div>
      </div>
      <Card className="p-4 mb-5">
        <p className="text-sm font-medium">{t('mediaAnalysis.justification')}:</p>
        <p className="text-gray-800 dark:text-white text-md">{data.media_analysis.justification}</p>
      </Card>
    </div>
  );
};

export default MediaAnalysis;
