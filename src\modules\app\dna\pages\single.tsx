import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Card, Icon, useNotify } from '@/index';
import { Editor } from 'src/components/CKEditor';
import MetaData from '../components/meta-data';
import Activity from '@/modules/app/components/activity/view-only-activity';
import {
  useApproveOrFeedbackDna,
  useChangeDnaStatus,
  useGenarateDna,
  useGetDnaById,
  useUpdateDnaData,
} from '@/modules/app/dna/apis/queries';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { ProtectedComponent } from '@/index';
import NotFound from '@/NotFoundPage';
import { useAddChecklist } from '@/apis/tool-checklist/queries';
import AddChecklistDialog from '@/modules/app/components/add-checklist-dialog';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import TranslationTabView from '../../components/localization/transaltion-view';
import { getContentStatusStyle } from '@/utils/helpers';
import { Separator } from '@/components/ui/separator';
import SlideshowList from '../components/slideshow/slideshow-list';
import Operations from '../components/operations/operation';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import DnaNavigation from '../components/dna-navigation';
import TranslationContentTabs from '../../components/translation-content-tabs';
import WorkflowStatusChecklist from '../../components/work-flow-status-checklist';
import ChangeDnaData from '../components/change-title-dialog';
import AIMagicDialog from '../components/edit-dna';
import EditDna from '../../components/edit-dna';
import ArticulateAnalysis from '../components/analysis';
import Tabs from '@/components/tabs';
import UniversalSkeleton from '@/components/universal-skeleton';
import { useHasPermission } from '@/modules/auth/store';

// Interfaces
interface ITab {
  label: string;
  value: string;
  permission: string | string[];
  subText?: boolean;
  blocked?: boolean;
  component?: JSX.Element;
}
const SingleDna = () => {
  //states
  const [open, setOpen] = useState(false);
  const [editDialog, setEditDialog] = useState<any>(false);
  const [editUsingAiDialog, setEditUsingAiDialog] = useState<boolean>(false);

  const [editTitle, setEditTitle] = useState(false);
  const [dnaTitle, setDnaTitle] = useState('');
  const [ids, setIds] = useState<number[]>([]);
  const [changeDnaData, setChangeDnaData] = useState(false);

  //hooks
  const { dnaId } = useParams();
  const { t } = useTranslation();
  const { data: dna, error, isPending } = useGetDnaById(Number(dnaId));
  const { mutate: generate, isPending: isGeneratingDNA } = useGenarateDna();
  const { mutate: addChecklist, isPending: isPendingChecklist } = useAddChecklist();
  const { mutate: mutateDnaStatus, isPending: isUpdatingStatus } = useChangeDnaStatus();
  const { mutate: approveDna, isPending: isApproving } = useApproveOrFeedbackDna();
  const { mutate: feedbackDna, isPending: isFeedingback } = useApproveOrFeedbackDna();
  const { mutate: changeStatus, isPending: loading } = useUpdateDnaData();
  const [selectedContentLanguage, setSelectedContentLanguage] = useState<IMetadata>();
  const [selectedContent, setSelectedContent] = useState<string>('');
  const { notify } = useNotify();

  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    if (!dnaId) return false;
    const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
    return savedState ? JSON.parse(savedState) : false;
  });

  useEffect(() => {
    if (dna) {
      setDnaTitle(dna?.title);
      setSelectedContentLanguage(dna.language);
      setSelectedContent(dna.dna_content);
      const allDnaIds = dna?.topic?.course?.all_dnas_ids || dna?.topic?.all_dnas_ids || [];
      setIds(allDnaIds);

      if (dnaId) {
        const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
        setIsExpanded(savedState ? JSON.parse(savedState) : false);
      }
    }
    if (dna && !dna.dna_content) {
      notify.info(
        'No content has been generated for this DNA yet. Please create the content before accessing this page.'
      );
    }
  }, [dna, dnaId]);

  useEffect(() => {
    if (dnaId) {
      localStorage.setItem(`dnaContentExpanded-${dnaId}`, JSON.stringify(isExpanded));
    }
  }, [isExpanded, dnaId]);

  const handelUpdate = () => {
    changeStatus(
      {
        id: dna?.id,
        payload: {
          title: dnaTitle,
        },
      },
      {
        onSuccess: () => {
          setEditTitle(false);
        },
      }
    );
  };

  // Tabs States
  const activeTabComponent: Record<string, JSX.Element> = {
    'meta-data': (
      <ProtectedComponent
        requiredPermissions={['dna_main_data', 'dna_sub_data', 'dna_resource_data']}
        requireAll={false}
      >
        {dna && <MetaData details={dna} />}
      </ProtectedComponent>
    ),
    activity: (
      <ProtectedComponent requiredPermissions="dna_activity_show">
        <Activity details={dna as any} />
      </ProtectedComponent>
    ),
    slideshow: (
      <ProtectedComponent requiredPermissions="dna_slideshow_show">
        <SlideshowList />
      </ProtectedComponent>
    ),
    localization: (
      <ProtectedComponent requiredPermissions="translation_show">
        <TranslationTabView details={dna} />
      </ProtectedComponent>
    ),
    operations: <Operations />,
    articulateAnalysis: (
      <ProtectedComponent requiredPermissions="dna_analysis_show">
        <ArticulateAnalysis />
      </ProtectedComponent>
    ),
  };

  const isBlocked = () => {
    return dna?.status === 'Blocked';
  };

  // Create tabs array with components
  const theTabs: ITab[] = [
    {
      label: t('dnaSinglePage.tabs.metaData'),
      value: 'meta-data',
      permission: ['dna_main_data', 'dna_sub_data', 'dna_resource_data'],
      blocked: false,
      component: activeTabComponent['meta-data'],
    },
    {
      label: t('dnaSinglePage.tabs.activity'),
      value: 'activity',
      permission: 'dna_activity_show',
      blocked: isBlocked(),
      component: activeTabComponent['activity'],
    },
    {
      label: t('dnaSinglePage.tabs.slideShow'),
      value: 'slideshow',
      permission: 'dna_slideshow_show',
      blocked: isBlocked(),
      component: activeTabComponent['slideshow'],
    },
    {
      label: t('dnaSinglePage.tabs.Localization'),
      value: 'localization',
      permission: 'translation_show',
      blocked: isBlocked(),
      component: activeTabComponent['localization'],
    },
    {
      label: t('dnaSinglePage.tabs.Operations'),
      value: 'operations',
      permission: 'show_log',
      blocked: isBlocked(),
      component: activeTabComponent['operations'],
    },
    {
      label: t('dnaSinglePage.tabs.articulateAnalysis'),
      value: 'articulateAnalysis',
      permission: 'dna_analysis_show',
      blocked: isBlocked(),
      component: activeTabComponent['articulateAnalysis'],
    },
    {
      label: t('dnaSinglePage.tabs.reading'),
      value: 'reading',
      subText: true,
      permission: 'for_administration',
      blocked: isBlocked(),
    },
    {
      label: t('dnaSinglePage.tabs.podcast'),
      value: 'podcast',
      subText: true,
      permission: 'for_administration',
      blocked: isBlocked(),
    },
    {
      label: t('dnaSinglePage.tabs.video'),
      value: 'video',
      subText: true,
      permission: 'for_administration',
      blocked: isBlocked(),
    },
  ];

  // Filter out blocked tabs
  const filteredTabs = theTabs.filter((tab) => {
    if (tab.blocked) return false;

    if (Array.isArray(tab.permission)) {
      const requireAll = tab.value === 'meta-data' ? false : true;
      return useHasPermission(tab.permission, requireAll);
    } else {
      return useHasPermission(tab.permission);
    }
  });

  const defaultTab = filteredTabs.length > 0 ? filteredTabs[0].value : 'meta-data';

  if (isPending) {
    return <UniversalSkeleton preset="userList" height="100%" rows={3} />;
  }

  if ((error && error?.message === 'Not_Found') || error?.message === 'Model Not Found') {
    return <NotFound />;
  }

  const toggleHeight = () => setIsExpanded((prev: any) => !prev);

  const handleReadyForReview = (checklist: (string | number)[]) => {
    if (dna) {
      mutateDnaStatus(dna.id, {
        onSuccess: () => {
          addChecklist(
            { check_list_ids: checklist, tool_id: dna.id, tool_type: 'Dna' },
            {
              onSuccess: () => {
                setOpen(false);
              },
            }
          );
        },
      });
    }
  };

  const handleApprove = () => {
    if (dna) {
      approveDna({ id: dna.id, status: 'approved' });
    }
  };

  const handleFeedback = () => {
    if (dna) {
      feedbackDna({ id: dna.id, status: 'feedback' });
    }
  };

  if (!dna?.dna_content) {
    return (
      <div>
        <DnaNavigation ids={ids} />
        <div className="flex flex-col justify-center items-center ms-2 min-h-[calc(100dvh-150px)]">
          <img src="/empty-activity.svg" alt="" />
          <div className="flex flex-col gap-2 my-5">
            <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.dna.noDnaContentTitle')}</h1>
            <p className="self-center text-slate-500">{t('dnaSinglePage.noDnaContentDescription')}</p>
          </div>
          <div>
            <ConditionalComponent status={dna?.dna_status} wantedStatus={StatusClass.DNA.EDIT.NO_CONTENT}>
              <Button
                onClick={() => {
                  generate(dna?.id || '');
                }}
                loading={isGeneratingDNA}
                type="button"
                className="flex gap-2 items-center"
              >
                <Icon icon="mage:stars-a-fill" width={22} className="cursor-pointer" />
                {t('generate')}
              </Button>
            </ConditionalComponent>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="my-3 space-y-4">
      <Alert className="bg-blue-50 dark:bg-background text-indigo-700">
        <AlertTitle className="text-xl font-medium">
          <div className="flex items-center gap-1">
            <Icon className="mt-1 text-indigo-700" icon="mdi:alert-circle" width={18} />
            <p>{t('dnaSinglePage.dnaAlertTitle')}</p>
          </div>
        </AlertTitle>
        <AlertDescription className="text-sm ml-2">• {t('dnaSinglePage.SinglePage.dnaAlert')}</AlertDescription>
      </Alert>
      <Card className="p-6">
        <div className=" mb-2">
          <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-3  border-b pb-5">
            <div className="space-y-2 ">
              {getContentStatusStyle(dna?.dna_status)}
              <div className="flex gap-3 mb-5 items-center">
                <h3 className="text-xl capitalize ">{dna?.title}</h3>
                <ConditionalComponent
                  status={dna?.dna_status}
                  wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK, StatusClass.DNA.EDIT.RFR]}
                >
                  <ConditionalComponent
                    status={dna?.topic?.course?.course_status}
                    wantedStatus={[StatusClass.COURSE.EDIT.RFR, StatusClass.COURSE.EDIT.APPROVED]}
                    operator="not"
                  >
                    <Icon
                      onClick={() => setChangeDnaData(true)}
                      icon="mynaui:edit-one"
                      width={22}
                      className="cursor-pointer text-primary mt-2"
                    />
                  </ConditionalComponent>
                </ConditionalComponent>
              </div>
              <p className="text-black-600">{dna?.learning_objectives}</p>
              {['review', 'production'].includes(dna?.dna_status?.phase?.action?.toLowerCase()) && (
                <WorkflowStatusChecklist dna={dna} needsPermissions={false} />
              )}
            </div>
            <div className="space-y-3 self-center">
              <DnaNavigation ids={ids} />
              <div className="self-center justify-end flex flex-wrap gap-2 order-1 sm:order-2">
                <ConditionalComponent status={dna?.dna_status} wantedStatus={[StatusClass.DNA.EDIT.RFR]}>
                  <ConditionalComponent
                    status={dna?.topic?.course?.course_status}
                    wantedStatus={[StatusClass.COURSE.EDIT.RFR, StatusClass.COURSE.EDIT.FEEDBACK]}
                  >
                    <ProtectedComponent requiredPermissions="dna_approve_or_feedback">
                      <Button
                        className="min-w-[140px]"
                        onClick={handleFeedback}
                        disabled={dna?.dna_status.phase_with_status === StatusClass.DNA.EDIT.FEEDBACK}
                        loading={isFeedingback}
                      >
                        {t('feedback')}
                      </Button>
                      <Button
                        className="flex items-center gap-1 min-w-[140px]"
                        loading={isApproving}
                        disabled={dna?.dna_status.phase_with_status === StatusClass.DNA.EDIT.APPROVED}
                        onClick={handleApprove}
                      >
                        {t('approve.edit.singleDNA')}
                      </Button>
                    </ProtectedComponent>
                  </ConditionalComponent>
                </ConditionalComponent>

                <ConditionalComponent status={dna?.dna_status} wantedStatus={StatusClass.DNA.EDIT.APPROVED}>
                  <Button disabled={true} className="flex gap-2 items-center">
                    {t('cousrePlanContentPage.approve')}
                    <Icon icon="solar:check-read-linear" width={25} className={'text-green-500'} />
                  </Button>
                </ConditionalComponent>

                <ConditionalComponent
                  status={dna?.dna_status}
                  wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK]}
                >
                  <Button
                    disabled={isUpdatingStatus}
                    loading={isUpdatingStatus}
                    className="flex gap-2 items-center w-[140px]"
                    onClick={() => setOpen(true)}
                  >
                    {t('editDnaModal.finish')}
                  </Button>
                </ConditionalComponent>
                <ConditionalComponent
                  status={dna?.dna_status}
                  wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.RFR, StatusClass.DNA.EDIT.FEEDBACK]}
                >
                  <ConditionalComponent
                    status={dna?.topic?.course?.course_status}
                    wantedStatus={[StatusClass.COURSE.EDIT.RFR, StatusClass.COURSE.EDIT.APPROVED]}
                    operator="not"
                  >
                    <ConditionalComponent
                      multiConditions={[
                        { status: dna?.dna_status, wantedStatus: [StatusClass.DNA.EDIT.RFR] },
                        { status: dna?.course_status, wantedStatus: [StatusClass.COURSE.EDIT.FEEDBACK] },
                      ]}
                      multiConditionOperator="allTogetherShouldNotPass"
                    >
                      <Button
                        className="min-w-[140px]"
                        variant={'outline'}
                        disabled={isUpdatingStatus}
                        onClick={() => setEditDialog(true)}
                      >
                        {t('editDnaModal.title')}
                      </Button>

                      {/* <Button
                        className="min-w-[140px]"
                        variant={'outline'}
                        disabled={isUpdatingStatus}
                        onClick={() => setEditUsingAiDialog(true)}
                      >
                        {t('editDnaModal.editUsingAi')}
                      </Button> */}
                    </ConditionalComponent>
                  </ConditionalComponent>
                </ConditionalComponent>

                {open && (
                  <AddChecklistDialog
                    open={open}
                    setOpen={setOpen}
                    callback={handleReadyForReview}
                    loading={isUpdatingStatus || isPendingChecklist}
                    type="ownerDna"
                  />
                )}
              </div>
            </div>
          </div>
          <Separator className="mt-4" />
          <TranslationContentTabs
            Dna={dna}
            setSelectedContent={setSelectedContent}
            setSelectedContentLanguage={setSelectedContentLanguage}
          />
          <div className="py-4">
            <div
              className={`
        overflow-y-hidden
        relative
        transition-all
        duration-500
        ease-in-out
        ${isExpanded ? 'max-h-[15000px]' : ' max-h-[350px]'}
        [&_.ck-toolbar]:hidden
      `}
            >
              <Editor
                height={'auto'}
                editorContent={selectedContent}
                setEditorContent={setSelectedContent}
                readOnly
                language={selectedContentLanguage}
              />
              {!isExpanded && (
                <div className="absolute bottom-[0px] h-[50px] left-0 right-0 bg-gradient-to-t from-background">
                  <span className="block">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                </div>
              )}
            </div>
            <div className="flex gap-3 justify-center items-center mt-2 text-primary">
              <Icon className="mt-1" icon={isExpanded ? 'ri:arrow-up-s-line' : 'ri:arrow-down-s-line'} width={22} />
              <p
                onClick={toggleHeight}
                className=" cursor-pointer"
                aria-expanded={isExpanded}
                role="button"
                tabIndex={0}
              >
                {isExpanded ? t('dnaSinglePage.hide') : t('dnaSinglePage.expand')}
              </p>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-wrap gap-2 p-3">
          <Tabs tabs={filteredTabs} defaultTab={defaultTab} useUrlParams={true} paramName="tab" />
        </div>
      </Card>

      {editDialog && dna && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false);
          }}
          data={dna || ''}
        />
      )}
      {editUsingAiDialog && dna && (
        <AIMagicDialog
          onOpen={editUsingAiDialog}
          onClose={() => {
            setEditUsingAiDialog(false);
          }}
          data={dna || ''}
        />
      )}

      {changeDnaData && (
        <ChangeDnaData
          data={dna}
          onOpen={changeDnaData}
          onOpenChange={() => {
            setChangeDnaData(false);
          }}
        />
      )}
    </div>
  );
};

export default SingleDna;
