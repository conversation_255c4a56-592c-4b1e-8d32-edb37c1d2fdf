import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { ISystemModuleStatus, IPhase, IStatus } from '../types';
import { useGetModulesStatus } from '../apis/queries';
import EditStatusDialog from '../components/edit-status-dialog';
import EditPhaseDialog from '../components/edit-phase-dialog';
import { useAdminTablesTabs } from '@/modules/app/content-tabs';
import TableContentHeader from '@/components/theTable/table-content-header';

const MetaData = () => {
  const { t } = useTranslation();
  const { data, isPending } = useGetModulesStatus();
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState<boolean>(false);
  const [selectedStatus, setSelectedStatus] = useState<IStatus | null>(null);
  const [isEditPhaseDialogOpen, setIsEditPhaseDialogOpen] = useState<boolean>(false);
  const [selectedPhase, setSelectedPhase] = useState<IPhase | null>(null);
  const adminTableTabs = useAdminTablesTabs();

  const columns: ITableColumn<ISystemModuleStatus & IPhase & IStatus>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: 'Module',
        width: '150px',
        cell: ({ row, level }) => {
          const displayText = row.title || row.action;
          return (
            <div
              style={{
                backgroundColor: level === 2 ? 'inherit' : row?.background_color,
                color: level === 2 ? 'inherit' : row?.text_color,
              }}
              className={`text-start truncate px-4 py-1 rounded`}
            >
              {displayText}
            </div>
          );
        },
      },
      {
        accessorKey: 'name_ar',
        header: 'Name_ar',
        width: '130px',
        cell: ({ row, level }) => {
          const displayText = row.title || row.name_ar;
          return (
            <div
              style={{ backgroundColor: row?.background_color, color: row?.text_color }}
              className={`text-start truncate px-4 py-1 rounded`}
            >
              {displayText}
            </div>
          );
        },
      },
      {
        accessorKey: 'name_en',
        header: 'Name_en',
        width: '130px',
        cell: ({ row, level }) => {
          const displayText = row.title || row.name_en;
          return (
            <div
              style={{ backgroundColor: row?.background_color, color: row?.text_color }}
              className={`text-start truncate px-4 py-1 rounded`}
            >
              {displayText}
            </div>
          );
        },
      },
      {
        accessorKey: 'description',
        header: 'Description',
        width: '400px',
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '80px',
        cell: ({ row, level }) => {
          switch (level) {
            case 1:
              return (
                <div className="flex justify-start">
                  <Button
                    variant="ghost"
                    size={'icon'}
                    onClick={() => {
                      setIsEditPhaseDialogOpen(true);
                      setSelectedPhase(row);
                    }}
                  >
                    <Icon icon="solar:pen-new-square-linear" width={21} className="text-primary" />
                  </Button>
                </div>
              );
            case 2:
              return (
                <Button
                  variant="ghost"
                  size={'icon'}
                  onClick={() => {
                    setIsStatusDialogOpen(true);
                    setSelectedStatus(row);
                  }}
                >
                  <Icon icon="solar:pen-new-square-linear" width={21} className="text-primary" />
                </Button>
              );
            default:
              return '_';
          }
        },
      },
    ];
  }, [t]);

  return (
    <div>
      <Table
        rows={data || []}
        columns={columns}
        loading={isPending}
        nestedConfig={{
          enabled: true,
          childProperties: ['phases', 'status'],
        }}
      >
        <TableContent>
          <TableContentHeader tabs={adminTableTabs}></TableContentHeader>
          <TableContentBody />
        </TableContent>
      </Table>
      {isStatusDialogOpen && (
        <EditStatusDialog
          isOpen={isStatusDialogOpen}
          setIsOpen={setIsStatusDialogOpen}
          selectedStatus={selectedStatus}
        />
      )}
      {isEditPhaseDialogOpen && (
        <EditPhaseDialog
          isOpen={isEditPhaseDialogOpen}
          setIsOpen={setIsEditPhaseDialogOpen}
          selectedPhase={selectedPhase}
        />
      )}
    </div>
  );
};

export default MetaData;
