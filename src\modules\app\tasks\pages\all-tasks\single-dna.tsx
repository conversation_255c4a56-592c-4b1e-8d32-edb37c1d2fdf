import { useState, useMemo, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, Icon, useNotify } from '@/index';
import { Editor } from '@/components/CKEditor';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useApproveOrFeedbackTaskDna, useGetTaskByCodeAdmin } from '@/modules/app/tasks/apis/queries';
import { useQueryClient } from '@tanstack/react-query';
import EditDna from '@/modules/app/components/edit-dna';
import NotesDialog from '../../components/notes-dialog';
import MetaData from '../../components/task-meta-data';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import TranslationTabView from '@/modules/app/components/localization/transaltion-view';
import Activity from '@/modules/app/components/activity/view-only-activity';
import { getContentStatusStyle } from '@/utils/helpers';
import { Separator } from '@/components/ui/separator';
import TranslationContentTabs from '@/modules/app/components/translation-content-tabs';
import { ILanguage } from '@/modules/app/dashboard/transaltion/types';
import { IDNA } from '@/modules/app/dna/types';
import WorkflowStatusChecklist from '@/modules/app/components/work-flow-status-checklist';
import SlideshowList from '@/modules/app/dna/components/slideshow/slideshow-list';
import Tabs, { ITab } from '@/components/tabs';

const SingleDna = () => {
  const { taskId, dnaId } = useParams();
  const { data } = useGetTaskByCodeAdmin(taskId || '');
  const details: IDNA | any = useMemo(() => {
    if (data) {
      const dna = data?.content?.tool_data?.topics?.flatMap((t) => t.dnas).find((d) => d.id === Number(dnaId));
      return dna;
    }
  }, [data]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [selectedContentLanguage, setSelectedContentLanguage] = useState<ILanguage>();
  const [selectedContent, setSelectedContent] = useState<string>('');
  const [editDialog, setEditDialog] = useState<any>(false);
  const [notesDialog, setNotesDialog] = useState(false);
  const { t, i18n } = useTranslation();
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  const { mutate: approveDna, isPending: isApproving } = useApproveOrFeedbackTaskDna();
  const { mutate: feedbackDna, isPending: isFeedingback } = useApproveOrFeedbackTaskDna();
  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    if (!dnaId) return false;
    const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
    return savedState ? JSON.parse(savedState) : false;
  });

  const navigate = useNavigate();
  const { confirm } = useConfirmation();

  useEffect(() => {
    if (details) {
      const allDnaIds = data?.content?.tool_data?.all_dnas_ids || [];
      setIds(allDnaIds);
      setSelectedContentLanguage(details?.language);
      setSelectedContent(details?.dna_content);
      const index = allDnaIds.findIndex((id: number) => id === Number(dnaId));
      setCurrentIndex(index);
    }
    if (dnaId) {
      const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
      setIsExpanded(savedState ? JSON.parse(savedState) : false);
    }
    if (details && !details.dna_content) {
      notify.error(
        'No content has been generated for this DNA yet. Please create the content before accessing this page.'
      );
    }
  }, [details, dnaId]);

  useEffect(() => {
    if (dnaId) {
      localStorage.setItem(`dnaContentExpanded-${dnaId}`, JSON.stringify(isExpanded));
    }
  }, [isExpanded, dnaId]);

  // Tabs States
  const activeTabComponent: Record<string, JSX.Element> = {
    'meta-data': <MetaData isSuperAdmin details={details as any} />,
    activity: <Activity details={details as any} />,
    localization: <TranslationTabView details={details} />,
    slideshow: <SlideshowList />,
  };

  // Create tabs array with components
  const theTabs: ITab[] = [
    {
      label: t('dnaSinglePage.tabs.metaData'),
      value: 'meta-data',
      component: activeTabComponent['meta-data'],
    },
    {
      label: t('dnaSinglePage.tabs.activity'),
      value: 'activity',
      component: activeTabComponent['activity'],
    },
    {
      label: t('dnaSinglePage.tabs.Localization'),
      value: 'localization',
      component: activeTabComponent['localization'],
    },
    {
      label: t('dnaSinglePage.tabs.slideShow'),
      value: 'slideshow',
      component: activeTabComponent['slideshow'],
    },
    // {
    //   label: t('dnaSinglePage.tabs.reading'),
    //   value: 'reading',
    //   subText: true,
    // },
    // {
    //   label: t('dnaSinglePage.tabs.podcast'),
    //   value: 'podcast',
    //   subText: true,
    // },
    // {
    //   label: t('dnaSinglePage.tabs.video'),
    //   value: 'video',
    //   subText: true,
    // },
  ];

  const toggleHeight = () => setIsExpanded((prev: any) => !prev);

  const handleApproveTAsk = () => {
    confirm({
      variant: 'info',
      title: t('approve.confirmation.title'),
      description: t('approve.confirmation.description'),

      onConfirm: () => {
        if (details?.dna_content) {
          approveDna({ dna_id: details.id, status: 'approved', code: data?.code });
        }
      },
    });
  };

  const handleFeedback = () => {
    confirm({
      variant: 'info',
      title: t('feedback.confirmation.title'),
      description: t('feedback.confirmation.description'),

      onConfirm: () => {
        if (details?.dna_content) {
          feedbackDna({ dna_id: details.id, status: 'feedback', code: data?.code });
        }
      },
    });
  };

  const handleNext = () => {
    if (ids.length > 0) {
      if (currentIndex < ids.length - 1) {
        const nextIndex = currentIndex + 1;
        navigate(`/app/all-tasks/course/${data?.code}/dna/${ids[nextIndex]}`);
        setCurrentIndex(nextIndex);
      }
    } else {
      navigate(`/app/all-tasks/course/${data?.code}/dna/${Number(dnaId) + 1}`);
    }
  };

  const handlePrevious = () => {
    if (ids.length > 0) {
      if (currentIndex > 0) {
        const prevIndex = currentIndex - 1;
        navigate(`/app/all-tasks/course/${data?.code}/dna/${ids[prevIndex]}`);
        setCurrentIndex(prevIndex);
      }
    } else {
      navigate(`/app/all-tasks/course/${data?.code}/dna/${Number(dnaId) - 1}`);
    }
  };

  const canGoNext = ids.length > 0 ? currentIndex < ids.length - 1 : true;
  const canGoPrevious = ids.length > 0 ? currentIndex > 0 : Number(dnaId) > 1;

  return (
    <div className="my-3 space-y-4">
      <Card className="p-6">
        <div className=" mb-2">
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              {getContentStatusStyle(details?.dna_status)}
              <p className="text-xl capitalize ">{details?.title}</p>
              <p className="opacity-[0.5]">{details?.topic?.title}</p>
              <WorkflowStatusChecklist needsPermissions={false} dna={details} />
            </div>
            <div className="space-y-3">
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  className="flex items-center gap-3 text-sm hover:text-primary dark:hover:text-primary"
                  onClick={handlePrevious}
                  disabled={!canGoPrevious}
                >
                  <Icon
                    icon={i18n.language === 'en' ? 'line-md:chevron-small-left' : 'line-md:chevron-small-right'}
                    className="mt-1"
                    width="20"
                  />
                  <p>{t('dnaSingle.previousDna')}</p>
                </Button>
                <Button
                  variant="ghost"
                  className="flex items-center gap-3 text-sm hover:text-primary dark:hover:text-primary"
                  onClick={handleNext}
                  disabled={!canGoNext}
                >
                  <p>{t('dnaSingle.nextDna')}</p>
                  <Icon
                    icon={i18n.language === 'en' ? 'line-md:chevron-small-right' : 'line-md:chevron-small-left'}
                    className="mt-1"
                    width="20"
                  />
                </Button>
              </div>
              <div className="flex justify-end gap-3">
                <ConditionalComponent
                  status={details?.dna_status}
                  wantedStatus={[
                    StatusClass.DNA.REVIEW.APPROVED,
                    StatusClass.DNA.REVIEW.FEEDBACK,
                    StatusClass.DNA.REVIEW.PRODUCTION,
                  ]}
                >
                  <Button
                    className="min-w-[150px]"
                    onClick={handleFeedback}
                    disabled={details?.dna_status.phase_with_status === StatusClass.DNA.REVIEW.FEEDBACK}
                    loading={isFeedingback}
                  >
                    {t('feedback')}
                  </Button>
                  <Button
                    className="min-w-[150px]"
                    loading={isApproving}
                    disabled={details?.dna_status.phase_with_status === StatusClass.DNA.REVIEW.APPROVED}
                    onClick={handleApproveTAsk}
                  >
                    {t('approve.review.singleDNA')}
                  </Button>
                </ConditionalComponent>
              </div>
            </div>
          </div>
          <Separator className="mt-4" />

          <TranslationContentTabs
            Dna={details}
            setSelectedContent={setSelectedContent}
            setSelectedContentLanguage={setSelectedContentLanguage}
          />
          <div className="py-4">
            <div
              className={`
        overflow-y-hidden
        relative
        transition-all
        duration-500
        ease-in-out
        ${isExpanded ? 'max-h-[15000px]' : ' max-h-[350px]'}
        [&_.ck-toolbar]:hidden
      `}
            >
              <Editor
                height={'auto'}
                editorContent={selectedContent}
                setEditorContent={setSelectedContent}
                readOnly
                language={selectedContentLanguage}
              />
              {!isExpanded && (
                <div className="absolute bottom-[0px] h-[50px] left-0 right-0 bg-gradient-to-t from-background">
                  <span className="block">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                </div>
              )}
            </div>
            <div className="flex gap-3 justify-center items-center mt-2 cursor-pointer text-primary">
              <Icon icon={isExpanded ? 'ri:arrow-up-s-line' : 'ri:arrow-down-s-line'} width={22} />
              <p onClick={toggleHeight} aria-expanded={isExpanded} role="button" tabIndex={0}>
                {isExpanded ? t('dnaSinglePage.hide') : t('dnaSinglePage.expand')}
              </p>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="flex flex-wrap gap-2 p-3">
          <Tabs tabs={theTabs} defaultTab="meta-data" useUrlParams={true} paramName="tab" />
        </div>
      </Card>

      {details && editDialog && data && (
        <EditDna onClose={() => setEditDialog(false)} onOpen={editDialog} data={details as any} />
      )}
      {notesDialog && <NotesDialog isOpen={notesDialog} data={details} tool={data} setIsOpen={setNotesDialog} />}
    </div>
  );
};

export default SingleDna;
