import { Form, TextInput, useForm, useValidate, Modal, ColorInput } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useUpdateModulesStatus } from '../apis/queries';
import { useEffect } from 'react';
import { IStatus } from '../types';
import i18n from '@/utils/i18n';
interface IProps {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  selectedStatus: IStatus | null;
}

const EditDialog = ({ isOpen, setIsOpen, selectedStatus }: IProps) => {
  // Hooks
  const { isRequired } = useValidate();
  const { t } = useTranslation();
  const { mutate, isPending } = useUpdateModulesStatus();
  //Form
  const { form, setFieldValue } = useForm({
    name_ar: '',
    name_en: '',
    description: '',
    module_id: 0,
    status_id: 0,
    background_color: null,
    text_color: null,
  });
  useEffect(() => {
    if (selectedStatus) {
      setFieldValue('module_id')(selectedStatus?.module_status_id);
      setFieldValue('status_id')(selectedStatus?.id);
      setFieldValue('name_ar')(selectedStatus?.name_ar);
      setFieldValue('name_en')(selectedStatus?.name_en);
      setFieldValue('description')(selectedStatus?.description);
      setFieldValue('background_color')(selectedStatus?.background_color);
      setFieldValue('text_color')(selectedStatus?.text_color);
    }
  }, [selectedStatus]);
  // Functions
  const handleSubmit = async () => {
    mutate(form, {
      onSuccess: () => {
        setIsOpen(false);
      },
    });
  };
  return (
    <Modal
      className="overflow-visible"
      width={800}
      open={isOpen}
      onOpenChange={setIsOpen}
      modalHeader={t('modulesStatus.popup.editStatus.title')}
    >
      <Form onSubmit={handleSubmit}>
        <div className="justify-between">
          <div className="lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name_ar"
              label={t('name_ar')}
              placeholder={t('name_ar')}
              value={form.name_ar}
              onChange={setFieldValue('name_ar')}
              isRequired
              validators={[isRequired()]}
            />
            <TextInput
              name="name_en"
              label={t('name_en')}
              placeholder={t('name_en')}
              value={form.name_en}
              onChange={setFieldValue('name_en')}
              isRequired
              validators={[isRequired()]}
            />
            <TextInput
              name="description"
              label={t('description')}
              placeholder={t('description')}
              value={form.description}
              onChange={setFieldValue('description')}
              isRequired
              validators={[isRequired()]}
            />
            <h1 className="text-sm mt-1">{t('statusColor')}</h1>
            <div className="border border-border p-4 rounded-lg grid gap-2  grid-cols-3">
              <div className="col-span-2 grid grid-cols-2 gap-2 w-full">
                <ColorInput
                  name="textColor"
                  label={t('textColor')}
                  placeholder={t('textColor')}
                  value={form.text_color}
                  onChange={setFieldValue('text_color')}
                  className="w-full"
                />

                <ColorInput
                  name="background_color"
                  label={t('backgroundColor')}
                  placeholder={t('backgroundColor')}
                  value={form.background_color}
                  onChange={setFieldValue('background_color')}
                  className="w-full"
                />
              </div>

              <div className="col-span-1 flex flex-col">
                <h1 className="text-sm mt-1">{t('finalStatusLook')}</h1>
                <div
                  style={{ backgroundColor: form.background_color, color: form.text_color }}
                  className="p-2 rounded-md mt-auto"
                >
                  {i18n.language === 'ar' ? form.name_ar : form.name_en}
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-start gap-2 mt-[16px] justify-end">
            <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
              {t('save')}
            </Button>

            <Button disabled={isPending} onClick={() => setIsOpen(false)} className=" mt-4" variant={'outline'}>
              {t('cancel')}
            </Button>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default EditDialog;
