stages:
 - build

build-pre:
  stage: build
  variables:
    # CI_DEBUG_TRACE: "true"
  script:
    - ssh -oStrictHostKeyChecking=no gitlab@********** "sudo bash /home/<USER>/scripts/manual_deploy.sh"
  only:
    - pre
  tags:
   - shell

build-main:
  stage: build
  variables:
    # CI_DEBUG_TRACE: "true"
  script:
    - ssh -oStrictHostKeyChecking=no gitlab@************ "bash /home/<USER>/scripts/deploy.sh"
  only:
    - main
  tags:
   - shell