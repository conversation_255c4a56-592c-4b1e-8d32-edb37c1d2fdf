import { lazy } from 'react';
import { ProtectedRoute } from '@/components';

const SystemLogs = lazy(() => import('./pages/list'));

export default [
  {
    path: 'systemLogs',
    element: (
      <ProtectedRoute requiredPermissions={'for_administration'}>
        <SystemLogs />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.dashboard.systmeLogs',
        title: 'breadcrumb.dashboard.systmeLogs',
      };
    },
  },
];
