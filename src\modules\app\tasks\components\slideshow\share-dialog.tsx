import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { t } from 'i18next';
import { Copy, CopyCheck } from 'lucide-react';
import { useNotify } from '@/hooks';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ShareDialogProps {
  uuid: string;
}

const ShareDialog: React.FC<ShareDialogProps> = ({ uuid }) => {
  const [activeTab, setActiveTab] = useState('link');

  const baseUrl = window.location.origin;
  const shareLink = `${baseUrl}/embed/slideshow/${uuid}`;
  const embedCode = `<iframe width="800" height="600" src="${shareLink}" frameborder="0" allowfullscreen></iframe>`;

  const [isPromptCopied, setIsPromptCopied] = useState<boolean>(false);
  useEffect(() => {
    if (isPromptCopied) {
      setTimeout(() => {
        setIsPromptCopied(false);
      }, 15000);
    }
  }, [isPromptCopied]);
  const { notify } = useNotify();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      notify.success(t('promptCopied'));
      setIsPromptCopied(true);
    });
  };

  return (
    <Dialog>
      <DialogTrigger>
        <Button variant="outline">{t('sharedSlideshow.share')}</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{t('slideshow.share')}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1">
          {/* Right side - Share options */}
          <div className="flex flex-col">
            <Tabs defaultValue="link" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-2 mb-4">
                <TabsTrigger value="link">{t('slideshow.shareLink')}</TabsTrigger>
                <TabsTrigger value="embed">{t('slideshow.embedCode')}</TabsTrigger>
              </TabsList>

              <TabsContent value="link" className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Input value={shareLink} readOnly className="flex-1" />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger className="me-5">
                        {isPromptCopied ? <CopyCheck /> : <Copy onClick={() => copyToClipboard(shareLink)} />}
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{t('copy')}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <p className="text-sm text-muted-foreground">{t('slideshow.shareLinkDescription')}</p>
              </TabsContent>

              <TabsContent value="embed" className="space-y-4">
                <div className="flex flex-col space-y-2">
                  <div className="relative flex gap-2 items-start">
                    <textarea
                      value={embedCode}
                      rows={6}
                      readOnly
                      className="w-full p-3 rounded-md border resize-none font-mono text-sm"
                    />
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger className="me-5">
                          {isPromptCopied ? <CopyCheck /> : <Copy onClick={() => copyToClipboard(embedCode)} />}
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{t('copy')}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <p className="text-sm text-muted-foreground">{t('slideshow.embedDescription')}</p>
                </div>
              </TabsContent>
            </Tabs>

            {/* <div className="mt-auto pt-4">
              <h3 className="text-sm font-medium mb-2">{t('slideshow.shareOptions')}</h3>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" className="gap-2">
                  <Icon icon="lucide:mail" width={16} />
                  {t('slideshow.email')}
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Icon icon="lucide:twitter" width={16} />
                  Twitter
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Icon icon="lucide:facebook" width={16} />
                  Facebook
                </Button>
                <Button variant="outline" size="sm" className="gap-2">
                  <Icon icon="lucide:linkedin" width={16} />
                  LinkedIn
                </Button>
              </div>
            </div> */}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShareDialog;
