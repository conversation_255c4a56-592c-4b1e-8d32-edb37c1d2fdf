import React, { memo, useState } from 'react';
import { useTable } from './context';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Checkbox } from '../ui/checkbox';
import { Icon } from '../icon';

function ColumnExpander() {
  const { columns, setColumns } = useTable();
  const [initialColumns] = useState(columns);

  const handleCheckboxChange = (accessorKey: string) => {
    setColumns((prevColumns: ITableColumn<any>[]) => {
      // Create a map of current columns for easy lookup
      const currentColumnsMap = new Map(prevColumns.map((col) => [col.accessorKey, col]));

      // If we're removing a column
      if (currentColumnsMap.has(accessorKey)) {
        return prevColumns.filter((col) => col.accessorKey !== accessorKey);
      }

      // If we're adding a column back
      const newColumns = [...initialColumns]
        .filter((col) => currentColumnsMap.has(col.accessorKey) || col.accessorKey === accessorKey)
        .map((col) => currentColumnsMap.get(col.accessorKey) || col);

      return newColumns;
    });
  };

  const filteredKeys = initialColumns.filter((item) => !['title', 'actions'].includes(item.accessorKey));

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Icon icon="mingcute:columns-2-line" width={30} className="cursor-pointer rounded-md opacity-60" />
      </DropdownMenuTrigger>
      <DropdownMenuContent className="p-3 space-y-3 min-w-[200px]">
        {filteredKeys.map((column) => (
          <div className="flex gap-2 items-center" key={column.accessorKey}>
            <Checkbox
              className="rounded-md"
              id={column.accessorKey}
              checked={columns.some((col) => col.accessorKey === column.accessorKey)}
              onCheckedChange={() => handleCheckboxChange(column.accessorKey)}
            />
            <label
              htmlFor={column.accessorKey}
              className="cursor-pointer text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-0.5"
            >
              {column.header}
            </label>
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

export default memo(ColumnExpander);
