import { formatDateByYear } from '@/utils/helpers';
import { useScreenSize } from '@/index';
import { ProtectedTaskComponent } from './protected-task-component';
import { IDNA } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/index';

interface MetaDataProps {
  details: IDNA;
}

const MetaDataItem = ({ label, value }: { label: string; value: string | number | React.ReactNode }) => (
  <div>
    <p className="text-sm opacity-[0.5]">{label}</p>
    <p className="font-medium">{value}</p>
  </div>
);

type ResourceItem = string | { link: string; title: string };

const ResourcesSection = ({ resources }: { resources: ResourceItem[] | string }) => {
  if (!resources) return null;

  // If it's a string, render it directly
  if (typeof resources === 'string') {
    return <p className="pl-4">- {resources}</p>;
  }

  return (
    <div className="mt-1">
      {resources.length > 0 ? (
        resources.map((resource, index) => (
          <div key={index} className="flex flex-col gap-2 px-3">
            {typeof resource === 'string' ? (
              resource.includes('https://') ? (
                <a
                  href={resource}
                  className="pl-4 underline text-primary hover:text-primary-dark transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  - {resource}
                </a>
              ) : (
                <p className="pl-4">- {resource}</p>
              )
            ) : (
              <a
                href={resource.link}
                className="pl-4 underline text-primary hover:text-primary-dark transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                - {resource.link || resource.title}
              </a>
            )}
          </div>
        ))
      ) : (
        <p className="pl-4">-</p>
      )}
    </div>
  );
};

// Helper function to calculate reading time
const calculateReadingTime = (wordCount: number): string => {
  const minutes = Math.ceil(wordCount / 90);
  if (minutes <= 0) return 'Less than 1 min';
  if (minutes >= 7) return '7+ min';
  return `${minutes} min`;
};

const MetaData = ({ details, isSuperAdmin }: { details: IDNA | undefined; isSuperAdmin?: boolean }) => {
  const screen = useScreenSize();
  const { t, i18n } = useTranslation();
  const isLTR = i18n.dir() === 'ltr';

  if (!details) {
    return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />; // Or you can return a placeholder message
  }

  const labelKey = i18n.dir() === 'ltr';

  const mainMetaData = [
    { label: t('dnaSinglePage.metaData.subject'), value: isLTR ? details.subject?.name_en : details.subject?.name_ar },
    {
      label: t('dnaSinglePage.metaData.length'),
      value: `${details.word_count} ${t('editDnaModal.word')} = ${calculateReadingTime(details?.word_count || 0)}`,
    },
    {
      label: t('dnaSinglePage.metaData.learning level'),
      value: i18n.language === 'en' ? details.bloom_tax?.name_en : details.bloom_tax?.name_ar,
    },
    {
      label: t('dnaSinglePage.metaData.language'),
      value: i18n.language === 'en' ? details.language?.name_en : details.language?.name_ar,
    },
    { label: t('dnaSinglePage.metaData.context'), value: details.txt || ' -' },
    { label: t('dnaSinglePage.metaData.learning objective'), value: details.learning_objectives },
  ];

  const proceduresData = [
    'editor',
    'edtidingApprovedBy',
    'reviwer',
    'ReviweringApprovedBy',
    'ActiviteCreatedBy',
    'ActiviteApprovedBy',
    'SlideShowCreatedBy',
    'SlideShowApprovedBy',
    'ArabicLocalizedBy',
    'ArabicApprovedBy',
    'EnglishLocalizedBy',
    'EnglishApprovedBy',
    'ReadingViewBy',
    'ReadingApproveBy',
    'VideoProducedBy',
    'VideoProductionApprovedBy',
    'VideoUpLoadedApprovedBy',
  ].map((key) => ({
    label: t(`dnaSinglePage.metaData.${key}`),
    value: key === 'editor' ? `${details?.user?.name} :${formatDateByYear(details?.created_at, i18n.language)}` : '-',
  }));

  return (
    <div>
      <div className="grid grid-cols-3 gap-6">
        <ProtectedTaskComponent requiredPermissions={isSuperAdmin ? 'public' : 'dna_sub_data'}>
          {mainMetaData.slice(0, 3).map((item, index) => (
            <MetaDataItem key={index} label={item.label} value={item.value} />
          ))}
          {mainMetaData.slice(3, 5).map((item, index) => (
            <div key={index} className="col-span-1">
              <MetaDataItem label={item.label} value={item.value} />
            </div>
          ))}
          <div className="col-span-2">
            {mainMetaData.slice(5).map((item, index) => (
              <div key={index} className="col-span-2">
                <MetaDataItem label={item.label} value={item.value} />
              </div>
            ))}
          </div>
        </ProtectedTaskComponent>
        <ProtectedTaskComponent requiredPermissions={isSuperAdmin ? 'public' : 'dna_sub_data'}>
          <div className="col-span-2">
            <p className="text-sm opacity-[0.5]">{t('dnaSinglePage.metaData.keywords')}</p>
            <div className="grid grid-cols-2 xl:grid-cols-6 gap-2">
              {details?.tags?.map((tag: any, index: number) => (
                <p key={index} className={`${index === 4 ? 'col-start-1' : ''} font-medium `}>
                  • {tag.name}
                </p>
              )) || '-'}
            </div>
          </div>
        </ProtectedTaskComponent>
        <ProtectedTaskComponent requiredPermissions={isSuperAdmin ? 'public' : 'dna_resource_data'}>
          <div className="col-span-2">
            <p className="text-sm opacity-[0.5]">{t('dnaSinglePage.metaData.resources')}</p>
            <ResourcesSection resources={(details?.resources as string[]) || []} />
          </div>
        </ProtectedTaskComponent>
      </div>
    </div>
  );
};

export default MetaData;
