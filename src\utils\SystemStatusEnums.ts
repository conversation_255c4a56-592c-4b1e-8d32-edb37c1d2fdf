export default class StatusClass {
  static readonly MODULE_STATUS = {
    DNA: 'DNA',
    COURSE: 'Course',
    TOPIC: 'Topic',
    TASK: 'Task',
  } as const;

  static readonly PHASE = {
    EDIT: 'Edit',
    REVIEW: 'Review',
    PRODUCTION: 'Production',
    STATUS: 'Group',
  } as const;

  static readonly CONTENT_STATUS = {
    DRAFT: 'draft',
    NO_CONTENT: 'no_content',
    COMPLETED: 'completed',
    RFR: 'ready_for_review',
    PRODUCTION: 'ready_for_production',
    FEEDBACK: 'feedback',
    APPROVED: 'approved',
    OPEN: 'open',
    IN_PROGRESS: 'in_progress',
    NOT_AVAILABLE: 'not_available',
    OVERDUE_NO_RESPONSE: 'overdue_no_response',
    OVERDUE: 'overdue',
    DECLINED: 'declined',
    REOPEN: 're-open',
    NOT_STARTED: 'not_started',
  } as const;

  static readonly DNA = {
    EDIT: {
      NO_CONTENT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.NO_CONTENT}`,
      DRAFT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      RFR: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.RFR}`,
      APPROVED: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    REVIEW: {
      DRAFT: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      PRODUCTION: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.PRODUCTION}`,
      APPROVED: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    PRODUCTION: {
      DRAFT: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
    },
  };
  static readonly TOPIC = {
    EDIT: {
      NO_CONTENT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.NO_CONTENT}`,
      DRAFT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      RFR: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.RFR}`,
      APPROVED: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    REVIEW: {
      DRAFT: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      PRODUCTION: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.PRODUCTION}`,
      APPROVED: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    PRODUCTION: {
      DRAFT: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
    },
  };
  static readonly COURSE = {
    EDIT: {
      NO_CONTENT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.NO_CONTENT}`,
      DRAFT: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      RFR: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.RFR}`,
      APPROVED: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.EDIT}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    REVIEW: {
      DRAFT: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
      PRODUCTION: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.PRODUCTION}`,
      APPROVED: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.APPROVED}`,
      FEEDBACK: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
    },
    PRODUCTION: {
      DRAFT: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.DRAFT}`,
    },
  };
  static readonly COURSE_GROUP = {
    NOT_STARTED: `${StatusClass.PHASE.STATUS}: ${StatusClass.CONTENT_STATUS.NOT_STARTED}`,
    IN_PROGRESS: `${StatusClass.PHASE.STATUS}: ${StatusClass.CONTENT_STATUS.IN_PROGRESS}`,
    COMPLETED: `${StatusClass.PHASE.STATUS}: ${StatusClass.CONTENT_STATUS.COMPLETED}`,
  };
  static readonly TASK = {
    REVIEW: {
      OPEN: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.OPEN}`,
      IN_PROGRESS: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.IN_PROGRESS}`,
      COMPLETED: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.COMPLETED}`,
      FEEDBACK: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
      OVERDUE: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.OVERDUE}`,
      NOT_AVAILABLE: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.NOT_AVAILABLE}`,
      OVERDUE_NO_RESPONSE: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.OVERDUE_NO_RESPONSE}`,
      DECLINED: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.DECLINED}`,
      REOPEN: `${StatusClass.PHASE.REVIEW}: ${StatusClass.CONTENT_STATUS.REOPEN}`,
    },
    PRODUCTION: {
      OPEN: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.OPEN}`,
      IN_PROGRESS: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.IN_PROGRESS}`,
      COMPLETED: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.COMPLETED}`,
      FEEDBACK: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.FEEDBACK}`,
      OVERDUE: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.OVERDUE}`,
      NOT_AVAILABLE: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.NOT_AVAILABLE}`,
      OVERDUE_NO_RESPONSE: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.OVERDUE_NO_RESPONSE}`,
      DECLINED: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.DECLINED}`,
      REOPEN: `${StatusClass.PHASE.PRODUCTION}: ${StatusClass.CONTENT_STATUS.REOPEN}`,
    },
  };
}
