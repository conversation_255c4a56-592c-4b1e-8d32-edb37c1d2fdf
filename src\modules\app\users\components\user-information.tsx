import { useValidate, useForm, TextInput, Textarea, Form, ComboboxInput, Icon, useNotify } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import moment from 'moment';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { useEffect, useRef, useState } from 'react';

const Profile = ({ data }: any) => {
  const { data: allmetaData } = useGetSingleMetadata('');
  const { t, i18n } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);
  const { notify } = useNotify();

  const educationsOptions = allmetaData?.filter((item) => item.type === 'educations') || [];
  const universitiesOptions = allmetaData?.filter((item) => item.type === 'universities') || [];
  const nationalitiesOptions = allmetaData?.filter((item) => item.type === 'nationalities') || [];
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    name: data?.name || null,
    info: data?.info || null,
    profession: data?.profession || null,
    nationality_id: data?.nationality?.id || null,
    education_id: data?.education?.id || null,
    university_id: data?.university?.id || null,
    nationally_number: data?.nationally_number || null,
    birth_date: data?.birth_date || null,
    sex: data?.sex || null,
    image: data?.image || null,
    cv: data?.cv || null,
  });

  const handleFileUpload = (event: any) => {
    setLoading(true);
    const file = event.target.files[0];
    const allowedTypes = ['application/pdf']; // Only allow PDF

    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('dnaCreationPage.form.file.validation'));
      event.target.value = '';
    } else {
      const formData = new FormData();
      formData.append('cv', file);
      setFieldValue('cv')(file);
    }
    setLoading(false);
  };

  const handleImageUpload = (event: any) => {
    setImageLoading(true);
    const file = event.target.files[0];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']; // Only allow images

    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('Please select a valid image file (JPEG, PNG, JPG)'));
      event.target.value = '';
      setImageLoading(false);
    } else if (file) {
      const formData = new FormData();
      formData.append('image', file);
      setFieldValue('image')(file);
      setImageLoading(false);
    } else {
      setImageLoading(false);
    }
  };

  useEffect(() => {
    setFieldValue({
      name: data?.name || null,
      info: data?.info || null,
      profession: data?.profession || null,
      nationality_id: data?.nationality_id || null,
      education_id: data?.education_id || null,
      university_id: data?.university_id || null,
      nationally_number: data?.nationally_number || null,
      birth_date: data?.birth_date || null,
      sex: data?.sex || null,
      image: data?.image || null,
      cv: data?.cv || null,
    });
  }, [data]);

  return (
    <Form className="space-y-5 ">
      <div className="grid grid-cols-2 w-full gap-5">
        <div className="gap-2 w-fit">
          <h4 className="font-medium text-zinc-400 mb-2">{t('profile.profilePicture')}</h4>
          <div className="relative">
            <Input
              ref={imageInputRef}
              className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer hidden`}
              type="file"
              id="image-upload"
              accept="image/*"
              onChange={handleImageUpload}
            />

            <div className="flex items-center gap-5">
              <div className={`relative size-24 border rounded-md overflow-hidden`}>
                <div className="w-full h-full bg-gray-100 flex flex-col justify-center">
                  {imageLoading ? (
                    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit mx-auto" width={30} />
                  ) : data?.image ? (
                    // Show image preview if there's an image
                    <img
                      src={typeof data.image === 'string' ? data.image : URL.createObjectURL(data.image)}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    // Show icon if no image
                    <div className="p-3 flex flex-col items-center justify-center">
                      <Icon icon={'ph:image'} className="text-gray-400" width={35} />
                    </div>
                  )}
                </div>
              </div>
            </div>
            <span
              onClick={() => data?.image && window.open(data.image, '_blank')}
              className={`text-sm ${data?.image ? 'cursor-pointer text-primary' : 'text-gray-500'}`}
            >
              {data?.image ? t('userPage.file.viewImage') : t('userPage.file.noImage')}
            </span>
          </div>
        </div>

        <div>
          <h4 className="font-medium text-zinc-400 mb-2">{t('profile.myCv')}</h4>
          <div className="relative">
            <Input
              ref={fileInputRef}
              className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer hidden`}
              type="file"
              id="file-upload"
              accept=".pdf"
              onChange={handleFileUpload}
            />

            <div className="flex items-center gap-5">
              <div className={`relative size-24 border rounded-md overflow-hidden`}>
                <div className="w-full h-full p-3 bg-gray-100 flex flex-col justify-center">
                  {loading ? (
                    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={35} />
                  ) : (
                    <Icon icon={'ph:file'} className="text-gray-400" width={35} />
                  )}
                </div>
              </div>
            </div>
            <span
              // onClick={() => window.open(data?.cv, '_blank')}
              className={`text-sm ${data?.cv ? 'cursor-pointer text-primary' : 'text-gray-500'} `}
            >
              {data?.cv ? t('userPage.file.viewCv') : t('userPage.file.noCv')}
            </span>
          </div>
        </div>
        <TextInput name="email" label={t('profile.email')} placeholder={data?.email} disabled className="w-full" />
        <TextInput
          name="status"
          label={t('profile.status')}
          placeholder={data?.account_status}
          disabled
          className="w-full"
        />

        <TextInput
          name="name"
          label={t('profile.name')}
          placeholder={t('profile.name')}
          value={data?.name}
          onChange={setFieldValue('name')}
          validators={[isRequired()]}
          className="w-full"
          disabled
        />

        <TextInput
          name="profession"
          label={t('profile.profession')}
          placeholder={t('profile.profession')}
          value={data?.profession}
          onChange={setFieldValue('profession')}
          validators={[isRequired()]}
          disabled
        />
        <ComboboxInput
          placeholder={t('profile.education')}
          name="education"
          label={t('profile.education')}
          options={generateEnum(educationsOptions, 'id', labelKey)}
          value={data?.education?.id}
          onChange={setFieldValue('education_id')}
          validators={[isRequired()]}
          dropIcon
          disabled
        />
        <ComboboxInput
          placeholder={t('profile.universities')}
          name="universities"
          label={t('profile.universities')}
          options={generateEnum(universitiesOptions, 'id', labelKey)}
          value={data?.university?.id}
          onChange={setFieldValue('university_id')}
          validators={[isRequired()]}
          dropIcon
          disabled
        />
        <ComboboxInput
          placeholder={t('profile.nationalities')}
          name="nationalities"
          label={t('profile.nationalities')}
          options={generateEnum(nationalitiesOptions, 'id', labelKey)}
          value={data?.nationality?.id}
          onChange={setFieldValue('nationality_id')}
          validators={[isRequired()]}
          dropIcon
          disabled
        />

        <div>
          <h3 className="text-sm mb-2 font-medium">{t('profile.birthDate')}</h3>
          <Popover>
            <PopoverTrigger disabled asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'pl-3 w-full text-left font-normal rounded-md',
                  !data?.birth_date && 'text-muted-foreground'
                )}
              >
                {data?.birth_date ? moment(form.birth_date).format('DD/MM/YYYY') : <span>{t('profile.pickDate')}</span>}
                <CalendarIcon className="ltr:ml-auto rtl:mr-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                captionLayout="dropdown-buttons"
                selected={data?.birth_date ? new Date(data?.birth_date) : undefined}
                onSelect={setFieldValue('birth_date')}
                disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                fromYear={1930}
                toYear={new Date().getFullYear()}
                initialFocus
                showOutsideDays={false}
              />
            </PopoverContent>
          </Popover>
        </div>

        <ComboboxInput
          placeholder={t('profile.gender')}
          name="gender"
          label={t('profile.gender')}
          options={[
            { value: 'male', label: 'Male' },
            { value: 'female', label: 'Female' },
          ]}
          value={data?.sex}
          onChange={setFieldValue('sex')}
          validators={[isRequired()]}
          dropIcon
          disabled
        />
        <TextInput
          name="profile.nationalNumber"
          label={t('profile.nationalNumber')}
          placeholder={t('profile.nationalNumber')}
          value={data?.nationally_number}
          onChange={setFieldValue('nationally_number')}
          validators={[isRequired()]}
          disabled
        />
        <Textarea
          name="info"
          label={t('profile.info')}
          placeholder={t('profile.info')}
          value={data?.info}
          onChange={setFieldValue('info')}
          validators={[isRequired()]}
          disabled
        />
      </div>
    </Form>
  );
};

export default Profile;
