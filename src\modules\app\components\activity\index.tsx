import { useState, useMemo } from 'react';
import { Form, Modal, useForm, useNotify, Api, GENERATE_ACTIVITY, TextInput, useValidate } from '@/index';
import { useTranslation } from 'react-i18next';
import QuestionTypeMcq from './question-type-mcq';
import QuestionT<PERSON><PERSON>lank from './question-type-blank';
import QuestionTypeCase from './question-type-case';
import { Button } from '@/components/ui/button';
import EmptyActivity from './empty-state';
import { IDNA } from '../../dna/types';
import { IActivityQuestion } from '../../tasks/types';
import withSettings from './with-settings';
import { Checkbox } from '@/components/ui/checkbox';
import ActivityFilter from './activityFilter';
import { useApproveMultipleActivity, useRegenerateActivity } from '../../tasks/apis/queries';
import { useParams } from 'react-router-dom';
import ActivityDialog from '@/modules/app/components/activity/activity-dialog';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
const Activity = ({ details }: { details: IDNA }) => {
  const [selectedQuestions, setSelectedQuestions] = useState<number[]>([]);
  const [selectedQuestionType, setSelectedQuestionType] = useState<
    'case_study' | 'mcq' | 'fill_in_the_blank' | 'true_false' | null
  >(null);
  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);

  const { mutate: regenerateActivity, isPending: isRegenerating } = useRegenerateActivity();
  const { mutate: approveMultipleActivity, isPending: isApprovingMultipleActivity } = useApproveMultipleActivity();
  const { t } = useTranslation();
  const { confirm } = useConfirmation();
  const { taskId } = useParams();

  const handleMultipleActivityApprove = () => {
    confirm({
      variant: 'info',
      title: t('activity.approve.confirmation.title'),
      description: t('activity.approve.all.confirmation.description'),
      onConfirm: () =>
        approveMultipleActivity(
          { code: taskId || '', activities: selectedQuestions },
          {
            onSuccess: () => {
              setSelectedQuestions([]);
            },
          }
        ),
    });
  };

  const handleRegenerateActivity = (form: any) => {
    confirm({
      variant: 'info',
      title: t('activity.regenerate.confirmation.title'),
      description: t('activity.regenerate.confirmation.description'),
      onConfirm: () => setIsActivityModalOpen(true),
    });
  };

  const handleSelectQuestion = (questionId: number) => {
    if (selectedQuestions.includes(questionId)) {
      setSelectedQuestions(selectedQuestions.filter((id) => id !== questionId));
    } else {
      setSelectedQuestions([...selectedQuestions, questionId]);
    }
  };
  const handleSelectAllQuestions = () => {
    if (selectedQuestions.length === Object.values(details?.activities).flat().length) {
      setSelectedQuestions([]);
      return;
    }
    setSelectedQuestions(
      details?.activities
        ? Object.values(details?.activities)
            .flat()
            .filter((item: IActivityQuestion) => item.status === 'draft')
            .map((item: IActivityQuestion) => item.id)
        : []
    );
  };

  const isDnaStatusProduction = details?.dna_status.phase_with_status === StatusClass.DNA.REVIEW.PRODUCTION;

  const QuestionTypesObject = useMemo(
    () => ({
      true_false: {
        title: t('dnaSinglePage.activity.trueOrFalse'),
        component: isDnaStatusProduction ? QuestionTypeMcq : withSettings(QuestionTypeMcq),
      },
      mcq: {
        title: t('dnaSinglePage.activity.multipleChoiceQuestions'),
        component: isDnaStatusProduction ? QuestionTypeMcq : withSettings(QuestionTypeMcq),
      },
      fill_in_the_blank: {
        title: t('dnaSinglePage.activity.fillintheBlank'),
        component: isDnaStatusProduction ? QuestionTypeBlank : withSettings(QuestionTypeBlank),
      },
      case_study: {
        title: t('dnaSinglePage.activity.caseStudy'),
        component: isDnaStatusProduction ? QuestionTypeCase : withSettings(QuestionTypeCase),
      },
    }),
    [t, isDnaStatusProduction]
  );
  const renderActivities = useMemo(() => {
    if (details?.activities_count === 0) {
      return <EmptyActivity contentId={details?.id} />;
    }

    return (
      <div>
        <div className="flex justify-between items-center -mb-5">
          <div className="flex gap-3">
            <ActivityFilter selectedType={selectedQuestionType} setSelectedType={setSelectedQuestionType} />
          </div>
          <ProtectedTaskComponent requiredPermissions="dna_activity_edit">
            <ConditionalComponent
              status={details?.dna_status}
              wantedStatus={[StatusClass.DNA.REVIEW.PRODUCTION]}
              operator="not"
            >
              <div className="flex gap-3">
                <Button variant={'ghost'} onClick={handleRegenerateActivity}>
                  {t('activity.regenerate')}
                </Button>
                <Button variant={'outline'} onClick={handleSelectAllQuestions}>
                  {t('activity.selectAll')}
                </Button>
                <Button
                  loading={isApprovingMultipleActivity}
                  disabled={selectedQuestions.length === 0 || isApprovingMultipleActivity}
                  onClick={handleMultipleActivityApprove}
                >
                  {t('activity.confirm')}
                  {selectedQuestions.length > 0 && `(${selectedQuestions.length}/ ${details?.activities_count})`}
                </Button>
              </div>
            </ConditionalComponent>
          </ProtectedTaskComponent>
        </div>
        {Object.entries(details?.activities || {})
          .filter(([key]) => !selectedQuestionType || key === selectedQuestionType)
          .map(([key, value]) => (
            <div key={key} className="mb-1.5">
              <div className="bg-secondary text-secondary-foreground mx-auto w-fit text-sm px-4 py-2 font-medium mt-14 mb-10 rounded-full">
                {QuestionTypesObject[key as keyof typeof QuestionTypesObject]?.title}
              </div>
              <div className="flex flex-col gap-10">
                {value.map((item: IActivityQuestion, index: number) => {
                  const Component = QuestionTypesObject[key as keyof typeof QuestionTypesObject]?.component;
                  return Component ? (
                    <div className="flex items-center gap-6">
                      {item.status === 'draft' ? (
                        <Checkbox
                          className="size-5"
                          checked={selectedQuestions.includes(item.id)}
                          onCheckedChange={() => handleSelectQuestion(item.id)}
                        />
                      ) : (
                        <div className="size-5" />
                      )}
                      <Component
                        key={index}
                        question={item}
                        questionNumber={index + 1}
                        setSelectedQuestions={setSelectedQuestions}
                      />
                    </div>
                  ) : null;
                })}
              </div>
            </div>
          ))}
      </div>
    );
  }, [details, QuestionTypesObject, selectedQuestions, selectedQuestionType, isApprovingMultipleActivity]);

  return (
    <div className="flex flex-col">
      {renderActivities}
      {isActivityModalOpen && (
        <ActivityDialog
          isOpen={isActivityModalOpen}
          onOpenChange={setIsActivityModalOpen}
          onSubmit={(form) => {
            regenerateActivity(
              { code: taskId || '', payload: form },
              {
                onSuccess: () => {
                  setIsActivityModalOpen(false);
                },
              }
            );
          }}
          isLoading={isRegenerating}
          contentId={details?.id}
        />
      )}
    </div>
  );
};

export default Activity;
