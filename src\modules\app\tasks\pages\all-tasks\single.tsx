import { Button } from '@/components/ui/button';
import { t } from 'i18next';
import { useParams } from 'react-router-dom';
import { useState } from 'react';
import { useGetTaskByCodeAdmin } from '../../apis/queries';
import SignleTaskReviwer from './signle-task';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { useHasPermission } from '@/modules/auth/store';

interface ITab {
  name: 'courseDetails' | 'taskDetails' | 'taskCode';
  disabled?: boolean;
}

const SingleTask = () => {
  const { taskId } = useParams();
  const { data: task } = useGetTaskByCodeAdmin(taskId || '');
  const navigate = useLanguageNavigate();
  const isAdmin = useHasPermission('for_administration');
  const [currentTab, setCurrentTab] = useState<'courseDetails' | 'taskDetails' | 'taskCode'>('taskCode');

  const tabs: ITab[] = [{ name: 'courseDetails' }, { name: 'taskDetails' }, { name: 'taskCode', disabled: true }];

  if (!task) return null;

  const handleSelect = (tab: ITab) => {
    if (tab.disabled) return;

    setCurrentTab(tab.name);
    if (tab.name === 'courseDetails') navigate(`/app/my-content/courses/${task?.content.tool_id}?tab=courseDetails`);
    if (tab.name === 'taskDetails') navigate(`/app/my-content/courses/${task?.content.tool_id}?tab=taskDetails`);
  };

  return (
    <div>
      <div className="bg-background p-4 ps-0">
        <div className="flex gap-3">
          {tabs.map(
            (tab) =>
              isAdmin && (
                <Button
                  variant={'outline'}
                  className={`min-w-[120px] hover:text-primary hover:bg-semi-primary ${
                    currentTab === tab.name ? 'text-primary bg-semi-primary' : ''
                  } `}
                  key={tab.name}
                  onClick={() => {
                    handleSelect(tab);
                  }}
                >
                  {tab.name === 'taskCode' ? task?.code || 'Task Code' : t(tab.name)}
                </Button>
              )
          )}
        </div>
      </div>
      {currentTab === 'taskDetails' ? <>_</> : currentTab === 'taskCode' ? <SignleTaskReviwer /> : <>_</>}
    </div>
  );
};

export default SingleTask;
