import { useFetchList } from '@/index';
import { useEffect, useMemo } from 'react';
import {
  convertStatusToFilterEnumByTitle,
  dateAndTimeFormat,
  getTaskStatusStyle,
  formatUserByNameAndEmail,
} from '@/utils/helpers';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import LanguageLink from '@/components/language-link';
import { useChangeTaskStatus } from '@/modules/app/tasks/apis/queries';

import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { ITask } from '../../types';
import { useGetModulesStatus } from '@/modules/app/dashboard/modules-status/apis/queries';
import { useTaskTablesTabs } from '@/modules/app/content-tabs';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import { useSearchParams } from 'react-router-dom';
function DnaList() {
  //  Hooks
  const { t, i18n } = useTranslation();
  const { mutate: acceptTask, isPending: isAcceptPending } = useChangeTaskStatus();
  const { mutate: declineTask, isPending: isDeclinePending } = useChangeTaskStatus();
  const { data: modulesStatus } = useGetModulesStatus();
  const [searchParams] = useSearchParams();

  // listen for seach query to handle notification click
  useEffect(() => {
    if (!searchParams.has('search')) return;
    search.update(Object.fromEntries(searchParams).search);
  }, [Object.fromEntries(searchParams)?.search]);

  const taskTbaleTabs = useTaskTablesTabs();

  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'Task', i18n.language);

  const { loading, list, count, refresh, search, filters, pagination } = useFetchList('/reviewer/tasks', 'tasks', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 10,
    },
    filters: {
      status_id: {
        dynamicEnum: statusFilter,
        placeholder: 'assignedTask.statusFilter',
      },
    },
  });

  const handleAccept = (task: any) => {
    acceptTask(
      { task_code: task.code, status: 'Accepted' },
      {
        onSuccess: () => {
          refresh();
        },
      }
    );
  };

  const handleDecline = (task: any) => {
    declineTask(
      { task_code: task.code, status: 'Declined' },
      {
        onSuccess: () => {
          refresh();
        },
      }
    );
  };

  const columns: ITableColumn<ITask>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '250px',
        cell: ({ row }) => getTaskStatusStyle(row?.task_status),
      },
      {
        accessorKey: 'title',
        header: t('assignedTask.table.title'),
        width: '200px',
        cell: ({ row }) => {
          return (
            <div className="space-y-1 rtl:text-right">
              {row?.task_status.action === 'in_progress' ||
              row?.task_status.action === 'feedback' ||
              row?.task_status.action === ' in_process' ||
              row?.task_status.action === 're-open' ? (
                <LanguageLink to={`/app/my-tasks/course/${row.code}`} className="flex gap-x-1 relative">
                  <Button className="px-0" variant={'link'}>
                    {row?.content?.tool_data?.title || row?.content?.tool_data?.info}
                  </Button>
                </LanguageLink>
              ) : (
                row?.content?.tool_data?.title || row?.content?.tool_data?.info
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'code',
        header: t('assignedTask.table.code'),
        width: '250px',
      },
      {
        accessorKey: 'description',
        header: t('assignedTask.table.description'),
        width: '100px',
        tooltip: true,
      },
      {
        accessorKey: 'priority',
        header: t('assignedTask.table.priority'),
        width: '80px',
      },
      {
        accessorKey: 'due_date',
        header: t('assignedTask.table.due_date'),
        width: '100px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.due_date, i18n.language);
        },
      },
      {
        accessorKey: 'assigned_to',
        header: t('assignedTask.table.assigned_to'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.assigned_to);
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '180px',
        cell: ({ row }) => {
          return (
            row?.task_status.action === 'open' && (
              <div className="flex gap-x-1 relative">
                <Button
                  loading={isAcceptPending}
                  disabled={isAcceptPending}
                  onClick={() => handleAccept(row)}
                  className="h-9"
                >
                  {t('assignedTask.accept')}
                </Button>
                <Button
                  loading={isDeclinePending}
                  disabled={isDeclinePending}
                  onClick={() => handleDecline(row)}
                  className="h-9"
                  variant={'destructive'}
                >
                  {t('assignedTask.decline')}
                </Button>
              </div>
            )
          );
        },
      },
    ];
  }, [isAcceptPending, isDeclinePending]);
  return (
    <>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={taskTbaleTabs}>
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('assignedTask.searchPlaceholder')} />
              <TableFilters filters={filters} />
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
    </>
  );
}

export default DnaList;
