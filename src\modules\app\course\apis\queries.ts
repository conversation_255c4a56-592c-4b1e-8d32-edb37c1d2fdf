import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  addNewTopic,
  getCourseById,
  generateTopicDna,
  deleteCourses,
  createCourseAi,
  createCourseFile,
  markCourseAsReadyForReview,
  editCourseTitle,
  deleteCourse,
  markCourseAsApproved,
  updateCourse,
  getParentsTaskCodes,
  getTaskByCourseId,
  approveCoursesInReview,
  approveCoursesInEdit,
} from './endpoints';
import { useNotify } from '@/hooks';
import { useTranslation } from 'react-i18next';

export const useGetCourseById = (id: string | undefined) => {
  return useQuery({
    queryKey: ['course', id],
    queryFn: () => (id ? getCourseById(id) : Promise.resolve(null)),
    enabled: !!id,
    refetchInterval: 10000,
  });
};

export const useGetParentsTaskCodes = (id: string | undefined, canCreateChildTask: boolean) => {
  return useQuery({
    queryKey: ['TaskCodes', id],
    queryFn: () => (id ? getParentsTaskCodes(id) : Promise.resolve(null)),
    enabled: !!id && !!canCreateChildTask,
    gcTime: 0,
    staleTime: 0,
  });
};

export const useGetTaskByCourseId = (id: string | undefined) => {
  return useQuery({
    queryKey: ['task', 'reviwer', id],
    queryFn: () => (id ? getTaskByCourseId(id) : Promise.resolve(null)),
    enabled: !!id,
  });
};

// Hook to  Create course plans
export const useCreateCoursePlanAi = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: createCourseAi,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useCreateCoursePlanFile = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: createCourseFile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to add new  topic to the course

export const useAddTopic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: any }) => addNewTopic(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
      notify.success(t('notify.topicCreated'));
    },
    onError: (error: any) => {
      notify.error(JSON.stringify(error));
    },
  });
};

export const useGenerateAllDnasInTopic = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  const { notify } = useNotify();
  return useMutation({
    mutationFn: generateTopicDna,
    onSuccess: () => {
      queryClient.invalidateQueries();
      // notify.success(t('notify.danGenerated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to delete a Courses
export const useDeleteCourses = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteCourses,
    onSuccess: () => {
      notify.success('Courses Deleted Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteCourse = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteCourse,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useMarkCourseAsReadyForReview = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: markCourseAsReadyForReview,
    onSuccess: () => {
      notify.success('Course marked as ready for review!');
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    },
    onError: (error: any) => {
      notify.error(error?.message);
    },
  });
};
export const useApproveCoursesInReview = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveCoursesInReview,
    onSuccess: () => {
      notify.success('Task marked as approved!');
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      notify.error(error?.message);
    },
  });
};
export const useApproveCoursesInEdit = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveCoursesInEdit,
    onSuccess: () => {
      notify.success(t('courseEdit.approveCourseSuccess'));
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
    onError: (error: any) => {
      notify.error(error?.message);
    },
  });
};

export const useEditCourseTitle = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editCourseTitle,
    onSuccess: () => {
      notify.success('Course Title updated successfully!');
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error?.message);
    },
  });
};

export const useMarkCourseAsApproved = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: markCourseAsApproved,
    onSuccess: () => {
      notify.success('Course Title updated successfully!');
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    },
    onError: (error: any) => {
      notify.error(error?.message);
    },
  });
};

export const useUpdateCourse = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateCourse,
    onSuccess: () => {
      notify.success('Course Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
