import { useQuery } from '@tanstack/react-query';
import { verifyEmail } from './endpoint';
export const useVerifyEmail = (payload: { id: string; hash: string }) => {
  return useQuery({
    queryKey: ['verify-email', payload.id, payload.hash],
    queryFn: () => verifyEmail(payload),
    retry: 1,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 0,
  });
};
