import { useMemo, useState } from 'react';
import { Icon, ProtectedComponent, useConfirmDialog, useNotify } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useDeleteDna, useGenarateDna, useGetDnaById } from '@/modules/app/dna/apis/queries';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { IDNAListItem } from '@/modules/app/dna/types';
import { getContentStatusStyle } from '@/utils/helpers';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import LanguageLink from '@/components/language-link';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import EditDna from '@/modules/app/components/edit-dna';
import ChangeData from '@/modules/app/dna/components/change-title-dialog';
import { useQueryClient } from '@tanstack/react-query';
import { useLocation, useNavigate, useParams } from 'react-router-dom';

const DnaTable = () => {
  // State
  const [editDialog, setEditDialog] = useState<boolean>(false);
  const [selectedDna, setSelectedDna] = useState<any>(null);
  const [changeDataDialog, setChangeDataDialog] = useState<boolean>(false);

  // Hooks
  const { data: allmetaData } = useGetSingleMetadata('');
  const { dnaId } = useParams();
  const { data: dna } = useGetDnaById(dnaId || '');
  const { mutate, isPending } = useGenarateDna();
  const { t, i18n } = useTranslation();
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { mutate: deleteDna } = useDeleteDna();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const location = useLocation();
  // drived
  const bloomTakOptions = allmetaData?.filter((item) => item.type === 'bloom"s_taxonomy') || [];

  const handleGenerateDna = async () => {
    mutate(dna?.id || '', {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ['dna'] });
        notify.success(t('notify.dnaGenerated'));
      },
      onError: () => {
        notify.error(t('notify.dnaGenerationFailed'));
      },
    });
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeleteDna = async (dnaId: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        try {
          hideConfirm();
          deleteDna(dnaId, {
            onSuccess: () => {
              const myLocation = location.pathname.replace(`/${dnaId}`, '');
              navigate(`${myLocation}`);
              notify.success('DNA deleted successfully');
            },
          });
        } catch (error) {
          notify.error('An error occurred. Please try again.');
        }
      },
    });
  };

  const columns: ITableColumn<IDNAListItem>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('dnaConentPgae.table.dnaTitle'),
        width: '350px',
        cell: ({ row }) => {
          const element = !row.dna_content ? (
            <span>{row.title}</span>
          ) : (
            <LanguageLink to={`/app/my-content/DNAs/${row?.id}`} className="text-primary  underline cursor-pointer">
              {row.title}
            </LanguageLink>
          );
          return (
            <div className="ms-10 rtl:text-right group transition-opacity">
              <div className="flex gap-2 items-center p-0">
                <div className={`break-words font-medium `}>{element}</div>
                <div className=" opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform group-hover:translate-y-0 translate-y-1">
                  <Icon
                    onClick={() => {
                      setSelectedDna(row);
                      setChangeDataDialog(true);
                    }}
                    icon="basil:edit-outline"
                    width={25}
                    className="cursor-pointer text-primary"
                  />
                </div>
              </div>
              <p className="opacity-50">{row.learning_objectives}</p>
            </div>
          );
        },
      },

      {
        accessorKey: 'audio_length',
        header: t('dnaConentPgae.table.length'),
        width: '100px',
      },
      {
        accessorKey: 'bloom_tax',
        header: t('CoursePlanCreationPage.form.audience'),
        width: '150px',
        cell: ({ row }) => {
          return (
            <div className="rtl:text-right">
              {i18n.language === 'en' ? row?.bloom_tax?.name_en : row?.bloom_tax?.name_ar}
            </div>
          );
        },
      },
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '120px',
        cell: ({ row }) => getContentStatusStyle(row?.dna_status),
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '150px',
        cell: ({ row }) => {
          return (
            <div className="flex gap-2 ">
              <ConditionalComponent status={row.dna_status} wantedStatus={StatusClass.DNA.EDIT.NO_CONTENT}>
                {isPending ? (
                  <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary" width={25} />
                ) : (
                  <Button onClick={handleGenerateDna} className="flex items-center gap-2 min-w-[100px]" type="button">
                    {t('generate')}
                    <Icon icon="mdi:stars-outline" width={20} />
                  </Button>
                )}
              </ConditionalComponent>
              <div className="flex flex-row-reverse gap-2 items-center">
                <ConditionalComponent
                  status={row.course_status}
                  operator="not"
                  wantedStatus={StatusClass.COURSE.EDIT.RFR}
                >
                  <ConditionalComponent
                    status={row.dna_status}
                    wantedStatus={[
                      StatusClass.DNA.EDIT.NO_CONTENT,
                      StatusClass.DNA.EDIT.DRAFT,
                      StatusClass.DNA.EDIT.FEEDBACK,
                      StatusClass.DNA.EDIT.RFR,
                    ]}
                  >
                    <ProtectedComponent requiredPermissions={'dna_delete'}>
                      <Icon
                        onClick={() => handleDeleteDna(row?.id)}
                        icon="gg:trash"
                        width="25"
                        className="text-red-500 cursor-pointer"
                      />
                    </ProtectedComponent>
                  </ConditionalComponent>
                  <ConditionalComponent
                    status={row.dna_status}
                    wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK, StatusClass.DNA.EDIT.RFR]}
                  >
                    <ProtectedComponent requiredPermissions={'dna_edit'}>
                      <Icon
                        onClick={() => {
                          setEditDialog(true), setSelectedDna(row);
                        }}
                        icon="basil:edit-outline"
                        width={25}
                        className="text-primary cursor-pointer"
                      />
                    </ProtectedComponent>
                  </ConditionalComponent>
                </ConditionalComponent>
              </div>
            </div>
          );
        },
      },
    ];
  }, [t, bloomTakOptions]);

  return (
    <>
      {dna && (
        <Table rows={[dna]} columns={columns}>
          <TableContent>
            <TableContentBody />
          </TableContent>
        </Table>
      )}

      {editDialog && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false), setSelectedDna(null);
          }}
          data={selectedDna}
        />
      )}

      {changeDataDialog && (
        <ChangeData
          data={selectedDna}
          onOpen={changeDataDialog}
          onOpenChange={() => {
            setChangeDataDialog(false), setSelectedDna(null);
          }}
        />
      )}
    </>
  );
};

export default DnaTable;
