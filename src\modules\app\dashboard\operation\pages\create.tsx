import { Form, TextInput, useForm, useValidate, Textarea } from '@/index';
import { MultiSelect } from '@/components/form/inputs/multi-select-combo-box';
import { Button } from '@/components/ui/button';
import { useGetPermissions } from '@/modules/app/roles/apis/queries';
import { useTranslation } from 'react-i18next';
import { useCreateOperation } from '@/modules/app/dashboard/operation/apis/queries';

const OperationCreate = () => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { mutate: createRole, isPending } = useCreateOperation();
  const { data } = useGetPermissions();
  const { t } = useTranslation();
  const permissionList = data?.map((permission) => ({ value: permission.id, label: permission.name }));

  //Form
  const { form, setFieldValue } = useForm({
    name: '',
    description: '',
    permissions: null,
  });
  // Functions
  const handleGenerate = async () => {
    createRole(form);
  };
  return (
    <div>
      <Form onSubmit={handleGenerate}>
        <div className="justify-between grid grid-cols-4 gap-6 ">
          <div className="col-span-3 lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name"
              label={t('name')}
              placeholder={t('userPage.operationName')}
              value={form.name}
              onChange={setFieldValue('name')}
              isRequired
              validators={[isRequired()]}
            />
            <Textarea
              name="description"
              label={t('description')}
              value={form.description}
              onChange={setFieldValue('description')}
              isRequired
              validators={[isRequired()]}
            />
            <MultiSelect
              name="permissions"
              label={t('userPage.permissions')}
              options={permissionList || []}
              onChange={setFieldValue('permissions')}
              value={form.permissions || []}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={50}
              validators={[isNotEmptyArray()]}
            />
          </div>
          <div className=" col-span-1 flex items-start gap-2 mt-[16px]">
            <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
              {t('create')}
            </Button>
            {/* <Button disabled={isPending} className=" mt-4" variant={'outline'} onClick={handleClear}>
              Clear
            </Button> */}
          </div>
        </div>
      </Form>
    </div>
  );
};

export default OperationCreate;
