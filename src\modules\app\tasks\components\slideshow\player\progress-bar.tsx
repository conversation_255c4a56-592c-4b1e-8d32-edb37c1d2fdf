import React from 'react';
import { Too<PERSON><PERSON>, Toolt<PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useAtom } from 'jotai';
import { currentTimeAtom } from '../../../store';
import { ISlide } from '@/modules/app/dna/apis/slideshow/types';

interface audioTracks {
  url: string;
  duration: number;
  startTime: number;
  index: number;
}

const ProgressBar = ({
  navigateToTrack,
  audioTracks,
  totalDuration,
  slides,
}: {
  navigateToTrack: (index: number, time: number) => void;
  audioTracks: audioTracks[];
  totalDuration: number;
  slides: ISlide[];
}) => {
  const [currentTime] = useAtom(currentTimeAtom);
  const findTrackIndex = (audioTracks: audioTracks[], timeClicked: number): number => {
    let low = 0;
    let high = audioTracks.length - 1;

    while (low <= high) {
      const mid = Math.floor((low + high) / 2);
      const track = audioTracks[mid];
      const endTime = track.startTime + track.duration;

      if (timeClicked >= track.startTime && timeClicked < endTime) {
        return mid;
      } else if (timeClicked < track.startTime) {
        high = mid - 1;
      } else {
        low = mid + 1;
      }
    }

    return -1;
  };
  const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();

    const ProgressBar = e.currentTarget;
    const rect = ProgressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const timeClicked = clickPosition * totalDuration;

    const clickedTrackIndex = findTrackIndex(audioTracks, timeClicked);

    if (clickedTrackIndex !== -1) {
      const timeWithinTrack = timeClicked - audioTracks[clickedTrackIndex].startTime;
      navigateToTrack(clickedTrackIndex, timeWithinTrack);
    }
  };

  return (
    <div
      className="w-full h-2 bg-gray-200 rounded relative cursor-pointer group"
      onClick={(e) => handleProgressBarClick(e)}
    >
      <div
        className="h-full bg-primary rounded absolute top-0 left-0"
        style={{ width: `${(currentTime / totalDuration) * 100}%` }}
      />
      {audioTracks.map((track, index) => {
        const startPosition = (track.startTime / totalDuration) * 100;
        const endPosition = ((track.startTime + track.duration) / totalDuration) * 100;
        const width = endPosition - startPosition;
        return (
          <div key={track.index}>
            <TooltipProvider delayDuration={200}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div
                    className="absolute top-0 h-full hover:bg-primary/50 transition-colors"
                    style={{
                      left: `${startPosition}%`,
                      width: `${width}%`,
                    }}
                  />
                </TooltipTrigger>
                <TooltipContent className="gap-2 p-0 border-0 shadow-none bg-background">
                  <div className="self-center w-60 h-32 rounded-lg border border-border">
                    <div
                      className="h-full w-full flex justify-center items-center py-2 scale-[0.25] origin-top-left transform [&_.ck-content]:space-y-7 [&_.ck-content]:flex [&_.ck-content]:items-center [&_.ck-content]:justify-center rounded-md custom-slideshow-container"
                      style={{ width: '400%', height: '400%' }}
                      dangerouslySetInnerHTML={{ __html: slides[track.index].content }}
                    />
                  </div>
                  <div className="w-full mb-4 bg-background text-center">
                    <p className="text-sm px-3 py-1 rounded-md">
                      {slides[track.index].title || `Slide ${track.index + 1}`}
                    </p>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        );
      })}
      {audioTracks.map((track, index) => {
        if (index === 0) return null;
        const position = (track.startTime / totalDuration) * 100;
        return (
          <div
            key={`divider-${index}`}
            className="absolute top-0 h-full w-2 bg-background"
            style={{ left: `${position}%` }}
          />
        );
      })}
    </div>
  );
};

export default ProgressBar;
