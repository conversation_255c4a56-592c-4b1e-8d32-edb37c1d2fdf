import authRoutes from '@/modules/auth/routes';
import appRoutes from '@/modules/app/routes';
import { Navigate } from 'react-router-dom';
import AppWrapper from './RouterProvider';
import EmailVerification from '@/modules/email-verification/routes';
import SlideShow from '../modules/app/slideshow/routes';
import { lazyWithRetry } from '@/utils/lazy-retry';

const EmbedSlideshow = lazyWithRetry(() => import('@/modules/embed/slideshow-player/page'));
const NotAuthorizedPage = lazyWithRetry(() => import('../NotAuthorizedPage'));
const RedirectToLanguage = lazyWithRetry(() => import('@/RedirectToLangugae'));

const lng = localStorage.getItem('i18nextLng') || 'en';
const routes = [
  {
    path: '/',
    element: <Navigate to={`/${lng}`} replace />,
  },

  ...EmailVerification,
  ...SlideShow,
  {
    path: 'embed/slideshow/:uuid',
    element: <EmbedSlideshow />,
  },
  {
    path: '/:lng',
    element: <AppWrapper />,
    children: [
      {
        path: '',
        element: <Navigate to="app" replace />,
      },

      // Include app and auth routes
      ...appRoutes,
      ...authRoutes,
    ],
  },

  { path: '/:lng/403', element: <NotAuthorizedPage /> },
  { path: '*', element: <RedirectToLanguage /> },
  // Add this to your existing routes
];

export default routes;
