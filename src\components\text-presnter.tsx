import React from 'react';

const TextPresenter: React.FC<{ text: string }> = ({ text }) => {
  // Function to detect Arabic characters
  const isArabic = (text: string) => /[\u0600-\u06FF]/.test(text);

  // Determine the text direction
  const direction = isArabic(text) ? 'rtl' : 'ltr';

  if (!text) return null;

  return (
    <div
      className={text?.includes('ul') ? '[&_ul]:list-disc [&_ul]:list-inside text-start inline-block' : 'inline-block'}
      style={{ direction }}
      dangerouslySetInnerHTML={{ __html: text }}
    ></div>
  );
};

export { TextPresenter };
