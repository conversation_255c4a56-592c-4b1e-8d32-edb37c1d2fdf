{"name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "cancel": "Cancel", "update": "Update", "create": "Create", "edit": "Edit", "save": "Save", "activate": "Activate", "generate": "Generate", "submit": "Submit", "clear": "Clear", "delete": "Delete", "analyze": "Analyze", "Approved": "Approved ", "Draft": "Draft", "No DNA": "No DNA ", "Reported": "Reported", "createActivity": "Create Activity", "Web": "Web", "URL": "URL", "File": "File", "Text": "Text", "Youtube URL": "Youtube URL", "uploadFile": "Upload file", "Method 1": "Method 1", "Method 2": "Method 2", "Arabic": "Arabic", "Education": "Education", "Tourism": "Tourism", "Energy": "Energy", "Mining and minerals": "Mining and minerals", "Industry and manufacturing": "Industry and manufacturing", "Transportation and logistics services": "Transportation and logistics services", "Financial services": "Financial services", "Health care": "Health care", "Sports": "Sports", "Law": "Law", "Entertainment": "Entertainment", "Art": "Art", "Design": "Design", "Real estate": "Real estate", "Human capital": "Human capital", "innovation": "innovation", "Environmental services": "Environmental services", "Chemicals": "Chemicals", "Pharmaceuticals and biotechnology": "Pharmaceuticals and biotechnology", "Agriculture": "Agriculture", "Food industries": "Food industries", "Aviation and defence": "Aviation and defence", "Information and Communication Technology": "Information and Communication Technology", "Business Management": "Business Management", "Retail and wholesale trade": "Retail and wholesale trade", "Other (write a subject)": "Other (write a subject)", "Early childhood education": "Early childhood education", "Primary education": "Primary education", "Intermediate education": "Intermediate education", "Secondary education": "Secondary education", "Undergraduate": "Undergraduate", "Postgraduate": "Postgraduate", "Professionals": "Professionals", "Remembering": "Remembering", "Understanding": "Understanding", "Applying": "Applying", "Analyzing": "Analyzing", "beginner": "beginner", "intermediate": "intermediate", "advanced": "advanced", "notify.login": "Login successful!", "notify.dnaCreated": "DNA Data Created!", "notify.danGenerated": "DNA Generated!", "notify.topicCreated": "Topic Created!", "notify.dnaDeleted": "DNA deleted successfully", "notify.dnaApprovred": "DNA approved successfully", "notify.dnaReported": "DNA Reported Successfully!'", "notify.coursePlaneCreated": "Course Plan Created Successfully!", "notify.topicDelect": "Topic deleted successfully", "notify.activityCreated": "Activity Created!", "notify.xmlGenerated": "XML generated successfully!", "table.pagination.showing": "Showing", "table.pagination.of": "Of", "table.pagination.next": "Next", "table.pagination.previous": "Previous", "confirmDialog.confirm": "Yes, i'm sure", "confirmDialog.cancel": "No, Cancel", "tooltip.approved": "Approved", "tooltip.reported": "Reported", "tooltip.draft": "Draft", "tooltip.notApproved": "Not Approved", "editDnaModal.title": "Edit DNA", "editDnaModal.word": "Word", "editDnaModal.version": "Version", "editDnaModal.saveDna": "Save DNA", "editDnaModal.approve": "Approve", "editDnaModal.report": "Report", "editDnaModal.download": "Download", "activityModal.title": "Generate Activity", "activityModal.trueOrFalse": "True or False", "activityModal.multipleChoiceQuestions": "Multiple Choice Questions", "activityModal.fillintheBlank": "Fill in the Blank", "activityModal.caseStudy": "Case Study", "approveDnaModal": {}, "reportDnaMOdal.title": "Report DNA", "reportDnaMOdal.reportReason": "Report Raseon", "breadcrumb.toolsPage.tools": "Tools", "breadcrumb.toolsPage.dnaCreation": "DNA Creation", "breadcrumb.toolsPage.topicCreation": "Topic Creation", "breadcrumb.toolsPage.coursePlan": "Course Plan", "breadcrumb.toolsPage.fileAnalyzer": "File Analyzer", "breadcrumb.myContentPage.MyContent": "My Content", "breadcrumb.myContentPage.DNAs": "DNAs", "breadcrumb.myContentPage.topic": "Topics", "breadcrumb.myContentPage.course": "Courses", "breadcrumb.myContentPage.DNA": "DNA", "breadcrumb.userPage.roles": "Roles", "breadcrumb.userPage.role": "Role", "breadcrumb.userPage.users": "Users", "breadcrumb.userPage.user": "User", "breadcrumb.userPage.userCreation": "User Creation", "breadcrumb.dashboard.keys": "Keys", "breadcrumb.dashboard.roles": "Roles", "breadcrumb.termsPage.terms": "Terms And Conditions", "breadcrumb.dashboard": "Dashboard  ", "theme.light": "Light", "theme.dark": "Dark", "theme.system": "System", "userIcon.signOut": "Sign out", "sidebar.tools": "Tools", "sidebar.users": "Users", "sidebar.roles": "Roles", "sidebar.myContent": "My Content", "sidebar.dashboard": "Dashboard", "sidebar.terms": "Terms And Conditions", "login.title": "Content Assistant", "login.cardTitle": "Log in to your account", "login.cardLogin": "Log In", "login.cardRegister": "Don't have an account?", "login.cardSignUp": "Sign up here", "signUp.title": "Create Your Account", "signUp.cardLogin": "Log in here", "signUp.cardSiginIn": "Already have an account?", "signUp.cardSignUp": "Create your account", "dnaCreationPage.title": "DNA Creation", "dnaCreationPage.confirmationText": "The generated DNA will be deleted. Are you sure you want to continue?", "dnaCreationPage.reGenerate": "Regenerate", "dnaCreationPage.generateDna": "Generate DNA", "dnaCreationPage.startOver": "Start over and generate a new DNA", "dnaCreationPage.learningObjective": "learning objective", "dnaCreationPage.actions": "Actions", "dnaCreationPage.version": "Version", "dnaCreationPage.word": "word", "dnaCreationPage.report": "Report", "dnaCreationPage.updateDna": "Update DNA", "dnaCreationPage.reportDNA": "Report DNA", "dnaCreationPage.reportReason": "Report Reason", "dnaCreationPage.form.title": "Title", "dnaCreationPage.form.subject": "Subject", "dnaCreationPage.form.otherSubject": "Other Subject", "dnaCreationPage.form.audience": "Audience", "dnaCreationPage.form.bloom'sTaxonomy": "<PERSON>'s Taxonomy", "dnaCreationPage.form.language": "Language", "dnaCreationPage.form.aiBaseModel": "Ai Base Model", "dnaCreationPage.form.contentLength": "Content Length", "dnaCreationPage.form.context": "Context", "dnaCreationPage.form.sources": "Sources", "dnaCreationPage.form.contextPlaceholder": "(Optional) Give the model hints to get exactly what you’re looking for", "dnaCreationPage.form.text": "Enter Text", "topicCreationPage.title": "Topic Creation", "topicCreationPage.learningLevels": "Learning Levels", "topicCreationPage.confirmationText": "The generated Topic will be deleted. Are you sure you want to continue?", "topicCreationPage.reGenerate": "Regenerate DNA", "topicCreationPage.startOver": "Start over and generate a Topic", "topicCreationPage.addNew": "Add another DNA Titile", "topicCreationPage.addNewPlavehodler": "Write a title for the missing DNA", "topicCreationPage.addNewButton": "Add Titile", "topicCreationPage.form.title": "Title", "topicCreationPage.form.subject": "Subject", "topicCreationPage.form.audience": "Audience", "topicCreationPage.form.language": "Language", "topicCreationPage.form.context": "Context", "topicCreationPage.form.contextPlaceholder": "(Optional) Give the model hints to get exactly what you’re looking for", "topicCreationPage.dnaTable.status": "Status", "topicCreationPage.dnaTable.dnaTitle": "DNA Title", "topicCreationPage.dnaTable.learningObjective": "learning Objective", "topicCreationPage.dnaTable.level": "Level", "topicCreationPage.dnaTable.lenght": "<PERSON><PERSON>", "topicCreationPage.dnaTable.actions": "Actions", "CoursePlanCreationPage.title": "Course Plan (Underdevelopment)", "CoursePlanCreationPage.confirmationText": "The generated Cousre will be deleted. Are you sure you want to continue?", "CoursePlanCreationPage.reGenerate": "Regenerate", "CoursePlanCreationPage.startOver": "Start over and generate a Course", "CoursePlanCreationPage.add": "Add", "CoursePlanCreationPage.addDna": "Add another DNA to this unit +", "CoursePlanCreationPage.addTopic": "(Optional) please add any additional details for the next unit", "CoursePlanCreationPage.form.title": "What do you whant to teach?", "CoursePlanCreationPage.form.titlePLaceholder": "Write an overview of treatment, goals, learning outcomes, skills and which ones you want to take into account.", "CoursePlanCreationPage.form.subject": "Subject", "CoursePlanCreationPage.form.audience": "Audience", "CoursePlanCreationPage.form.language": "Language", "CoursePlanCreationPage.form.difficultyLevel": "Difficulty Level", "CoursePlanCreationPage.table.status": "Status", "CoursePlanCreationPage.table.title": "Title", "CoursePlanCreationPage.table.dnas": "DNAs", "CoursePlanCreationPage.table.lenght": "Length", "CoursePlanCreationPage.table.bloomTax": "Bloom Taxonomy", "CoursePlanCreationPage.table.actions": "Actions", "fileAnalyzer.title": "File Analyzer (Underdevelopment)", "fileAnalyzer.engine": "Engine", "fileAnalyzer.engineProment1": "Engine 1: GPT-4 API Summary Engine. This engine leverages OpenAI's GPT-4 API to quickly summarize the content of files and URLs into main points. It is designed for speed, providing fast and efficient summaries, though it may omit some finer details in the process.", "fileAnalyzer.engineProment2": "Engine 2: LangChain-Based Embedding & Summary Engine. This engine uses LangChain to break down documents, URLs, and YouTube videos into chunks, convert them into embeddings, and manually summarize each chunk. While it takes more time, it delivers detailed and context-aware summaries, retaining more of the original content's nuances.", "programCreation.title": "Program Plan (Soon)", "contentRewrite.title": "Content Rewrite (Soon)", "userPage.dialogHeaders.editUser": "Edit User", "userPage.dialogHeaders.updatePassword": "Update Password", "userPage.account.status": "Account Status", "userPage.account.active": "Active", "userPage.account.blocked": "Blocked", "userPage.role": "Role", "userPage.roleName": "Role Name", "userPage.permissions": "permissions", "userPage.permissionsName": "Select permissions", "userPage.single.acticatedDate": "Actions Date", "userPage.single.content": "Content", "userPage.single.viewContent": "View my contnet", "userPage.single.Underdevelopment": "underdevelopment", "userPage.single.generated": "Generated", "userPage.single.approved": "Approved", "userPage.filters.search": "Search by <PERSON><PERSON>", "userPage.filters.roleSearch": "Search by name", "userPage.filters.status": "Filter users by status", "userPage.filters.role": "Filter users by role", "userPage.filters.reset": "Reset", "userPage.tabs.users": "Users", "userPage.tabs.roles": "Roles", "userPage.buttons.headerButton": "Create New User", "userPage.buttons.headerRoleButton": "Create New Role", "userPage.buttons.accountActivate": "Activate", "userPage.buttons.accountDeActivate": "Deactivate", "userPage.buttons.createUser": "Create User", "userPage.buttons.updateUser": "Update User", "userPage.accountStatus.blocked": "blocked", "userPage.accountStatus.pending": "pending", "userPage.accountStatus.active": "active", "userPage.table.id": "ID", "userPage.table.name": "Name", "userPage.table.email": "Email", "userPage.table.role": "Role", "userPage.table.lessons": "Lessons", "userPage.table.date": "Join Date", "userPage.table.status": "Status", "userPage.table.permissions": "Permissions", "userPage.table.actions": "Actions", "dnaConentPgae.createNewDna": "Create New Dna", "dnaConentPgae.searchPlaceholder": "Search by DNA", "dnaConentPgae.statusFilter": "Filter DNAs by <PERSON><PERSON><PERSON>er", "dnaConentPgae.userFIlter": "Filter DNAs by users", "dnaConentPgae.bulkActions.title": "Bulk Actions", "dnaConentPgae.bulkActions.approveDna": "Approve DNAs", "dnaConentPgae.bulkActions.approveDnaText": "Approved DNAs will be moved to the production phase. Are you sure you want to proceed?", "dnaConentPgae.bulkActions.exportExcel": "Export to Excel", "dnaConentPgae.bulkActions.exportExcelText": "This action will export an Excel Sheet, Would you like to continue?", "dnaConentPgae.bulkActions.createActivite": "Create Activites", "dnaConentPgae.bulkActions.createActiviteText": "Note that this action will take some time and it will create 2 questions per type, Do you want to continue?", "dnaConentPgae.tabs.dnas": "DNAs", "dnaConentPgae.tabs.topics": "Topics", "dnaConentPgae.tabs.courses": "Courses", "dnaConentPgae.statistics.title": "Statistics", "dnaConentPgae.statistics.allDna": "All DNA", "dnaConentPgae.statistics.approved": "Approved", "dnaConentPgae.statistics.danGenerated": "DNA Generated", "dnaConentPgae.statistics.reported": "Reported", "dnaConentPgae.table.status": "Status", "dnaConentPgae.table.dnaTitle": "DNA Title", "dnaConentPgae.table.topic": "Topic", "dnaConentPgae.table.subject": "Subject", "dnaConentPgae.table.level": "Level", "dnaConentPgae.table.length": "Length", "dnaConentPgae.table.date": "Date", "dnaConentPgae.table.author": "Author", "dnaConentPgae.table.actions": "Actions", "topicContentPage.createNewtopic": "Create new unit", "topicContentPage.searchPlaceholder": "Search by DNA", "topicContentPage.statusFilter": "Filter DNAs by <PERSON><PERSON><PERSON>er", "topicContentPage.userFIlter": "Filter DNAs by users", "topicContentPage.table.status": "Status", "topicContentPage.table.title": "Title", "topicContentPage.table.subject": "Subject", "topicContentPage.table.audience": "Audience", "topicContentPage.table.language": "Language", "topicContentPage.table.source": "source", "topicContentPage.table.date": "Date ", "topicContentPage.table.author": "Author", "topicContentPage.table.actions": "Actions", "cousrePlanContentPage.createNewtopic": "Create new course plan", "cousrePlanContentPage.searchPlaceholder": "Search by DNA", "cousrePlanContentPage.statusFilter": "Filter DNAs by <PERSON><PERSON><PERSON>er", "cousrePlanContentPage.userFIlter": "Filter DNAs by users", "cousrePlanContentPage.courseDeleteText": "The generated Courses will be deleted. Are you sure you want to continue?", "cousrePlanContentPage.table.status": "Status", "cousrePlanContentPage.table.title": "Title", "cousrePlanContentPage.table.subject": "Subject", "cousrePlanContentPage.table.audience": "Audience", "cousrePlanContentPage.table.language": "Language", "cousrePlanContentPage.table.level": "Level", "cousrePlanContentPage.table.date": "Date ", "cousrePlanContentPage.table.author": "Author", "cousrePlanContentPage.table.actions": "Actions", "dnaSinglePage.unApprove": "Unapprove DNA and discard related data", "dnaSinglePage.expand": "Expand Content", "dnaSinglePage.hide": "Hide Content", "dnaSinglePage.tabs.soon": "Soon", "dnaSinglePage.tabs.metaData": "<PERSON><PERSON><PERSON>", "dnaSinglePage.tabs.activity": "Activity", "dnaSinglePage.tabs.slideShow": "slideshow", "dnaSinglePage.tabs.reading": "Reading", "dnaSinglePage.tabs.podcast": "Podcast", "dnaSinglePage.tabs.video": "Video", "dnaSinglePage.metaData.description": "Description", "dnaSinglePage.metaData.length": "Length", "dnaSinglePage.metaData.subject": "Subject", "dnaSinglePage.metaData.course": "Course", "dnaSinglePage.metaData.topic": "Topic", "dnaSinglePage.metaData.learning objective": "Learning Objective", "dnaSinglePage.metaData.learning level": "Learning Level", "dnaSinglePage.metaData.language": "Language", "dnaSinglePage.metaData.source": "Source", "dnaSinglePage.metaData.context": "Context", "dnaSinglePage.metaData.procedures": "Procedures", "dnaSinglePage.metaData.resources": "Resources", "dnaSinglePage.metaData.keywords": "Keywords", "dnaSinglePage.metaData.created in": "Created In", "dnaSinglePage.metaData.created by": "Created By", "dnaSinglePage.metaData.approved in": "Approved In", "dnaSinglePage.metaData.approved by": "Approved By", "dnaSinglePage.metaData.audio created in": "Audio Created In", "dnaSinglePage.metaData.audio created by": "Audio Created By", "dnaSinglePage.metaData.audio file": "Audio File", "dnaSinglePage.metaData.script created in": "Script Created In", "dnaSinglePage.metaData.script created by": "Script Created By", "dnaSinglePage.activity.generateXml": "XML Generated ", "dnaSinglePage.activity.copyXml": "  Copy XML", "dnaSinglePage.activity.generateActivity": "Generate Activity", "dnaSinglePage.activity.noActivity": "There is no activity generated to this lesson yet.", "dnaSinglePage.activity.answer": "Answer", "dnaSinglePage.activity.explanation": "Explanation", "dnaSinglePage.activity.trueOrFalse": "True or False", "dnaSinglePage.activity.multipleChoiceQuestions": "Multiple Choice Questions", "dnaSinglePage.activity.fillintheBlank": "Fill in the Blank", "dnaSinglePage.activity.caseStudy": "Case Study", "dnaSinglePage.slideShow.generate": "Generate Slideshow", "dnaSinglePage.slideShow.noSlide": "There is no slideshow generated to this lesson yet.", "dnaSinglePage.slideShow.reGenerate": "Regenerate", "dnaSinglePage.slideShow.playSlide": " play slideshow", "dnaSinglePage.slideShow.slide": "Slide", "dnaSinglePage.slideShow.textInTheScreen": "Text in the scene", "dnaSinglePage.slideShow.title": "Title", "dnaSinglePage.slideShow.introduction": "Introduction", "dnaSinglePage.slideShow.voiceOver": "Voice Over", "dnaSinglePage.slideShow.mainIdeas": "Main Ideas", "dnaSinglePage.slideShow.closingStatement": "Closing Statement", "dnaSinglePage.slideShow.keyTakeaways": "Key Takeaways", "dnaSinglePage.slideShow.updateVoiceOver": "Update Voice Over", "dnaSinglePage.slideShow.generateVoiceOver": "Generate Voice Over", "dashboardPage.tabs.keys": "Keys", "dashboardPage.keys.createNew": "Create New Key", "dashboardPage.keys.search": "Search By Value", "dashboardPage.keys.table.keys": "Keys", "dashboardPage.keys.table.ar": "Arabic", "dashboardPage.keys.table.en": "English", "dashboardPage.keys.newKeyDialog.title": "Add New Key", "dashboardPage.keys.newKeyDialog.key": "Key", "dashboardPage.keys.newKeyDialog.value": "Value", "dashboardPage.table.actions": "Actions", "dashboardPage.tabs.roles": "Roles", "newtitle": "New Title"}