import { useState, useEffect } from 'react';
import { Form, useForm, useValidate, Textarea, TextInput, DnaEnums } from '@/index';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { generateEnum } from '@/utils/helpers';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { useSearchParams } from 'react-router-dom';
const CoursePlanAiForm = ({ onSubmit, courseData, id, error, counterToResetForm }: any) => {
  const [searchParams, setSearchParams] = useSearchParams();
  // State
  const { data: allmetaData } = useGetSingleMetadata('');
  const subjectOptions = allmetaData?.filter((item) => item.type === 'subject') || [];
  const audienceOptions = allmetaData?.filter((item) => item.type === 'audience') || [];
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];
  const learningStrategyOptions = allmetaData?.filter((item) => item.type === 'learning_strategy') || [];
  const learningTypeOptions = allmetaData?.filter((item) => item.type === 'learning_type') || [];
  const difficultyLevelOptions = allmetaData?.filter((item) => item.type === 'difficulty_level') || [];
  const creditHoursOptions = allmetaData?.filter((item) => item.type === 'credit_hours') || [];

  const { isRequired } = useValidate();
  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const initialState = {
    version: 'v.1.02',
    info: '',
    subject_id: '',
    audience_id: '',
    language_id: '',
    difficulty_level_id: '',
    learning_strategy_id: '',
    learning_type_id: '',
    credit_hours_id: '',
    model: 'gpt-4.1',
  };
  const { form, setFieldValue, setFormValue } = useForm(initialState);

  useEffect(() => {
    if (courseData) {
      setFormValue({
        version: 'v.1.02',
        info: courseData?.info,
        subject_id: courseData?.subject.id,
        audience_id: courseData?.audience.id,
        language_id: courseData?.language.id,
        level: courseData?.level,
        difficulty_level_id: courseData?.difficultyLevel.id,
        learning_strategy_id: courseData?.learningStrategy.id,
        learning_type_id: courseData?.learningType.id,
        credit_hours_id: courseData?.creditHours.id,
        model: courseData?.model,
      });
    }
  }, [courseData]);

  const handleCoursePlanFormSubmittion = async (event: any) => {
    event.preventDefault();
    onSubmit(form, {
      onSuccess: (data: any) => {
        setSearchParams({ courseId: data.id, tab: 'ai-form' }, { replace: true });
      },
    });
  };

  useEffect(() => {
    if (counterToResetForm) {
      setFormValue(initialState);
    }
  }, [counterToResetForm]);

  return (
    <Form id={id} onSubmit={handleCoursePlanFormSubmittion}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 lg:block space-y-4" dir={i18n.dir()}>
        <Textarea
          name="info"
          label={t('CoursePlanCreationPage.form.title')}
          placeholder={t('CoursePlanCreationPage.form.titlePLaceholder')}
          rows={6}
          value={form.info}
          onChange={setFieldValue('info')}
          isRequired
          validators={[isRequired()]}
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.subject')}
          name="subject_id"
          label={t('CoursePlanCreationPage.form.subject')}
          options={generateEnum(subjectOptions, 'id', labelKey)}
          value={form.subject_id}
          onChange={setFieldValue('subject_id')}
          validators={[isRequired()]}
        />
        {form.subject === 'Other (write a subject)' && (
          <TextInput
            name="otherSubject"
            label="Other Subject"
            placeholder="Enter other subject"
            value={form.other_subject}
            onChange={setFieldValue('other_subject')}
            isRequired
            validators={[isRequired()]}
          />
        )}
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.audience')}
          name="audience_id"
          label={t('CoursePlanCreationPage.form.audience')}
          options={generateEnum(audienceOptions, 'id', labelKey)}
          value={form.audience_id}
          onChange={setFieldValue('audience_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.learningType')}
          name="learningType_id"
          label={t('CoursePlanCreationPage.form.learningType')}
          options={generateEnum(learningTypeOptions, 'id', labelKey)}
          value={form.learning_type_id}
          onChange={setFieldValue('learning_type_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.learningStrategy')}
          name="learningStrategy"
          label={t('CoursePlanCreationPage.form.learningStrategy')}
          options={generateEnum(learningStrategyOptions, 'id', labelKey)}
          value={form.learning_strategy_id}
          onChange={setFieldValue('learning_strategy_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.language')}
          name="language_id"
          label={t('CoursePlanCreationPage.form.language')}
          options={generateEnum(languageOptions, 'id', labelKey)}
          value={form.language_id}
          onChange={setFieldValue('language_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.creditHours')}
          name="credit_hours_id"
          label={t('CoursePlanCreationPage.form.creditHours')}
          options={generateEnum(creditHoursOptions, 'id', labelKey)}
          value={form.credit_hours_id}
          onChange={setFieldValue('credit_hours_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.difficultyLevel')}
          name="difficulty_level_id"
          label={t('CoursePlanCreationPage.form.difficultyLevel')}
          options={generateEnum(difficultyLevelOptions, 'id', labelKey)}
          value={form.difficulty_level_id}
          onChange={setFieldValue('difficulty_level_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          name="aiBaseModel"
          label={t('dnaCreationPage.form.aiBaseModel')}
          placeholder={t('dnaCreationPage.form.aiBaseModel')}
          options={DnaEnums.ai_base_model}
          value={form.model}
          onChange={setFieldValue('model')}
          dropIcon
          disabled
          validators={[isRequired()]}
        />
      </div>
    </Form>
  );
};

export default CoursePlanAiForm;
