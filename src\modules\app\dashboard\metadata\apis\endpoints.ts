import { Api } from '@/services';
import { MetadataKeys } from '../types';
import { api } from '@/services/axios';

export const getTypes = async () => {
  const { data } = await Api.get('/types-names');
  return data.data;
};

export const addMetadata = async (data: Record<string, string>) => {
  const { data: result } = await Api.post('/types', data);
  return result;
};

export const deleteMetadata = async (id: string) => {
  const { data: result } = await Api.delete(`/types/${id}`);
  return result;
};
export const restoreMetadata = async (id: string) => {
  const { data: result } = await Api.put(`/types/${id}/restore`);
  return result;
};

export const updateMetadata = async ({ id, data }: { id: string; data: Record<string, string> }) => {
  const { data: result } = await Api.put(`/types/${id}`, { ...data, _method: 'put' });
  return result;
};

export const getSingleMetadata = async (type: MetadataKeys | ''): Promise<IMetadata[]> => {
  const { data: result } = await Api.get(`/types?type=${type}&page_size=1000`);

  return result.data.items;
};

export const importTypes = async (payload: { excel: File }) => {
  const { data } = await api.post('/types/import-data', payload, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.data;
};

export const exportTypes = async () => {
  const response = await api.get(`/types/export-data`, {
    responseType: 'blob',
  });

  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'types.xlsx');
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);

  return response.data;
};
