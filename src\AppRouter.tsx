// React
import React, { StrictMode } from 'react';
import { createBrowserRouter, RouterProvider } from 'react-router-dom';

import routes from '/src/routes';

// Deps
import 'iconify-icon';

// Components
import { AppProvider } from './context/provider';
import { ThemeProvider } from './context/theme-provider';
import { ReactQueryProvider } from './context/react-query-provider';
import { DirectionProvider } from './context/direction-provider';

export const AppRouter = () => {
  // Route
  const router = createBrowserRouter(routes);

  // hooks

  return (
    <StrictMode>
      <ReactQueryProvider>
        <AppProvider>
          <ThemeProvider defaultTheme="light" storageKey="vite-ui-theme">
            <DirectionProvider>
              <RouterProvider router={router} />
            </DirectionProvider>
          </ThemeProvider>
        </AppProvider>
      </ReactQueryProvider>
    </StrictMode>
  );
};
