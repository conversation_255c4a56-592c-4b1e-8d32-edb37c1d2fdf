import React from 'react';

function ChunkErrorBoundary({ children, fallback }: any) {
  const [hasError, setHasError] = React.useState(false);

  console.log('ChunkErrorBoundary rendered');
  console.log(hasError, 'hasError');

  React.useEffect(() => {
    const handleError = (error: any) => {
      console.log(error, 'error in ChunkErrorBoundary');

      // Only show refresh for chunk load errors
      if (error.name === 'ChunkLoadError' || error.message?.includes('dynamically imported module')) {
        setHasError(true);
      }
    };

    const handleUnhandledRejection = (event: any) => {
      const error = event.reason;
      if (error?.name === 'ChunkLoadError' || error.message?.includes('dynamically imported module')) {
        setHasError(true);
      }
    };

    // Listen for chunk load errors
    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (hasError) {
    if (fallback) {
      return fallback;
    }

    return (
      <div>
        <p>Failed to load component.</p>
        <button onClick={() => window.location.reload()}>Refresh page</button>
      </div>
    );
  }

  return children;
}

export default ChunkErrorBoundary; // Make sure this line exists
