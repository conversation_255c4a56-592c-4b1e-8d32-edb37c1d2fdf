import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useAddProgramPlanSkill } from '../../../apis/queries';

const SkillCustomeRow = ({ id }: { id: string }) => {
  // hooks
  const { t } = useTranslation();
  const { mutate: mutate, isPending: isPending } = useAddProgramPlanSkill();
  //refs

  return (
    <tr>
      <td colSpan={3} className="p-4 border-b border-border">
        <div
          className={`text-primary rtl:text-right font-semibold border border-border py-2 px-2 rounded-lg cursor-default`}
        >
          {t('programPlan.steps.learningOutcome.row.placeholder')}
        </div>
      </td>

      <td className="p-4 border-b border-border">
        <Button
          type="button"
          className="border-primary"
          loading={isPending}
          disabled={isPending}
          onClick={() => mutate(id)}
          variant={'outline'}
          size={'sm'}
        >
          + {t('CoursePlanCreationPage.add')}
        </Button>
      </td>
    </tr>
  );
};

export { SkillCustomeRow };
