import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import ActivityDialog from './activity-dialog';
import { useGenerateActivity } from '@/modules/app/tasks/apis/queries';
import { useParams } from 'react-router-dom';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';

const EmptyActivity = ({ contentId }: { contentId: number | string }) => {
  const { t } = useTranslation();
  const [isActivityDialogOpen, setIsActivityDialogOpen] = useState(false);
  const { taskId } = useParams();
  const { mutate, isPending } = useGenerateActivity();
  const handleSubmit = (form: any) => {
    mutate(
      { code: taskId || '', payload: form },
      {
        onSuccess: () => setIsActivityDialogOpen(false),
      }
    );
  };
  return (
    <div className="flex flex-col justify-center items-center ms-2 min-h-96">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.activity.noActivityTitle')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.activity.noActivityDescription')}</p>
      </div>

      <ProtectedTaskComponent requiredPermissions={'dna_activity_create'}>
        <Button
          onClick={() => setIsActivityDialogOpen(true)}
          loading={isPending}
          className="flex gap-2 px-5 items-center"
        >
          {t('dnaSinglePage.activity.generateActivity')}
        </Button>
      </ProtectedTaskComponent>

      {isActivityDialogOpen && (
        <ActivityDialog
          isOpen={isActivityDialogOpen}
          onOpenChange={() => setIsActivityDialogOpen(false)}
          contentId={contentId}
          onSubmit={handleSubmit}
          isLoading={isPending}
        />
      )}
    </div>
  );
};

export default EmptyActivity;
