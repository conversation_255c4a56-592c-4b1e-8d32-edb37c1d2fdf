// table-content-row.tsx - Updated TheRow component
import { useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSSProperties, memo } from 'react';
import { CSS } from '@dnd-kit/utilities';
import { cn } from '@/lib/utils';
import { get } from 'object-path';
import { TableCell, TableRow } from '../ui/table';
import { Input } from '../ui/input';
import { Icon } from '../icon';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';

/**
 * A single row that is draggable. We do *not* create a SortableContext here.
 * Instead, we rely on the parent-level SortableContext.
 */
export const TheRow = memo(function TheRow({
  row,
  level,
  rowPath,
  columns,
  isMultipleSelect,
  isDraggable,
  rowKey,
  isRowSelected,
  onSelectRow,
  expandedRows,
  toggleRow,
  getChildRows,
  t,
  parentRow,
  index,
}: {
  row: any;
  level: number;
  rowPath: string;
  columns: any[];
  isMultipleSelect: boolean;
  isDraggable: boolean;
  rowKey: string;
  isRowSelected: (id: string) => boolean;
  onSelectRow: (row: any) => void;
  expandedRows: Set<string>;
  toggleRow: (path: string) => void;
  getChildRows: (row: any, level: number) => any[] | null;
  t: any;
  parentRow: any[];
  index: number; // Add index type
}) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: row.id,

    data: {
      // Provide data for handleDragEnd or handleDragOver
      id: row.id,
      level,
      rowPath: rowPath,
      // If parent is the second-last part of the path (or top-level if none)
      parentId: rowPath.split('->')[rowPath.split('->').length - 2] || null,
      title: row.title,
    },
    strategy: verticalListSortingStrategy,
  });

  // Draggable styling
  const style: CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
    opacity: isDraggable && isDragging ? 0.6 : 1,
  };

  // Check children for expansions
  const children = getChildRows(row, level);
  const rowHasChildren = !!(children && children.length > 0);
  const isExpanded = expandedRows.has(rowPath);

  // For cell rendering
  function renderCell(column: any) {
    const value = get(row, column.accessorKey);

    // Create content element
    let content;
    if (column.cell) {
      content = column.cell({ row, level, parentRow, index });
    } else if (!value) {
      content = <p className="rtl:text-right">—</p>;
    } else if (typeof value !== 'string' && typeof value !== 'number') {
      content = <p className="rtl:text-right">{t('error')}</p>;
    } else {
      content = (
        <p className="rtl:text-right truncate" style={{ maxWidth: column.width }}>
          {value}
        </p>
      );
    }

    // Wrap with tooltip if needed
    if (column.tooltip && value) {
      return (
        <Tooltip delayDuration={300}>
          <TooltipTrigger asChild>
            <div>{content}</div>
          </TooltipTrigger>
          <TooltipContent className="max-h-[200px] max-w-[200px] overflow-auto">
            <p>{value.toString()}</p>
          </TooltipContent>
        </Tooltip>
      );
    }

    return content;
  }

  return (
    <TableRow
      ref={setNodeRef}
      style={style}
      className={`hover:bg-secondary border-b border-border ${level > 0 ? 'bg-muted dark:bg-background' : ''}`}
    >
      {isMultipleSelect && (
        <TableCell width={45} className="whitespace-nowrap break-words">
          <Input
            className="!rounded-full size-3.5"
            type="checkbox"
            checked={isRowSelected(row[rowKey])}
            onChange={() => onSelectRow(row)}
          />
        </TableCell>
      )}
      {isDraggable && (
        <TableCell
          {...attributes}
          {...listeners}
          className={cn('w-1', { 'ltr:pl-14': level > 0 }, { 'rtl:pr-14': level > 0 })}
        >
          <Icon className="text-lg text-gray-400 cursor-grab" icon="ci:drag-vertical" />
        </TableCell>
      )}

      {columns.map((column, idx) => {
        // Indent the first column if it has children
        if (idx === 0) {
          const padding = rowHasChildren ? 20 : 40;
          return (
            <TableCell key={idx} className="p-4 py-3 overflow-hidden" width={column.width}>
              <div
                onClick={(e: any) => {
                  e.stopPropagation();
                  toggleRow(rowPath);
                }}
                className="flex items-center cursor-pointer gap-3"
                style={{ paddingInlineStart: `${level * padding}px` }}
              >
                {rowHasChildren && (
                  <Icon
                    icon={isExpanded ? 'lucide:chevron-down' : 'lucide:chevron-right'}
                    className={`size-4 mt-1  ${isExpanded ? '' : 'rtl:rotate-180'} text-primary `}
                    width={17}
                  />
                )}
                {renderCell(column)}
              </div>
            </TableCell>
          );
        }
        // Normal columns
        return (
          <TableCell key={idx} className="p-4 py-3 overflow-hidden" width={column.width}>
            {renderCell(column)}
          </TableCell>
        );
      })}
    </TableRow>
  );
});
