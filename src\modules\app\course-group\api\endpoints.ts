import { ICoursesSimplified, ICreateCourseGroup } from '../types';
import { Api } from '@/services';

export const getCoursesSimplified = async (): Promise<ICoursesSimplified[]> => {
  const { data } = await Api.get(`/courses/groups/selected-courses`);
  return data.data;
};

export const createCourseGroup = async (payload: ICreateCourseGroup): Promise<any> => {
  const { data } = await Api.post(`/courses/groups/store`, payload);
  return data.data;
};

export const deleteCourseGroup = async (id: string): Promise<any> => {
  const { data } = await Api.post(`/courses/groups/delete/${id}`, {
    _method: 'delete',
  });
  return data.data;
};

export const deleteCourseInsideCourseGroup = async ({
  id,
  course_id,
}: {
  id: string | undefined;
  course_id: string;
}): Promise<any> => {
  const { data } = await Api.post(`/courses/groups/delete-course/${id}`, {
    _method: 'delete',
    course_id,
  });
  return data.data;
};

export const updateCourseGroup = async ({ id, ...payload }: ICreateCourseGroup & { id: string }): Promise<any> => {
  const { data } = await Api.post(`/courses/groups/update/${id}`, {
    ...payload,
    _method: 'put',
  });
  return data.data;
};

export const updateCourseGroupStatus = async ({ id, status_id }: { id: number; status_id: number }): Promise<any> => {
  const { data } = await Api.put(`/courses/groups/change-status/${id}`, { status_id });
  return data.data;
};
