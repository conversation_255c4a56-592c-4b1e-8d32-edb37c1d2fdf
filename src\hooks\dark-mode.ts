import { useState } from 'react';

export function useDarkMode() {
  const isInDarkMode = () => {
    const result = document.documentElement.classList.contains('dark');
    return result;
  };

  const [isDark, setIsDark] = useState(() => {
    const isDarkApplied = isInDarkMode();
    const theme = window.localStorage.getItem('theme');
    return isDarkApplied || theme === 'dark';
  });

  const setDarkMode = () => {
    document.documentElement.classList.add('dark');
    setIsDark(true);
  };

  const unsetDarkMode = () => {
    document.documentElement.classList.remove('dark');
    document.documentElement.setAttribute('data-color-mode', 'light');
    setIsDark(false);
  };

  const switchDarkMode = () => {
    if (isDark) {
      unsetDarkMode();
      window.localStorage.setItem('theme', 'light');
    } else {
      setDarkMode();
      window.localStorage.setItem('theme', 'dark');
    }
  };

  const setCachedDarkMode = () => {
    const theme = window.localStorage.getItem('theme');
    if (theme === 'light') {
      unsetDarkMode();
    } else {
      setDarkMode();
    }
  };

  return {
    isInDarkMode,
    setDarkMode,
    unsetDarkMode,
    switchDarkMode,
    setCachedDarkMode,
    isDark,
  };
}
