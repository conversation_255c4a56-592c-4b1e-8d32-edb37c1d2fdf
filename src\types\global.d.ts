interface ITableCellParams<Row> {
  row: Row;
  level?: number;
  parentRow?: Row;
  index?: number;
}

interface ITableColumn<Row> {
  accessorKey: string;
  header: string | React.ReactNode;
  width?: string;
  cell?: (params: ITableCellParams<Row>) => React.ReactNode;
  tooltip?: boolean;
}

interface IMetadata {
  id: string | number;
  type: string;
  name_en: string;
  name_ar: string;
  description: string;
  deleted_at: null | string;
  [key: string]: string | number | undefined | null;
}
