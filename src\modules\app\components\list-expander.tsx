import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useTranslation } from 'react-i18next';

type ListItem = string | { name_en: string; name_ar: string };

const ListExpander = ({ list, initialVisiableItems = 3 }: { list: ListItem[]; initialVisiableItems: number }) => {
  const { i18n } = useTranslation();
  const [isExpanded, setIsExpanded] = useState(false);

  if (!list || list.length === 0) return null;
  const getDisplayText = (item: ListItem): string => {
    if (typeof item === 'string') {
      return item;
    }
    return i18n.language === 'ar' ? item.name_ar : item.name_en;
  };

  const displayItems = isExpanded ? list : list.slice(0, initialVisiableItems);
  const hasMoreItems = list.length > initialVisiableItems;

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="flex flex-wrap gap-1 relative items-center">
      {displayItems.map((item, index) => (
        <Badge key={index} variant={'outline'} className="px-4 py-1.5 text-nowrap">
          {getDisplayText(item)}
        </Badge>
      ))}

      {hasMoreItems && (
        <Button
          variant="ghost"
          size="sm"
          className="p-1 h-7 rounded-full"
          onClick={toggleExpand}
          title={isExpanded ? 'Show less' : `Show ${list.length - 3} more`}
        >
          {isExpanded ? (
            <Button className="size-7" variant={'outline'} size={'icon'}>
              <ChevronUp className="h-4 w-4" />
            </Button>
          ) : (
            <Button className="size-7" variant={'outline'} size={'icon'}>
              <ChevronDown className="h-4 w-4" />
            </Button>
          )}
        </Button>
      )}
    </div>
  );
};

export default ListExpander;
