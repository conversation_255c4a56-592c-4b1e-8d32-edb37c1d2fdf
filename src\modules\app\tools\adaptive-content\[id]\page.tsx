import { Form, useForm, ComboboxInput, useValidate, Textarea, Icon, useNotify, TextInput, PageLoader } from '@/index';
import { Button } from '@/components/ui/button';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { useGetDnaById, useReformatDna } from '@/modules/app/dna/apis/queries';
import { useParams } from 'react-router-dom';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { useEffect, useState } from 'react';
import { useAtom } from 'jotai';
import { userAtom } from '@/modules/auth/store';

interface Iinfo {
  label: string;
  value: string;
  name: string;
  onChange: (newValue: string) => void;
}
const RewriteDnaContent = () => {
  // Hooks
  const { t, i18n } = useTranslation();
  const { data: allmetaData } = useGetSingleMetadata('');
  const { mutate, data: reformatedDna, isPending } = useReformatDna();
  const { isRequired } = useValidate();
  const { id = '' } = useParams();
  const { notify } = useNotify();
  const { data } = useGetDnaById(Number(id));
  const [type, setType] = useState('effect');
  const [editField, setEditField] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(true);
  const [user, setUser] = useAtom(userAtom);

  const { form, setFieldValue } = useForm({
    audience_id: 0,
    style_id: 0,
    pre_knowledge_id: 0,
    subject_id: 0,
    language_id: 0,
    location_id: 0,
    notes: '',
    user_name: '',
    user_info: '',
    education: '',
    profession: '',
    university: '',
  });

  const handleFieldUpdate = (name: string, value: string) => {
    setFieldValue(name)(value);
    setEditField(null);
  };

  const InfoRow = ({ label, value, name, onChange }: Iinfo) => {
    const [tempValue, setTempValue] = useState(value);

    return (
      <div className="grid grid-cols-5 gap-5 items-center">
        <p className="opacity-[0.5] col-span-1 p-2">{label}</p>
        {editField === name ? (
          <div className="bg-gray-200 dark:bg-black rounded-md p-2 w-full col-span-4 flex gap-2">
            <Form className="flex gap-3">
              <TextInput
                name={name}
                placeholder={t('dnaCreationPage.form.title')}
                value={tempValue}
                onChange={(e: string) => setTempValue(e)}
                className="w-[250px]"
              />
              <div className="flex gap-2">
                <Button onClick={() => handleFieldUpdate(name, tempValue)}>{t('update')}</Button>
                <Button variant={'outline'} onClick={() => setEditField(null)}>
                  {t('cancel')}
                </Button>
              </div>
            </Form>
          </div>
        ) : (
          <div className="bg-gray-200 dark:bg-black rounded-md p-2 w-full col-span-4 flex gap-2">
            <p>{value ? value : '---'}</p>
            <Icon
              onClick={() => setEditField(name)}
              icon="mingcute:edit-line"
              width={15}
              className="self-start cursor-pointer text-blue-500"
            />
          </div>
        )}
      </div>
    );
  };

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const getFilteredOptions = (type: string) =>
    generateEnum(allmetaData?.filter((item) => item.type === type) || [], 'id', labelKey);

  const handleSubmit = () => {
    if (id)
      mutate(
        { id, payload: form },
        {
          onSuccess: () => {
            setType('submit'), notify.success('Dna Reformatted Successfully!');
          },
        }
      );
  };

  useEffect(() => {
    if (!allmetaData || !data) return;

    const getIdByName = (nameToMatch: string) => {
      const matchedItem = allmetaData?.find((item) => item.type === nameToMatch);
      return matchedItem ? matchedItem.id : null;
    };

    const subjectId = getIdByName('subject');
    const audienceId = getIdByName('audience');
    const languageId = getIdByName('language');
    const styleId = getIdByName('style');
    const preKnowledgeId = getIdByName('pre_knowledge');
    const locationId = getIdByName('location');

    const fieldsToUpdate = {
      audience_id: data.audience_id || audienceId,
      style_id: styleId,
      pre_knowledge_id: preKnowledgeId,
      subject_id: data.subject_id || subjectId,
      language_id: data.language_id || languageId,
      location_id: locationId,
      user_name: user?.name,
    };

    (Object.keys(fieldsToUpdate) as Array<keyof typeof fieldsToUpdate>).forEach((field) => {
      setFieldValue(field)(fieldsToUpdate[field]);
    });
    {
      type === 'effect' &&
        mutate(
          { id, payload: { ...fieldsToUpdate, notes: '' } },
          {
            onSuccess: () => {
              setType('submit'), setIsLoaded(false);
            },
          }
        );
    }
  }, [allmetaData, data]);

  if (isLoaded) {
    return <PageLoader />;
  }

  return (
    <div>
      <div className="px-3">
        <h5 className="mb-5 text-xl">{data.title}</h5>
        {reformatedDna && (
          <div className="p-5 border border-border rounded-lg shadow-sm bg-white dark:bg-black">
            <div dangerouslySetInnerHTML={{ __html: reformatedDna?.article }} className="whitespace-pre-wrap" />
          </div>
        )}
        <Accordion type="single" collapsible className="w-full space-y-4 mt-3">
          <AccordionItem value="item-1">
            <AccordionTrigger>{t('UserInformation')}</AccordionTrigger>
            <AccordionContent>
              <div className="space-y-3">
                {[
                  { label: t('name'), value: form.user_name ? form.user_name : user?.name, name: 'user_name' },
                  { label: t('profile.info'), value: form.user_info ? form.user_info : user?.info, name: 'user_info' },
                  {
                    label: t('profile.profession'),
                    value: form.profession ? form.profession : user?.profession,
                    name: 'profession',
                  },
                  {
                    label: t('profile.education'),
                    value: form.education ? form.education : user?.education,
                    name: 'education',
                  },
                  {
                    label: t('profile.university'),
                    value: form.university
                      ? form.university
                      : labelKey
                      ? user?.university?.name_en
                      : user?.university?.name_ar,
                    name: 'university',
                  },
                ].map((info, index) => (
                  <InfoRow
                    key={index}
                    label={info.label}
                    value={info.value}
                    name={info.name}
                    onChange={(newValue) => setFieldValue(info.name)(newValue)}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
          {/* <AccordionItem value="item-3">
            <AccordionTrigger>{t('dashboardPage.tabs.metaData')}</AccordionTrigger>
            <AccordionContent></AccordionContent>
          </AccordionItem> */}
          <AccordionItem value="item-2">
            <AccordionTrigger>{t('DNAOriginalVersion')}</AccordionTrigger>
            <AccordionContent>
              <div className="p-5 border border-border rounded-lg shadow-sm bg-white dark:bg-black">
                <div dangerouslySetInnerHTML={{ __html: data.dna }} className="whitespace-pre-wrap"></div>
                <div className="mt-4">
                  <p className="text-xl font-semibold">{t('dnaSinglePage.metaData.resources')}</p>
                  {data?.resources?.length
                    ? data.resources.map((resource: string, index: number) => (
                        <div key={index} className="flex flex-col gap-2 px-3">
                          {resource.includes('https://') ? (
                            <a href={resource} className="pl-4 underline text-primary" target="_blank" rel="noreferrer">
                              - {resource}
                            </a>
                          ) : (
                            <p className="pl-4">- {resource}</p>
                          )}
                        </div>
                      ))
                    : '-'}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
        <div className="mt-5">
          <h1 className="text-xl">{t('dashboardPage.tabs.metaData')}</h1>
          <Form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
              <div className="space-y-4">
                {[
                  {
                    name: 'audience_id',
                    label: t('dnaCreationPage.form.audience'),
                    options: getFilteredOptions('audience'),
                  },
                  {
                    name: 'style_id',
                    label: t('dnaCreationPage.form.style'),
                    options: getFilteredOptions('style'),
                  },
                  {
                    name: 'pre_knowledge_id',
                    label: t('dnaCreationPage.form.preKnowledge'),
                    options: getFilteredOptions('pre_knowledge'),
                  },
                  {
                    name: 'language_id',
                    label: t('dnaCreationPage.form.language'),
                    options: getFilteredOptions('language'),
                  },
                  {
                    name: 'subject_id',
                    label: t('dnaCreationPage.form.subject'),
                    options: getFilteredOptions('subject'),
                  },
                  {
                    name: 'location_id',
                    label: t('dnaSinglePage.metaData.location'),
                    options: getFilteredOptions('location'),
                  },
                ].map(({ name, label, options }) => (
                  <ComboboxInput
                    key={name}
                    name={name}
                    label={label}
                    placeholder={label}
                    options={options}
                    value={form[name]}
                    onChange={setFieldValue(name)}
                    dropIcon
                    validators={[isRequired()]}
                    customLabel
                  />
                ))}
              </div>
              <div className="self-end">
                <Textarea
                  name="notes"
                  label={t('Rewrite notes')}
                  placeholder={t('dnaCreationPage.form.contextPlaceholder')}
                  rows={5}
                  value={form.notes}
                  onChange={setFieldValue('notes')}
                />
              </div>
            </div>
            <div className="flex mb-10 gap-3 mt-3 justify-end">
              <Button
                type="submit"
                loading={isPending}
                disabled={
                  isPending ||
                  (data.language_id === form.language_id &&
                    data.audience_id === form.audience_id &&
                    form.subject_id === data.subject_id)
                }
                className="min-w-[100px] mt-4"
              >
                {t('dnaCreationPage.rewrite')}
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default RewriteDnaContent;
