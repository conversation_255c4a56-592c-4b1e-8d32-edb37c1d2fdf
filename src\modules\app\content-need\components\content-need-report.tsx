import { useMemo } from 'react';

import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';
import { useAtom } from 'jotai';
import { contentNeedsAtom } from '../../tools/store/contnet-need';
import { localizePresenter } from '@/utils/helpers';

const ReportStep = ({ onComplete }: { onComplete: (data: any) => void }) => {
  const [data] = useAtom(contentNeedsAtom);
  const { t, i18n } = useTranslation();

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'organization_vision',
        header: t('contentNeed.table.job_title'),
        width: '400px',
        cell: ({ row, level }) => {
          return (
            <div className="space-y-3">
              <p>{row.title}</p>
              <p>{row.learning_outcome}</p>
            </div>
          );
        },
      },
      {
        accessorKey: 'skill',
        header: t('Skills'),
        width: '600px',
        cell: ({ row }) => {
          return (
            <div className="grid grid-cols-[3fr_2fr_1fr_1fr] gap-4 p-4">
              <div className="col-span-4 grid grid-cols-[3fr_2fr_1fr_1fr] pb-2 font-medium border-b border-gray-400">
                <div>{t('Skills title')}</div>
                <div>{t('Skills correlation')}</div>
                <div>{t('Skills type')}</div>
                <div>{t('contentNeed.steps.skills.table.levels')}</div>
              </div>

              {row.skills.map((skill: any, index: number) => (
                <>
                  <div className="col-span-4 grid grid-cols-[3fr_2fr_1fr_1fr] font-medium rtl:text-right">
                    <div className="w-[220px]">
                      <Badge variant="outline" className="font-medium">
                        {skill.title}
                      </Badge>
                    </div>
                    <div>{skill.correlation}</div>
                    <div>{skill.type}</div>
                    <div>{localizePresenter(skill.level, i18n.language)}</div>
                  </div>
                  <hr className="col-span-4 border-gray-200" />
                </>
              ))}
            </div>
          );
        },
      },
      {
        accessorKey: 'roles',
        header: t('Roles title'),
        width: '400px',
        cell: ({ row }) => {
          return (
            <ul className="rtl:text-right space-y-1">
              {row.roles.map((role: any, index: number) => (
                <li className="list-disc" key={index}>
                  {role.title}
                </li>
              ))}
            </ul>
          );
        },
      },
    ];
  }, [t]);

  return (
    <>
      <div className="mt-[-1.75rem] flex items-center justify-center">
        <div className="max-w-[1500px] w-full bg-background p-6">
          <p className="text-lg font-medium my-3">{t('contentNeed.steps.outcome.title')}</p>
          <Table rows={data.jobs || []} columns={columns}>
            <TableContent>
              <TableContentBody />
            </TableContent>
          </Table>
        </div>
      </div>
    </>
  );
};
export default ReportStep;
