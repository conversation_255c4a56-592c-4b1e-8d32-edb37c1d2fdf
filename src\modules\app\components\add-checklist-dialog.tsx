'use client';

import { useMemo, useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { Button } from '@/components/ui/button';
import { generateEnum } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useNotify } from '@/hooks';
import { MetadataKeys } from '@/modules/app/dashboard/metadata/types';
interface IProps {
  loading: boolean;
  callback: (selectedItems: string[]) => void;
  open: boolean;
  setOpen: (open: boolean) => void;
  type: 'ownerDna' | 'reviewrDna' | 'ownerCourse' | 'reviewrCourse';
}
const AddChecklistDialog = ({ callback, open, setOpen, loading, type }: IProps) => {
  const checklistType: MetadataKeys = useMemo(() => {
    if (type === 'ownerDna') {
      return 'owner_dna_checklist';
    } else if (type === 'ownerCourse') {
      return 'owner_course_checklist';
    } else if (type === 'reviewrDna') {
      return 'reviewer_dna_checklist';
    } else if (type === 'reviewrCourse') {
      return 'reviewer_course_checklist';
    }
    return 'owner_dna_checklist';
  }, [type]);
  const { data: metaData } = useGetSingleMetadata(checklistType);

  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const [checklist, setChecklist] = useState<string[]>([]);

  const { notify } = useNotify();
  const handleChecklistChange = (itemId: string) => {
    if (checklist.includes(itemId)) {
      setChecklist((prev) => prev.filter((id) => id !== itemId));
      return;
    } else {
      setChecklist((prev) => [...prev, itemId]);
    }
  };

  const handleSubmit = () => {
    if (!checklist.length) {
      notify.error(t('feedback.selectFeedback'));
      return;
    }
    callback(checklist);
  };
  const checklistArray = useMemo(() => {
    return generateEnum(metaData || [], 'id', labelKey);
  }, [metaData]);

  const handelTitle = () => {
    if (type === 'ownerDna') {
      return t('feedback.ownerDna');
    } else if (type === 'ownerCourse') {
      return t('feedback.ownerCourse');
    } else if (type === 'reviewrDna') {
      return t('feedback.reviewerDna');
    } else if (type === 'reviewrCourse') {
      return t('feedback.reviewerCourse');
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader className=" border-b border-border pb-3">
          <DialogTitle className="text-2xl">{handelTitle()}</DialogTitle>
          <DialogDescription>{t('feedback.contentFeedbackDescription')}</DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-3">
          {checklistArray.length > 0 ? (
            checklistArray.map((item: any) => (
              <div key={item.value} className="flex items-center gap-2">
                <Checkbox
                  className="size-5 border-border rounded"
                  id={`checklist-${item.value}`}
                  checked={checklist.includes(item.value)}
                  onCheckedChange={() => handleChecklistChange(item.value)}
                />
                <Label className="cursor-pointer text-base font-normal" htmlFor={`checklist-${item.value}`}>
                  {item.label}
                </Label>
              </div>
            ))
          ) : (
            <div className="my-5 text-center">{t('feedback.noChecklist')}</div>
          )}
        </div>

        <DialogFooter className="border-t border-border pt-3 flex gap-2">
          <Button variant="outline" onClick={() => setOpen(false)}>
            {t('cancel')}
          </Button>
          <Button disabled={loading || checklist.length !== metaData?.length} loading={loading} onClick={handleSubmit}>
            {t('submit')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddChecklistDialog;
