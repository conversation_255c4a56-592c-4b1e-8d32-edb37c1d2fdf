import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createRole, deleteRole, getPermissions, getRoleById, getRoles, updateRole } from './endpoints';
import { IRole, IPermission } from '../types';
import { useNotify } from '@/index';
import useLanguageNavigate from '@/hooks/use-lang-navigation';

// hook to get all roles
export const useGetRoles = () => {
  return useQuery<IRole[]>({
    queryKey: ['roles'],
    queryFn: getRoles,
  });
};

// Hook to get role by ID
export const useGetRoleById = (id: string | undefined) => {
  return useQuery<IRole | null>({
    queryKey: ['role', id],
    queryFn: () => (id ? getRoleById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null
  });
};

// Hook to update a role
export const useUpdateRole = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();

  return useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: { name: string; permissions: string[] } }) =>
      updateRole(id, payload),
    onSuccess: () => {
      // Invalidate the specific query for the role that was updated
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      notify.success('Role updated successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
// Hook to create a role
export const useCreateRole = () => {
  const queryClient = useQueryClient();
  const navigate = useLanguageNavigate();
  const { notify } = useNotify();
  return useMutation<IRole, Error, { name: string; permissions: IPermission }>({
    mutationFn: createRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      navigate('/app/roles');
      notify.success('Role created successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};

// Hook to get permissions
export const useGetPermissions = () => {
  return useQuery<IPermission[], Error>({
    queryKey: ['permissions'],
    queryFn: getPermissions,
  });
};

// Hook to delete a role
export const useDeleteRole = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: deleteRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      notify.success('Role deleted successfully');
    },
  });
};
