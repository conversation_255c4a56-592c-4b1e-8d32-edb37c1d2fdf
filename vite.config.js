// import ckeditor5 from '@ckeditor/vite-plugin-ckeditor5';
import react from '@vitejs/plugin-react';
import path from 'path';
import fs from 'fs';
import { defineConfig, loadEnv } from 'vite';

const __dirname = path.dirname(__filename);

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());

  const useHttps = env.VITE_USE_HTTPS === 'true';
  const port = parseInt(env.VITE_PORT) || 8080;
  const httpsConfig = useHttps
    ? {
        key: fs.readFileSync(env.VITE_SSL_KEY_PATH),
        cert: fs.readFileSync(env.VITE_SSL_CERT_PATH),
      }
    : false;

  return {
    plugins: [
      react(),
      // ckeditor5({
      //   theme: require.resolve('@ckeditor/ckeditor5-theme-lark'),
      // }),
    ],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        src: '/src',
      },
    },
    server: {
      port,
      https: httpsConfig,
      // proxy: {
      //   '/api': {
      //     target: env.VITE_API_BASE_URL || 'http://localhost:8080',
      //     changeOrigin: true,
      //     withCredentials: false,
      //     rewrite: (path) => path.replace(/^\/api/, ''),
      //   },
      // },
    },
    build: {
      sourcemap: false,
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },
      rollupOptions: {
        treeshake: true,
        output: {
          manualChunks: {
            // Vendor chunks
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'i18n-vendor': ['react-i18next', 'i18next'],
            'ui-vendor': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-tabs'],
            'utils-vendor': ['lodash', 'date-fns', 'clsx'],
            'icons-vendor': ['lucide-react', '@iconify/react'],
          },
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
          assetFileNames: 'assets/[name]-[hash].[ext]',
          // Ensure consistent chunk naming to help with caching
          experimentalMinChunkSize: 10000,
        },
        // Improve chunk loading reliability
        external: (id) => {
          // Don't externalize anything that should be bundled
          return false;
        },
      },
      chunkSizeWarningLimit: 1000, // Increase warning limit to 1MB
    },
  };
});
