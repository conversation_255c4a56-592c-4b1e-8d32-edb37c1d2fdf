import { useMemo, useState } from 'react';
import EmptyAnalysis from './empty-state';
import {
  useGenerateArticulateAnalysis,
  useGetDnaArticulateAnalysis,
  useGetDnaById,
  useUpdateAnalysis,
} from '../../apis/queries';
import { useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { IStoryBoard } from '../../types';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';
import { Card, Icon, ProtectedComponent } from '@/components';
import PromptViewerDialog from '@/modules/app/components/prompt-viewer-dialog';
import { Button } from '@/components/ui/button';
import GenerationDialog from './generation-dialog';
import { dateAndTimeFormat } from '@/utils/helpers';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import FileManagerDialog from '@/components/file-manager/file-manager-dialog';
import { useNotify } from '@/hooks';
import { useConfirmation } from '@/components/confirmation-popup/hooks';

const ArticulateAnalysis = () => {
  // states
  const [isGenerationDialogOpen, setIsGenerationDialogOpen] = useState(false);
  const [isSuggestedMediaGeneratorOpen, setIsSuggestedMediaGeneratorOpen] = useState(false);
  const [expandAll, setExpandAll] = useState(false);
  const [selectedPrompt, setSelectedPrompt] = useState({} as IStoryBoard);
  const { dnaId } = useParams();
  const { notify } = useNotify();
  const { i18n, t } = useTranslation();
  const { mutate, isPending: isRegenrating } = useGenerateArticulateAnalysis();
  const { data: dna } = useGetDnaById(dnaId);
  const { mutate: updateAnalysis, isPending: isUpdating, variables } = useUpdateAnalysis();

  //hooks
  const { confirm } = useConfirmation();

  const { data, isPending } = useGetDnaArticulateAnalysis(dnaId);

  const handleImageSelect = (newImageUrl: string) => {    
    const contentWithoutImages = selectedPrompt.full_block_content.replace(/<a[^>]*><\/a>|<a[^>]*\/>/g, '');
    const updatedContent = `${contentWithoutImages}<a href="${newImageUrl}" alt="Selected image" />`;
    updateAnalysis({
      dna_id: Number(dnaId),
      content: updatedContent,
      storyboard_id: selectedPrompt.id,
    });
  };

  const handelCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      notify.success(t('fullBlockContentCopied.success'));
    } catch (err) {
      notify.error(t('copyFailed'));
    }
  };

  const CollapsibleContent = ({ content, expandAll }: { content: string; expandAll: boolean }) => {
    const previewLength = 140;
    const needsCollapse = content && content.length > previewLength;

    if (!needsCollapse) {
      return <div dangerouslySetInnerHTML={{ __html: content }} />;
    }

    return (
      <div>
        <div
          dangerouslySetInnerHTML={{
            __html: expandAll ? content : `${content.substring(0, previewLength)}...`,
          }}
        />
      </div>
    );
  };
  const extractImageSrcFromHtml = (htmlContent: string) => {
    if (!htmlContent) return '';

    const imgMatch = htmlContent.match(/<a[^>]+href="([^"]*)"/);
    return imgMatch ? imgMatch[1] : '';
  };

  const handleDeleteMedia = (row: IStoryBoard) => {
    confirm({
      variant: 'destructive',
      title: t('analysis.table.delete.title'),
      description: t('analysis.table.delete.description'),
      onConfirm: () => {
        const contentWithoutImages = row.full_block_content.replace(/<a[^>]*><\/a>|<a[^>]*\/>/g, '');
        updateAnalysis({
          dna_id: Number(dnaId),
          content: contentWithoutImages,
          storyboard_id: row.id,
        });
      },
    });
  };

  const columns: ITableColumn<IStoryBoard>[] = useMemo(() => {
    return [
      {
        accessorKey: 'block_number',
        header: t('analysis.table.blockNumber'),
        width: '50px',
      },
      {
        accessorKey: 'block_title',
        header: t('analysis.table.blockTitle'),
        width: '350px',
        tooltip: true,
        cell: ({ row }) => {
          return <div className="line-clamp-6">{row.block_title}</div>;
        },
      },
      {
        accessorKey: 'full_block_content',
        header: (
          <div>
            {t('analysis.table.fullBlockContent')}
            <Button
              variant="outline"
              className="ml-4"
              onClick={() => {
                setExpandAll((prev) => !prev);
              }}
            >
              {expandAll ? t('analysis.table.collapseAll') : t('analysis.table.expandAll')}
            </Button>
          </div>
        ),
        width: '500px',
        cell: ({ row }) => {
          return <CollapsibleContent content={row?.full_block_content} expandAll={expandAll} />;
        },
      },
      {
        accessorKey: 'block_type',
        header: t('analysis.table.blockType'),
        tooltip: true,
        width: '100px',
      },
      {
        accessorKey: 'suggested_media',
        header: t('analysis.table.suggestedMedia'),
        tooltip: true,
        width: '150px',
        cell: ({ row }) => {
          return <div className="text-center line-clamp-6">{row?.suggested_media || '__'}</div>;
        },
      },
      {
        accessorKey: 'additional_notes',
        header: t('analysis.table.additionalNotes'),
        tooltip: true,
        width: '150px',
        cell: ({ row }) => {
          return <div className="line-clamp-6">{row.additional_notes}</div>;
        },
      },
      {
        accessorKey: 'actions',
        header: t('Actions'),
        width: '280px',
        cell: ({ row }) => (
          <div className="flex gap-2">
            {row?.full_block_content && row.full_block_content.includes('<a') ? (
              <div className="relative w-full h-28 ">
                <img
                  src={extractImageSrcFromHtml(row?.full_block_content)}
                  className="object-cover w-full rounded-md h-28"
                  alt=""
                />
                <div className="absolute inset-0 flex items-center justify-center gap-6">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          onClick={() => {
                            setIsSuggestedMediaGeneratorOpen(true);
                            setSelectedPrompt(row);
                          }}
                          size={'icon'}
                        >
                          <Icon icon="basil:edit-outline" width={22} className="cursor-pointer" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('analysis.table.reGenerateMedia')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          onClick={() => window.open(extractImageSrcFromHtml(row?.full_block_content), '_blank')}
                          size={'icon'}
                        >
                          <Icon icon="hugeicons:view" width={22} className="cursor-pointer" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('analysis.table.actions.view')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Button
                          onClick={() => {
                            handleDeleteMedia(row);
                          }}
                          size={'icon'}
                          loading={isUpdating && row.id === variables?.storyboard_id}
                        >
                          <Icon icon="gg:trash" width={22} className="cursor-pointer" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('analysis.table.actions.delete')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ) : (
              <Button
                onClick={() => {
                  setIsSuggestedMediaGeneratorOpen(true);
                  setSelectedPrompt(row);
                }}
                loading={isUpdating && row.id === variables?.storyboard_id}
              >
                {t('analysis.table.actions.generateMedia')}
              </Button>
            )}
            <div className="flex justify-start">
              <Icon
                icon="tabler:copy"
                width={25}
                className="cursor-pointer text-primary"
                onClick={() => handelCopyToClipboard(row.full_block_content)}
              />
            </div>
          </div>
        ),
      },
    ];
  }, [t, i18n.language, isPending, data, variables, isUpdating, expandAll]);
  if (isPending)
    return (
      <div>
        <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
      </div>
    );
  if (!data)
    return (
      <div>
        <EmptyAnalysis dna={dna} />
      </div>
    );
  return (
    <div>
      <div>
        <h2 className="mb-3 font-medium">{t('analysis.info')}</h2>
        <Card className="p-4 mb-5">
          <div className="flex space-x-2 divide-x-2">
            <div className="flex items-center gap-2 pe-4">
              <span className="text-sm font-medium">{t('analysis.createdAt')}:</span>
              <span className="text-sm text-gray-500">
                {dateAndTimeFormat(data?.analysis.created_at, i18n.language)}
              </span>
            </div>
            <div className="flex items-center gap-2 px-4">
              <span className="text-sm font-medium">{t('analysis.model')}:</span>
              <span className="text-sm text-gray-500">{data.analysis.model}</span>
            </div>
            <div className="flex items-center gap-2 px-4">
              <span className="text-sm font-medium">{t('analysis.prompt')}:</span>
              <span className="text-sm text-gray-500">
                <PromptViewerDialog model={data.analysis.model} prompt={data.analysis.prompt} />
              </span>
            </div>
          </div>
        </Card>
      </div>
      <div>
        <h2 className="mb-3 font-medium">{t('analysis.storyBoard')}</h2>

        <Table columns={columns} rows={data.storyboard || []}>
          <TableContent>
            <TableContentHeader>
              <div className="flex justify-end w-full">
                <ProtectedComponent requiredPermissions={'dna_analysis_create'}>
                  <Button
                    loading={isRegenrating}
                    onClick={() => setIsGenerationDialogOpen(true)}
                    className="flex gap-2 px-5 "
                  >
                    {t('analysis.reGenerate')}
                  </Button>
                </ProtectedComponent>
              </div>
            </TableContentHeader>
            <TableContentBody />
          </TableContent>
        </Table>

        {isGenerationDialogOpen && dna && (
          <GenerationDialog dna={dna} open={isGenerationDialogOpen} onOpenChange={setIsGenerationDialogOpen} />
        )}
        {isSuggestedMediaGeneratorOpen && (
          <FileManagerDialog
            open={isSuggestedMediaGeneratorOpen}
            onOpenChange={setIsSuggestedMediaGeneratorOpen}
            handleImageSelect={handleImageSelect}
            defaultPrompt={selectedPrompt?.suggested_media || ''}
          />
        )}
      </div>
    </div>
  );
};

export default ArticulateAnalysis;
