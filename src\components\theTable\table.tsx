import { useCallback, memo, useState, useEffect } from 'react';
import { TableContext } from './context';
import { TableProps } from './types';

const Table = ({
  rows,
  columns,
  rowKey = 'id',
  multipleSelect,
  loading,
  nestedConfig,
  draggingConfig,
  children,
  ...props
}: TableProps) => {
  const [rowsSnapshot, setRowsSnapshot] = useState(rows);
  useEffect(() => {
    setRowsSnapshot(rows);
  }, [rows]);

  // Track which rowPaths are expanded
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [visibleColumns, setVisibleColumns] = useState(columns);

  useEffect(() => {
    setVisibleColumns(columns);
  }, [columns]);

  const handleSelectAll = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (multipleSelect?.setSelectedRows) {
        multipleSelect.setSelectedRows(event?.currentTarget?.checked ? rows : []);
      }
    },
    [rows, multipleSelect?.setSelectedRows]
  );

  const handleSelectRow = useCallback(
    (row: any) => {
      if (multipleSelect?.setSelectedRows && multipleSelect.selectedRows) {
        const isSelected = multipleSelect.selectedRows.some((r) => r[rowKey] === row[rowKey]);
        if (isSelected) {
          multipleSelect.setSelectedRows(multipleSelect.selectedRows.filter((r) => r[rowKey] !== row[rowKey]));
        } else {
          multipleSelect.setSelectedRows([...multipleSelect.selectedRows, row]);
        }
      }
    },
    [multipleSelect?.setSelectedRows, multipleSelect?.selectedRows, rowKey]
  );

  const isRowSelected = useCallback(
    (rowId: string | number) => {
      return multipleSelect?.selectedRows?.some((row) => row[rowKey] === rowId) ?? false;
    },
    [multipleSelect?.selectedRows, rowKey]
  );

  const contextValue = {
    rows: rowsSnapshot,
    setRows: setRowsSnapshot,
    columns: visibleColumns,
    setColumns: setVisibleColumns,
    rowKey,
    selectedRows: multipleSelect?.selectedRows || [],
    onSelectRow: handleSelectRow,
    onSelectAll: handleSelectAll,
    isMultipleSelect: !!multipleSelect,
    isDraggable: draggingConfig?.enabled ?? false,
    draggingConfig: {
      enabled: draggingConfig?.enabled ?? false,
      onDragEnd: draggingConfig?.onDragEnd,
    },
    isRowSelected,
    loading,
    nestedConfig: {
      enabled: nestedConfig?.enabled ?? false,
      childProperties: nestedConfig?.childProperties ?? ['children'],
    },
    expandedRows,
    setExpandedRows,
  };

  return (
    <TableContext.Provider value={contextValue}>
      <div {...props}>{children}</div>
    </TableContext.Provider>
  );
};

export default memo(Table);
