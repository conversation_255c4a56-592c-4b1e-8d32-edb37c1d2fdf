import { Icon, useFetchList } from '@/index';
import { TablePagination } from '@/components/theTable';
import NotificationCard from '../../components/notifications/notification-card';
import { INotification } from '@/modules/app/notification/types';
import { useGetUnreadNotificationsCount, useMarkAllNotificationAsRead } from '../apis/queries';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

const NotificationsList = () => {
  const { mutate: markAllAsRead, isPending: isMarkingAllNotificationAsRead } = useMarkAllNotificationAsRead();
  const { data: unreadCount } = useGetUnreadNotificationsCount();

  const { t } = useTranslation();

  //  Hooks
  const { loading, list, count, pagination } = useFetchList<INotification>('/user/notifications', 'notifications', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
  });
  if (loading) {
    return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />;
  }
  return (
    <div className="w-fit flex flex-col gap-2 min-h-[calc(100vh-152px)]">
      <div className="flex gap-12">
        <div>
          {list.map((notification) => (
            <NotificationCard key={notification.id} notification={notification} />
          ))}
        </div>
        {unreadCount > 0 && (
          <Button
            className="text-xs px-1 h-6 text-gray-900"
            variant={'outline'}
            onClick={() => markAllAsRead()}
            loading={isMarkingAllNotificationAsRead}
            disabled={isMarkingAllNotificationAsRead}
          >
            {t('notification.markAllAsRead')}
          </Button>
        )}
      </div>
      <div className="mt-auto ">
        <TablePagination count={count} pagination={pagination} />
      </div>
    </div>
  );
};

export default NotificationsList;
