import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createTranslationKey,
  exportTranslations,
  getLanguages,
  importTranslations,
  updateLanguageKey,
} from './endpoints';
import { ILanguage } from '../types';
import { useNotify } from '@/index';
import { useTranslation } from 'react-i18next';

export const useGetLanguages = () => {
  return useQuery<ILanguage[]>({
    queryKey: ['languages'],
    queryFn: getLanguages,
    placeholderData: [],
  });
};

export const useUpdateLangugaeKey = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['updateKey'],
    mutationFn: updateLanguageKey,
    onSuccess: () => {
      notify.success(t('notify.keyUpdated'));
      queryClient.invalidateQueries({ queryKey: ['translations'] });
    },
    onError: () => {
      notify.error(t('notify.keyUpdateError'));
    },
  });
};

export const useCreateTranslationKey = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ['createTranslationKey'],
    mutationFn: createTranslationKey,
    onSuccess: () => {
      notify.success(t('notify.keyCreated'));
      queryClient.invalidateQueries({ queryKey: ['translations'] });
    },
    onError: () => {
      notify.error(t('notify.keyCreateError'));
    },
  });
};

export const useImportTranslations = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  return useMutation({
    mutationKey: ['importTranslations'],
    mutationFn: importTranslations,
    onSuccess: () => {
      notify.success(t('notify.imported'));
    },
    onError: (error) => {
      notify.error(error.message || t('notify.somethingWentWrong'));
    },
  });
};
export const useExportTranslations = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();

  return useMutation({
    mutationKey: ['exportTranslations'],
    mutationFn: exportTranslations,
    onSuccess: () => {
      notify.success(t('notify.exported'));
    },
    onError: (error) => {
      notify.error(error.message || t('notify.somethingWentWrong'));
    },
  });
};
