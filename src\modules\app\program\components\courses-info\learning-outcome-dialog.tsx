import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useEffect } from 'react';
import { useUpdateCourseLearningOutcome } from '../../apis/queries';

const ProgramPlanCourseLearningOutcomeDialog = ({
  isOpen,
  setIsOpen,
  outcomeBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  outcomeBeingEdited: { id: any; course_id: any; learning_outcome_id: any; outcome: any; learning_skill_type: any };
}) => {
  const { t } = useTranslation();
  const { form, setFieldValue, setFormValue } = useForm({
    id: outcomeBeingEdited.id || '',
    course_id: outcomeBeingEdited.course_id || '',
    learning_outcome_id: outcomeBeingEdited.learning_outcome_id || '',
    outcome: outcomeBeingEdited.outcome || '',
    learning_skill_type: outcomeBeingEdited.learning_skill_type || '',
  });
  useEffect(() => {
    setFormValue({
      ...outcomeBeingEdited,
    });
  }, [outcomeBeingEdited]);
  const { mutate, isPending } = useUpdateCourseLearningOutcome();
  const handleSubmit = () => {
    mutate(form, {
      onSuccess: () => {
        setIsOpen(false);
      },
    });
  };

  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('editLearningOutcome')}>
      <Form onSubmit={handleSubmit} className="flex flex-col gap-3">
        <TextInput
          name="outcome"
          label={t('programPlan.course.learningOutcome.outcome')}
          value={form.outcome}
          onChange={setFieldValue('outcome')}
        />
        <TextInput
          name="learning_skill_type"
          label={t('programPlan.course.learningOutcome.learningSkillType')}
          value={form.learning_skill_type}
          onChange={setFieldValue('learning_skill_type')}
        />
        <Button loading={isPending} disabled={isPending} type="submit" className="mt-4 ml-auto w-fit">
          {t('save')}
        </Button>
      </Form>
    </Modal>
  );
};

export default ProgramPlanCourseLearningOutcomeDialog;
