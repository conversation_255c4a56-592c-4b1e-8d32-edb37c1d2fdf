import { useGetTaskByCode, useMarkAsCopmlete } from '@/modules/app/tasks/apis/queries';
import { useParams } from 'react-router-dom';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { ICourse, ICourseTopic } from '@/modules/app/course/types';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { dateAndTimeFormat, getContentStatusStyle, getTaskStatusStyle } from '@/utils/helpers';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';
import AddChecklistDialog from '@/modules/app/components/add-checklist-dialog';
import { useAddChecklist } from '@/apis/tool-checklist/queries';
import i18n from '@/utils/i18n';
import TableContentHeader from '@/components/theTable/table-content-header';
import TableRowsExpander from '@/components/theTable/table-rows-expander';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import WorkflowStatusChecklist from '@/modules/app/components/work-flow-status-checklist';
import { IDNA } from '@/modules/app/dna/types';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Separator } from '@/components/ui/separator';
import NotesDialog from '../../components/notes-dialog';
import { Icon } from '@/components';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import EditDna from '../../components/edit-dna-task';
import { EditCourseDialog } from '../../components/edit-course-dialog';
import ChangeDnaData from '../../components/edit-dna-data-dialog';
import StatusClass from '@/utils/SystemStatusEnums';
import { ConditionalComponent } from '@/components/conditional-status-component';
import { ProtectedTaskComponent } from '../../components/protected-task-component';

const SignleTaskReviwer = () => {
  // State
  const [open, setOpen] = useState(false);
  const [notesDialog, setNotesDialog] = useState(false);
  const [dnaNotes, setDnaNotes] = useState<any>(null);
  const [isEditingCourseOpen, setIsEditingCourseOpen] = useState(false);
  const [changeDataDialog, setChangeDataDialog] = useState<boolean>(false);

  const { taskId } = useParams();
  const { data, isLoading } = useGetTaskByCode(taskId || '');
  const [editDialog, setEditDialog] = useState<any>(false);
  const [danData, setDanData] = useState<any>(null);
  const [selectedDna, setSelectedDna] = useState<any>(null);

  const { mutate: markAsComplete, isPending } = useMarkAsCopmlete();
  const { mutate: addChecklist, isPending: isPendingChecklist } = useAddChecklist();
  // Hooks
  const navigate = useLanguageNavigate();

  const { t } = useTranslation();
  const handleReadyForProduction = (checklist: (string | number)[]) => {
    if (data) {
      markAsComplete(
        { code: taskId },
        {
          onSuccess: () => {
            addChecklist(
              { check_list_ids: checklist, tool_id: data.content.tool_id, tool_type: 'Course' },
              {
                onSuccess: () => {
                  navigate('/app/my-tasks');
                },
              }
            );
          },
        }
      );
    }
  };

  const allTopicsReadyForProduction = data?.content?.tool_data?.topics?.every(
    (topic) => topic.topic_status?.action === 'ready_for_production' || topic.topic_status?.action === 'approved'
  );

  const columns: ITableColumn<ICourse & ICourseTopic & IDNA>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('CoursePlanCreationPage.table.title'),
        width: '350px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex gap-2 items-start py-1 rtl:text-right">
                  <div>
                    <p className="font-medium">{row.title}</p>
                    <p className="text-gray-500">{row.learning_objectives}</p>
                  </div>
                </div>
              );
            case 1:
              const element = !row.dna_content ? (
                <span>{row.title}</span>
              ) : (
                <p
                  className="text-primary font-medium underline cursor-pointer"
                  onClick={() => navigate(`/app/my-tasks/course/${data?.code}/dna/${row.id}`)}
                >
                  {row.title}
                </p>
              );
              return (
                <div className="rtl:text-right group">
                  <div className="flex gap-2 items-center p-0">
                    <div className={`break-words font-bold`}>{element}</div>
                    <div className=" opacity-0 group-hover:opacity-100 transition-opacity duration-300 ease-in-out transform group-hover:translate-y-0 ">
                      <div>
                        <ConditionalComponent
                          multiConditions={[
                            {
                              status: data?.content?.tool_data?.courseStatus,
                              wantedStatus: [StatusClass.COURSE.REVIEW.APPROVED],
                            },
                            { status: row.dna_status, wantedStatus: [StatusClass.DNA.REVIEW.APPROVED] },
                          ]}
                          operator="not"
                          multiConditionOperator="allTogetherShouldNotPass"
                        >
                          {permissionFinder('dna_edit') && row?.dna_status?.phase?.action != 'Production' && (
                            <Icon
                              onClick={() => {
                                setChangeDataDialog(true);
                                setSelectedDna(row);
                              }}
                              icon="basil:edit-outline"
                              width={25}
                              className="cursor-pointer text-primary"
                            />
                          )}
                        </ConditionalComponent>
                      </div>
                    </div>
                  </div>
                  <p className="font-medium text-gray-500">{row.learning_objectives}</p>
                  <WorkflowStatusChecklist dna={row} />
                </div>
              );
            default:
              return null;
          }
        },
      },

      {
        accessorKey: 'dnas',
        header: t('CoursePlanCreationPage.table.dnas'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return <div className="rtl:text-right">{row.dnas.length}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'length',
        header: t('CoursePlanCreationPage.table.lenght'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              const length = row.dnas.reduce(
                (acc, lesson) => acc + parseInt(lesson?.audio_length?.split('min')?.[0] || '0'),
                0
              );
              return (
                <div className="font-medium rtl:text-right">
                  <span>{length} min</span>
                </div>
              );
            case 1:
              return <div className="font-medium max-w-12 rtl:text-right">{row.audio_length}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'bloom_tax',
        header: t('CoursePlanCreationPage.table.bloomTax'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return null;
            case 1:
              return <div className="font-medium max-w-12 rtl:text-right">{row.bloom_tax?.name_en}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'status',
        header: t('CoursePlanCreationPage.table.status'),
        width: '120px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return getTaskStatusStyle(row?.topic_status);
            case 1:
              return getContentStatusStyle(row?.dna_status);
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '80px',

        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return null;
            case 1:
              return (
                <ProtectedTaskComponent requiredPermissions="dna_edit">
                  <ConditionalComponent
                    status={data?.content?.tool_data?.courseStatus}
                    wantedStatus={[StatusClass.COURSE.REVIEW.APPROVED]}
                    operator="not"
                  >
                    {row.dna_status.phase?.action != 'Production' && (
                      <Icon
                        onClick={() => {
                          setEditDialog(true);
                          setDanData(row);
                        }}
                        icon="basil:edit-outline"
                        width={25}
                        className="cursor-pointer text-primary"
                      />
                    )}
                  </ConditionalComponent>
                </ProtectedTaskComponent>
              );
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'reviewer_notes',
        header: t('CoursePlanCreationPage.table.reviewerNotes'),
        width: '150px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              const reviewerNotesCount = row.dnas?.filter((dna) => dna.reviewer_notes !== null).length || 0;
              return reviewerNotesCount > 0 ? reviewerNotesCount : '_';
            case 1:
              return row.reviewer_notes ? (
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger>
                      <Icon
                        onClick={() => {
                          setNotesDialog(true), setDnaNotes(row);
                        }}
                        icon={'tabler:notes'}
                        width={25}
                        className="text-primary cursor-pointer"
                      />
                    </TooltipTrigger>
                    <TooltipContent> {t('myTaskTable.reviewerNotes')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                '_'
              );
            default:
              return null;
          }
        },
      },
    ];
  }, [t, data]);

  const infoItems = [
    {
      label: t('taskData.accordion.code'),
      value: data?.code,
    },
    {
      label: t('taskData.accordion.description'),
      value: data?.description,
    },
    {
      label: t('taskData.accordion.priority'),
      value: data?.priority,
    },
    {
      label: t('taskData.accordion.dueDate'),
      value: dateAndTimeFormat(data?.due_date || '', i18n.language),
    },
    {
      label: t('taskData.accordion.taskStatus'),
      value: getContentStatusStyle(data?.task_status),
    },
  ];

  const permissionFinder = (
    permissionsName: 'dna_edit' | 'can_reviewed_dna' | 'can_report_dna' | 'can_add_dna_notes'
  ) => {
    return (
      data?.operations.some((operation) => {
        return operation.permissions.some((permission) => permission.name === permissionsName);
      }) || null
    );
  };

  return (
    <div>
      <div className=" bg-white dark:bg-background shadow-sm rounded-md mb-4">
        <Accordion type="single" collapsible>
          <AccordionItem className="border-none" value="item-1">
            <AccordionTrigger className="font-light hover:no-underline gap-5 bg-muted rounded-t-md border border-border p-4">
              <div className="flex justify-between w-full">
                <div className="flex gap-2">
                  <h3 className="text-xl flex capitalize items-center gap-1">{data?.content?.tool_data?.title}</h3>
                  <ConditionalComponent
                    status={data?.content?.tool_data?.courseStatus}
                    wantedStatus={[StatusClass.COURSE.REVIEW.APPROVED]}
                    operator="not"
                  >
                    {permissionFinder('dna_edit') &&
                      data?.content?.tool_data?.courseStatus?.phase?.action != 'Production' && (
                        <Icon
                          onClick={() => setIsEditingCourseOpen(true)}
                          icon="mynaui:edit-one"
                          width={22}
                          className="cursor-pointer mt-1.5 ml-1 text-primary"
                        />
                      )}
                  </ConditionalComponent>
                </div>
                <p className="text-sm">{getContentStatusStyle(data?.content?.tool_data?.courseStatus)}</p>
              </div>
              <Separator orientation="vertical" className="h-8" />
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div className="grid grid-cols-2  md:grid-cols-3 gap-4">
                {infoItems.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <p className="opacity-50">{item.label}</p>
                    <p>{item.value || '-'}</p>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      <Table
        rows={data?.content?.tool_data?.topics || []}
        columns={columns}
        loading={isLoading}
        nestedConfig={{
          enabled: true,
          childProperties: ['dnas'],
        }}
      >
        <TableContent>
          <TableContentHeader className="mt-0 flex justify-between">
            <div className="flex items-center gap-2">
              <TableColumsExpander />
              <p>{t('breadcrumb.myContentPage.DNAs')}</p>
              <TableRowsExpander />
            </div>
            <ProtectedTaskComponent requiredPermissions="course_approve">
              <Button
                loading={isPending}
                onClick={() => setOpen(true)}
                disabled={
                  (!allTopicsReadyForProduction && data?.task_status?.phase?.action === 'Review') ||
                  data?.task_status.action === 'completed'
                }
              >
                {data?.content?.tool_data?.courseStatus?.phase?.action === 'Production'
                  ? t('taskPhase.readyForProduction')
                  : t('editDnaModal.readyForProduction')}
              </Button>
            </ProtectedTaskComponent>
          </TableContentHeader>
          <TableContentBody />
        </TableContent>
      </Table>

      {editDialog && data && (
        <EditDna
          onOpen={editDialog}
          onOpenChange={() => {
            setEditDialog(false), setDanData(null);
          }}
          data={danData}
          course={data}
        />
      )}
      <div className="flex justify-end mt-5">
        {open && (
          <AddChecklistDialog
            open={open}
            setOpen={setOpen}
            callback={handleReadyForProduction}
            loading={isPending || isPendingChecklist}
            type="reviewrCourse"
          />
        )}
        {notesDialog && <NotesDialog isOpen={notesDialog} data={dnaNotes} setIsOpen={setNotesDialog} viewOnly />}
        {isEditingCourseOpen && data && (
          <EditCourseDialog task={data} open={isEditingCourseOpen} setOpen={setIsEditingCourseOpen} />
        )}{' '}
        {changeDataDialog && data && (
          <ChangeDnaData
            task={data}
            data={selectedDna}
            onOpen={changeDataDialog}
            onOpenChange={() => {
              setChangeDataDialog(false), setSelectedDna(null);
            }}
          />
        )}
      </div>
    </div>
  );
};

export default SignleTaskReviwer;
