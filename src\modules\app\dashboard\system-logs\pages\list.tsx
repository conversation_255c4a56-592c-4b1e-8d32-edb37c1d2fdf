import { useMemo, useState } from 'react';
import { useFetchList, Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
} from '@/components/theTable';
import { dateAndTimeFormat, getLogStatus } from '@/utils/helpers';
import { useHasPermission } from '@/modules/auth/store';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import LogsData from '../components/logs';
import { useAdminTablesTabs } from '@/modules/app/content-tabs';
import LanguageLink from '@/components/language-link';
import { Button } from '@/components/ui/button';

const SystemLogs = () => {
  const [logsData, selogsData] = useState(null);
  const [showLogs, setShowLogs] = useState(false);
  const adminTableTabs = useAdminTablesTabs();

  const { t, i18n } = useTranslation();

  const { ready, loading, list, count, refresh, search, filters, pagination } = useFetchList('/logs', 'logs', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'systemLogs.userFIlter',
            },
          }
        : {}),
      action: {
        enum: 'logsActions',
        placeholder: 'dnaConentPgae.logActionFilter',
      },
      model: {
        enum: 'logsModules',
        placeholder: 'dnaConentPgae.logModelFilter',
      },
    },
  });

  const columns: ITableColumn<any>[] = useMemo(() => {
    return [
      {
        accessorKey: 'action',
        header: t('systemLogs.table.actions'),
        width: '50px',
        cell: ({ row }) => {
          return (
            <div className={`px-4 text-center py-1.5 rounded-md whitespace-nowrap ${getLogStatus(row?.action)}`}>
              {row?.action}
            </div>
          );
        },
      },
      {
        accessorKey: 'user.name',
        header: t('dnaConentPgae.table.author'),
        width: '100px',
        cell: ({ row }) => {
          return (
            <div className="text-start space-y-1">
              <p>{row?.user?.name}</p>
              <p>{row?.user?.email}</p>
            </div>
          );
        },
      },
      {
        accessorKey: 'model_type',
        header: t('systemlogs.table.modelType'),
        width: '80px',
        cell: ({ row }) => {
          return <div className="text-start">{row.log_name === 'Dna' ? t('DNA') : t(row.log_name)}</div>;
        },
      },
      {
        accessorKey: 'created_at',
        header: t('dnaConentPgae.table.date'),
        width: '100px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'id',
        header: t('systemlogs.table.content'),
        width: '100px',
        cell: ({ row }) => {
          let url;
          const modalType = row.model_type.includes('\\') ? row.model_type.split('\\').pop() : row.model_type;

          if (modalType === 'Dna') {
            url = `/app/my-content/DNAs/${row.model_id}`;
          } else if (modalType === 'Course') {
            url = `/app/my-content/courses/${row.model_id}`;
          }
          return (
            <div className="rtl:text-right">
              {modalType !== 'Topic' ? (
                <LanguageLink to={url}>
                  <Button>{`${t(`dashboard.seeThis${modalType}`)}`}</Button>
                </LanguageLink>
              ) : (
                t('noLinkForThisTopic')
              )}
            </div>
          );
        },
      },
      {
        accessorKey: 'logs',
        header: t('dnaConentPgae.table.logs'),
        width: '70px',
        cell: ({ row }) => {
          return (
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger>
                  <Icon
                    onClick={() => {
                      setShowLogs(true);
                      selogsData(row);
                    }}
                    icon="lets-icons:eye-light"
                    width={25}
                    className="text-primary cursor-pointer"
                  />
                </TooltipTrigger>
                <TooltipContent>{t('logs')}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        },
      },
    ];
  }, [t]);

  return (
    <div>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={adminTableTabs} className="justify-between">
            <div className="flex gap-3 items-center">
              {/* <TableSearch className="w-fit" search={search} placeholder={t('search')} /> */}
              <TableFilters filters={filters} />
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>

      {showLogs && logsData && <LogsData isOpen={showLogs} onOpenChange={setShowLogs} data={logsData} />}
    </div>
  );
};

export default SystemLogs;
