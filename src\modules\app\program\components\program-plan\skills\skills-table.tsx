import { useState } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { SkillCustomeRow } from './skill-custome-row';

import { Button } from '@/components/ui/button';
import ProgramEditDialog from './skill-edit';
import { programPlanAtom } from '../../../store';
import { useDeleteSkill } from '../../../apis/queries';
const SkillsTable = ({ list }: any) => {
  //  State
  const [data] = useAtom(programPlanAtom);

  const { mutate: deleteSkill, isPending: isDeleting, variables } = useDeleteSkill();

  const [selectedRows, setSelectedRows] = useState<any>([]);
  // const [data] = useAtom(programPlanAtom);
  const [learningOutcomeDialogOpen, setLearningOutcomeDialogOpen] = useState(false);
  const [rowBeingEdited, setRowBeingEdited] = useState<{
    program_skill_id: number;
    program_id: number;
    name: string;
    description: string;
    skill_type: string;
  }>({
    program_id: 0,
    program_skill_id: 0,
    name: '',
    description: '',
    skill_type: '',
  });

  //  Hooks
  const { t } = useTranslation();

  return (
    <>
      <div className="mt-[-1.75rem] flex items-center justify-center ">
        <div className="max-w-[1500px] w-full bg-background  p-6">
          {/* <Table
            slots={{
              actions: (_: any, row: any) => {
                return (
                  <div className="flex">
                    <Button
                      size="icon"
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setRowBeingEdited({
                          program_id: data.id,
                          program_skill_id: row.id,
                          description: row.description,
                          skill_type: row.skill_type,
                          name: row.name,
                        });
                        setLearningOutcomeDialogOpen(true);
                      }}
                      className="border-primary"
                      variant={'ghost'}
                    >
                      <Icon icon="basil:edit-outline" width={25} className="text-primary cursor-pointer" />
                    </Button>

                    <Button
                      size="icon"
                      loading={isDeleting && variables.program_skill_id === row.id}
                      disabled={isDeleting && variables.program_skill_id === row.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteSkill({ id: data.id, program_skill_id: row.id });
                      }}
                      variant={'ghost'}
                    >
                      <Icon icon="gg:trash" width={25} className="text-red-500 cursor-pointer" />
                    </Button>
                  </div>
                );
              },
            }}
            breakpoint={'lg'}
            ready={true}
            loading={false}
            rows={list}
            count={list?.length}
            title={'Skills'}
            jumbotron={false}
            searchInupt={false}
            customeRows={<SkillCustomeRow id={data?.id} />}
            selectedRows={selectedRows}
            onSelectedRowsChange={setSelectedRows}
            isMultiSelectTable={false}
            columns={[
              {
                key: 'name',
                label: t('contentNeed.steps.skills'),
              },
              {
                key: 'description',
                label: t('assignedTask.table.description'),
              },
              {
                key: 'skill_type',
                label: t('contentNeed.steps.skills.table.type'),
                width: '250px',
              },
              {
                key: 'actions',
                label: t('topicContentPage.table.actions'),
                width: '100px',
              },
            ]}
          /> */}
          {learningOutcomeDialogOpen && (
            <ProgramEditDialog
              isOpen={learningOutcomeDialogOpen}
              setIsOpen={() => setLearningOutcomeDialogOpen(false)}
              rowBeingEdited={rowBeingEdited}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default SkillsTable;
