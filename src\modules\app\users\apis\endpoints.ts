import { IPasswordPayload, ICreateUser } from '../types';
import { IUser } from '@/modules/auth/types';
import { Api } from '@/services';
import { CHANGE_STATUS_USER, CHANGE_PASSWORD, CREATE_USER } from '@/services';

// Toggle User status between active and blocked
export const toggleUserStatus = async (id: number): Promise<void> => {
  await Api.put(`${CHANGE_STATUS_USER}/${id}`);
};

// Toggle Users status between active and blocked
export const toggleUsersStatus = async ({
  ids,
  status,
}: {
  ids: number[];
  status: 'active' | 'blocked';
}): Promise<void> => {
  await Api.put(`/user/users/change-statuses`, { ids, status });
};

// Update user password
export const updatedUserPassword = async (id: number, payload: IPasswordPayload): Promise<void> => {
  await Api.put(`${CHANGE_PASSWORD}/${id}`, payload);
};

export const editUser = async ({
  name,
  email,
  password,
  role,
  account_status,
  id,
}: {
  name: string;
  email: string;
  password: string;
  role: string;
  account_status: string;
  id: number;
}): Promise<void> => {
  await Api.put(`user/${id}`, { name, email, password, role, account_status });
};

// Create new user
export const createUser = async (payload: ICreateUser): Promise<void> => {
  await Api.post(CREATE_USER, payload);
};

// Get user by ID
export const getUserById = async (id: string): Promise<IUser> => {
  const { data } = await Api.get(`${CREATE_USER}/${id}`);
  return data.data;
};

export const updateProfile = async (payload: {
  name: string;
  info: string;
  profession: string;
  education: string;
  university: string;
}) => {
  const { data } = await Api.post(
    `/auth/update-profile`,
    { _method: 'PATCH', ...payload },
    {
      'Content-Type': 'multipart/form-data',
    }
  );
  return data.data;
};
