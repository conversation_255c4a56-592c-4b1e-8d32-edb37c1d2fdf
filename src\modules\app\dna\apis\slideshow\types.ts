// Endpoint Parameter Types
export interface GenerateSlideshowParams {
  id: string | number;
  slideshow_sound: string;
  language_id: string | number;
}

export interface AddSlideParams {
  id?: string | number;
  taskCode?: string | number;
  dnaId?: string | number;
  template: string;
  prompt: string;
  voice_over_sound: string;
  next_slide_id?: string | number | null;
  slideshow_id?: string | number;
}

// Response Types
export interface Language {
  id: number | string;
  type: string;
  name_en: string;
  name_ar: string;
  description: string;
  deleted_at: string;
}

// New Slides Types

export interface ISlideShow {
  id: number;
  title: string;
  status: string;
  slides_count: number;
  dna_id: number;
  version: string;
  language: IMetadata;
  created_by: {
    id: number;
    name: string;
    email: string;
  };
  created_at: string;
  prompt: string;
  model: string;
}

// ISingleSlideShow mix bettwen  ISlideShow  exluding   language  an created_by
export interface ISingleSlideShow {
  id: number;
  title: string;
  status: string;
  slides_count: number;
  slides: ISlide[];
  dna_id: number;
  version: string;
  created_at: string;
  uuid: string;
}

export interface ISheredSlideShow {
  title: string;
  slideshow_uuid: string;
  slides_count: number;
  slides: IShedredSlide[];
}

export interface IShedredSlide {
  content: string;
  title: string;
  voice_over: string;
  template: string;
  voice_over_url: string;
}

export interface ISlide {
  id: number;
  content: string;
  title: string;
  voice_over: string;
  sound_type: string;
  slide_number: number;
  template: string;
  order: string;
  media: any[];
  voice_over_url: string;
  parent: null;
}
