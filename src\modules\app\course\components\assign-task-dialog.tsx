import {
  Modal,
  useForm,
  Form,
  useValidate,
  CalendarInput,
  ComboboxInput,
  MultiSelect,
  DnaEnums,
  Textarea,
} from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useGetOperations } from '@/modules/app/dashboard/operation/apis/queries';
import moment from 'moment';
import { useAssignTask, useGetUsers } from '@/modules/app/tasks/apis/queries';
import { useGetParentsTaskCodes } from '../apis/queries';
import { useEffect } from 'react';

const AssignTaskDialog = ({
  onOpen,
  onClose,
  course,
  type = 'task',
  canCreateChildTask = false,
}: {
  onOpen: boolean;
  onClose: () => void;
  course: any;
  type?: 'childTask' | 'task';
  canCreateChildTask?: boolean;
}) => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { data: operations } = useGetOperations();
  const { t } = useTranslation();
  const { mutate: assign, isPending: pending } = useAssignTask();
  const { data: users } = useGetUsers();
  const operationsOptions = operations?.map((operation) => ({ label: operation.name, value: operation.id })) || [];
  const { data } = useGetParentsTaskCodes(course?.id || course?.content?.tool_id, canCreateChildTask);
  const { form, setFieldValue } = useForm({
    assigned_to: null,
    content_type: 'course',
    priority: null,
    due_date: new Date(new Date().getTime() + 24 * 60 * 60 * 1000),
    description: '',
    operations: [],
    parent_task_id: data?.parent_task_code || null,
  });

  useEffect(() => {
    if (data) {
      setFieldValue('parent_task_id')(data?.parent_task_code);
    }
  }, [data]);
  const handleSubmit = () => {
    const formattedDate = moment(form.due_date).format('YYYY-MM-DD');
    const finalForm = {
      ...form,
      due_date: formattedDate,
      content_id: course?.content?.tool_id || course?.id,
    };
    assign(finalForm, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Modal
      className="overflow-visible"
      width={800}
      open={onOpen}
      onOpenChange={onClose}
      modalHeader={data?.parent_task_code.length > 0 ? t('assignChildTaskDialog.title') : t('assignTaskDialog.title')}
    >
      <Form onSubmit={handleSubmit} className="min-h-96 ">
        <div className="flex gap-4">
          <div className="flex flex-col gap-3">
            <ComboboxInput
              name="reviewer"
              label={t('assignTask.form.reviewer')}
              placeholder={t('assignTask.form.assigned_to')}
              options={users?.items || []}
              optionLabelKey={'email'}
              optionValueKey="id"
              value={form.assigned_to}
              onChange={setFieldValue('assigned_to')}
              dropIcon
              validators={[isRequired()]}
            />
            <CalendarInput
              label={t('assignTask.form.due_date')}
              name="due_date"
              value={form.due_date}
              onChange={setFieldValue('due_date')}
              validators={[isRequired()]}
              disabled={(date: Date) =>
                date < new Date(new Date().getTime() + 8 * 60 * 60 * 1000) || date < new Date('1900-01-01')
              }
            />
          </div>
          <div className="w-full flex flex-col gap-1">
            <MultiSelect
              name="operations"
              label={t('operations')}
              options={operationsOptions}
              onChange={setFieldValue('operations')}
              value={form.operations || []}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={2}
              validators={[isNotEmptyArray()]}
            />
            <Textarea
              rows={6}
              label={t('metadataPage.description')}
              value={form.description}
              onChange={setFieldValue('description')}
              className="w-full p-2 border rounded mb-3.5"
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="priority"
              label={t('assignTask.form.priority')}
              placeholder={t('assignTask.form.priority')}
              options={DnaEnums.TaskPriorityStatus}
              value={form.priority}
              onChange={setFieldValue('priority')}
              dropIcon
              validators={[isRequired()]}
            />
            {data?.parent_task_code.length > 0 && (
              <ComboboxInput
                name="taskCode"
                label={t('assignTask.form.taskCode')}
                placeholder={t('assignTask.form.taskCode')}
                options={[data]}
                optionLabelKey="parent_task_code"
                optionValueKey="parent_task_code"
                value={form.parent_task_id}
                onChange={setFieldValue('parent_task_id')}
              />
            )}
          </div>
        </div>
        <div className="pt-1 mt-5 border-t border-border flex justify-end">
          <Button loading={pending} disabled={pending} type="submit" className="mt-4 w-fit">
            {t('task.assignPopup.button')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default AssignTaskDialog;
