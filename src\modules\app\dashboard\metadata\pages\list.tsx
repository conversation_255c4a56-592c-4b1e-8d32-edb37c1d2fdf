import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import NewMetadataDialog from '../components/create';
import EditMetadataDialog from '../components/edit';
import { useFetchList, Icon, useConfirmDialog } from '@/index';
import { useTranslation } from 'react-i18next';
import { useDeleteMetadata, useExportTypes, useImportTypes } from '@/modules/app/dashboard/metadata/apis/queries';
import StoredMetaDataDialog from '../components/stored';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
  TableHeader,
  TableBulkActions,
} from '@/components/theTable';
import { File } from 'lucide-react';
import { useAdminTablesTabs } from '@/modules/app/content-tabs';

const MetaData = () => {
  const [isNewMetadataDialog, setIsNewMetadataDialog] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isStoredDialogOpen, setIsStoredDialogOpen] = useState(false);
  const [selectedMetaData, setSelectedMetaData] = useState({});
  const [file, setFile] = useState<File | null>(null);
  const { mutate: deleteMetadata, isPending, variables } = useDeleteMetadata();
  const { mutate: exportTypes, isPending: isExporting } = useExportTypes();
  const { mutate: importTypes, isPending: isImporting } = useImportTypes();
  const adminTableTabs = useAdminTablesTabs();

  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { t } = useTranslation();
  const handleEdit = (row: any) => {
    setSelectedMetaData(row);
    setIsEditDialogOpen(true);
  };
  const handleFileUpload = (file: File) => {
    setFile(file);
  };
  const { ready, loading, list, count, refresh, search, filters, pagination } = useFetchList('/types', 'types', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      type: {
        enum: 'metadata',
        placeholder: 'metadata.filters.selectType',
      },
    },
  });

  const DnaConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('metadataList.confirmationText')}</p>
      </div>
    );
  };

  const handleDeleteMetaData = async (id: any) => {
    showConfirm(DnaConfirmText(), {
      async onConfirm() {
        deleteMetadata(id, {
          onSuccess: () => {
            refresh(), hideConfirm();
          },
        });
      },
    });
  };

  const columns: ITableColumn<IMetadata>[] = useMemo(() => {
    return [
      {
        accessorKey: 'id',
        header: 'Id',
        width: '50px',
      },
      {
        accessorKey: 'type',
        header: 'Type',
        width: '150px',
      },
      {
        accessorKey: 'name_ar',
        header: 'Name_ar',
        width: '130px',
      },
      {
        accessorKey: 'name_en',
        header: 'Name_en',
        width: '130px',
      },
      {
        accessorKey: 'description',
        header: 'Description',
        width: '400px',
        cell: ({ row }) => {
          return <div className="text-start truncate">{row.description}</div>;
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '80px',
        cell: ({ row }) => {
          return (
            <div className="flex justify-start">
              <Button
                onClick={() => handleDeleteMetaData(row.id)}
                loading={isPending && row.id === variables}
                disabled={isPending}
                variant="ghost"
                size={'icon'}
              >
                <Icon icon="bxs:hide" width={24} className="text-primary" />
              </Button>

              <Button variant="ghost" size={'icon'}>
                <Icon
                  onClick={() => handleEdit(row)}
                  icon="solar:pen-new-square-linear"
                  width={21}
                  className="text-primary"
                />
              </Button>
            </div>
          );
        },
      },
    ];
  }, []);

  return (
    <div>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={adminTableTabs} className="justify-between">
            <div className="flex gap-3 items-center">
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="ms-auto flex gap-3 items-center">
              <div className="flex items-center gap-2">
                <Popover>
                  <PopoverTrigger>
                    <Button variant={'outline'} className="flex items-center gap-2">
                      <Icon icon="solar:cloud-upload-linear" width={20} />
                      {t('dashboardPage.keys.actions.Import')}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent>
                    {file ? (
                      <div className="flex justify-between items-center flex-col gap-3 py-4">
                        <File className="h-8 w-8 mx-auto text-muted-foreground" />
                        <div className="text-lg font-semibold">{file.name}</div>
                        <div className="flex gap-2">
                          <Button
                            loading={isImporting}
                            disabled={isImporting}
                            className="px-6 h-9"
                            onClick={() => importTypes({ excel: file }, { onSuccess: () => setFile(null) })}
                          >
                            {t('import')}
                          </Button>
                          <Button className="px-6 h-9" variant="secondary" onClick={() => setFile(null)}>
                            {t('remove')}
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex justify-center gap-3 flex-col items-center text-center py-5">
                        <File className="h-8 w-8 mx-auto text-muted-foreground" />
                        <div className="space-y-2">
                          <p className="text-sm font-medium">{t('dashboardPage.keys.actions.import.clickToBrowse')}</p>
                          <p className="text-xs text-muted-foreground">Supports Excel file up to 10MB</p>
                          <label htmlFor="image-upload">
                            <Input
                              id="image-upload"
                              type="file"
                              accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                              className="hidden"
                              onChange={(e) => {
                                if (e.target.files?.[0]) {
                                  handleFileUpload(e.target.files[0]);
                                }
                              }}
                            />
                            <Button
                              variant="secondary"
                              className="mx-auto mt-2"
                              size="sm"
                              type="button"
                              onClick={() => document.getElementById('image-upload')?.click()}
                            >
                              Browse Files
                            </Button>
                          </label>
                        </div>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
                <Button
                  loading={isExporting}
                  disabled={isExporting}
                  className="flex items-center gap-2"
                  variant="outline"
                  onClick={() => exportTypes()}
                >
                  <Icon icon="solar:cloud-download-linear" width={20} />
                  {t('export')}
                </Button>
              </div>
              <Button variant={'outline'} onClick={() => setIsStoredDialogOpen(true)}>
                Stored MetaData
              </Button>
              <Button onClick={() => setIsNewMetadataDialog(true)}>Add new MetaData</Button>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>

      {isNewMetadataDialog && (
        <NewMetadataDialog isOpen={isNewMetadataDialog} onOpenChange={setIsNewMetadataDialog} refresh={refresh} />
      )}
      {isStoredDialogOpen && (
        <StoredMetaDataDialog isOpen={isStoredDialogOpen} onOpenChange={setIsStoredDialogOpen} refreshTable={refresh} />
      )}
      {isEditDialogOpen && selectedMetaData && (
        <EditMetadataDialog
          isOpen={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
          refresh={refresh}
          metadata={selectedMetaData}
        />
      )}
    </div>
  );
};

export default MetaData;
