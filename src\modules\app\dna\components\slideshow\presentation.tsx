import React, { useRef, useCallback, useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
import { Navigation, Keyboard } from 'swiper/modules';
import { Modal } from '@/index';
import 'react-h5-audio-player/lib/styles.css';
import { ISlide } from '@/modules/app/dna/apis/slideshow/types';
import { useAtom } from 'jotai';
import {
  captionsAtom,
  currentTrackIndexAtom,
  isControlsVisibleAtom,
  isFullscreenAtom,
  atomsCleaner,
  totalDurationAtom,
} from '../../store';

import AudioPlayer from './player/audio-player';
import { t } from 'i18next';

interface PresentationProps {
  slides: ISlide[];
  onOpenChange: () => void;
  isOpen: boolean;
  isEmbedded?: boolean;
}

const SwiperPresentation: React.FC<PresentationProps> = ({ slides, onOpenChange, isOpen, isEmbedded = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState<number>(1);

  const [audioTracks, setAudioTracks] = useState<{ url: string; duration: number; startTime: number; index: number }[]>(
    []
  );
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();
  const initialControlsTimeoutRef = useRef<NodeJS.Timeout>();
  const [isControlsVisible, setIsControlsVisible] = useAtom(isControlsVisibleAtom);
  const [, setCurrentTrackIndex] = useAtom(currentTrackIndexAtom);
  const [captions] = useAtom(captionsAtom);
  const [isFullscreen, setIsFullscreen] = useAtom(isFullscreenAtom);
  const [_, setAtomsCleaner] = useAtom(atomsCleaner);
  const [totalDuration, setTotalDuration] = useAtom(totalDurationAtom);
  const [hasInitialControlsShown, setHasInitialControlsShown] = useState(false);

  const showControls = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    setIsControlsVisible(true);
    hideControls();
  };

  const hideControls = () => {
    controlsTimeoutRef.current = setTimeout(() => {
      setIsControlsVisible(false);
    }, 3000);
  };

  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
        setAtomsCleaner(null);
      }
    };
  }, []);

  // Show controls for 10 seconds on initial load in embedded mode
  useEffect(() => {
    if (isEmbedded && !hasInitialControlsShown) {
      setIsControlsVisible(true);
      setHasInitialControlsShown(true);

      // Hide controls after 10 seconds
      initialControlsTimeoutRef.current = setTimeout(() => {
        setIsControlsVisible(false);
      }, 5000);
    }

    // Cleanup timeout on unmount
    return () => {
      if (initialControlsTimeoutRef.current) {
        clearTimeout(initialControlsTimeoutRef.current);
      }
    };
  }, [isEmbedded, hasInitialControlsShown, setIsControlsVisible]);

  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!document.fullscreenElement) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if ((containerRef.current as any).webkitRequestFullscreen) {
        (containerRef.current as any).webkitRequestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      }
      setIsFullscreen(false);
    }
  }, []);

  useEffect(() => {
    const initializeAudioTracks = async () => {
      const tracksToProcess = slides
        .map((slide, index) => ({
          url: slide.voice_over_url,
          index,
          duration: 0,
          startTime: 0,
        }))
        .filter((track) => track.url);

      if (tracksToProcess.length === 0) {
        setAudioTracks([]);
        setTotalDuration(0);
        return;
      }
      setAudioTracks(tracksToProcess);

      const loadDurationPromises = tracksToProcess.map(
        (track) =>
          new Promise<{ index: number; duration: number }>((resolve) => {
            const audio = new Audio(track.url);

            audio.addEventListener('loadedmetadata', () => {
              resolve({ index: track.index, duration: audio.duration });
            });

            audio.addEventListener('error', () => {
              resolve({ index: track.index, duration: 0 });
            });
          })
      );

      const loadedDurations = await Promise.all(loadDurationPromises);

      let cumulativeTime = 0;
      const updatedTracks = [...tracksToProcess].map((track) => {
        const loadedTrack = loadedDurations.find((t) => t.index === track.index);
        const duration = loadedTrack ? loadedTrack.duration : 0;
        const startTime = cumulativeTime;
        cumulativeTime += duration;

        return {
          ...track,
          duration,
          startTime,
        };
      });

      setAudioTracks(updatedTracks);
      setTotalDuration(cumulativeTime);
    };

    if (isOpen && slides.length > 0) {
      initializeAudioTracks();
    }
  }, [isOpen, slides, setTotalDuration]);

  const handleSlideChange = useCallback(
    (swiper: any) => {
      const currentIndex = swiper.activeIndex;
      setActiveSlide(currentIndex + 1);

      const trackIndex = audioTracks.findIndex((track) => track.index === currentIndex);
      if (trackIndex !== -1) {
        setCurrentTrackIndex(trackIndex);
      }
    },
    [audioTracks, setCurrentTrackIndex]
  );

  const handleTrackChange = (trackIndex: number) => {
    if (sliderRef.current) {
      sliderRef.current.swiper.slideTo(audioTracks[trackIndex].index);
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    };
  }, []);

  const renderContent = () => (
    <>
      <div
        onDoubleClick={toggleFullscreen}
        className={`flex-grow flex items-center justify-center ${isEmbedded ? 'w-full h-full' : 'w-[80%] mx-auto'}`}
      >
        <Swiper
          ref={sliderRef}
          modules={[Navigation, Keyboard]}
          spaceBetween={50}
          slidesPerView={1}
          effect="fade"
          className={`overflow-hidden relative ${
            isFullscreen ? '' : isEmbedded ? '' : '[&_.swiper-wrapper]:w-[1300px]'
          } text-center`}
          onSlideChange={handleSlideChange}
          keyboard={{ enabled: true }}
        >
          {slides?.map((slide, index) => (
            <SwiperSlide key={slide.slide_number}>
              <div
                className={`[&_.ck-content]:space-y-7 [&_.ck-content]:flex [&_.ck-content]:items-center [&_.ck-content]:justify-center rounded-md shadow-m w-full flex justify-center items-center ${
                  isFullscreen
                    ? 'fullscreen-slideshow-container'
                    : isEmbedded
                    ? 'custom-slideshow-container h-[550px]'
                    : 'custom-slideshow-container h-[550px]'
                }`}
                dangerouslySetInnerHTML={{ __html: slide.content }}
              ></div>
              {captions && (
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-full max-w-2xl px-4 bg-background rounded-md">
                  <p className="p-2 bg-background backdrop-blur-sm text-center text-sm">{slide.voice_over}</p>
                </div>
              )}
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div className="relative z-10">
        {audioTracks.length > 0 && (
          <AudioPlayer
            audioTracks={audioTracks}
            setAudioTracks={setAudioTracks}
            slides={slides}
            onTrackChange={handleTrackChange}
            toggleFullscreen={toggleFullscreen}
          />
        )}
      </div>
    </>
  );

  return isEmbedded ? (
    <div
      onMouseEnter={showControls}
      onMouseLeave={hideControls}
      onMouseMove={showControls}
      ref={containerRef}
      dir="ltr"
      className={`relative w-full ${
        isFullscreen ? 'bg-background h-screen w-screen flex flex-col justify-between' : 'flex flex-col'
      } ${isControlsVisible ? '' : 'cursor-none'}`}
    >
      {isOpen && renderContent()}
    </div>
  ) : (
    <Modal
      open={isOpen}
      onOpenChange={onOpenChange}
      width="1500px"
      modalHeader={`${t('slide')} ${activeSlide} : ${slides[activeSlide - 1]?.title}`}
    >
      <div
        onMouseEnter={showControls}
        onMouseLeave={hideControls}
        onMouseMove={showControls}
        ref={containerRef}
        dir="ltr"
        className={`overflow-hidden relative ${
          isFullscreen ? 'bg-background h-screen w-screen flex flex-col justify-between' : ''
        } ${isControlsVisible ? '' : 'cursor-none'}`}
      >
        {isOpen && renderContent()}
      </div>
    </Modal>
  );
};

export default SwiperPresentation;
