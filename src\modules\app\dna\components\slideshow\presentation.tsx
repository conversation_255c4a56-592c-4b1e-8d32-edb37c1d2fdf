import React, { useRef, useCallback, useEffect, useState } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';
import 'swiper/css/effect-fade';
import { Navigation, Keyboard } from 'swiper/modules';
import { Modal, Icon } from '@/index';
import 'react-h5-audio-player/lib/styles.css';
import { ISlide } from '@/modules/app/dna/apis/slideshow/types';
import { useAtom } from 'jotai';
import {
  captionsAtom,
  currentTrackIndexAtom,
  isControlsVisibleAtom,
  isFullscreenAtom,
  atomsCleaner,
  totalDurationAtom,
} from '../../store';

import AudioPlayer from './player/audio-player';
import { t } from 'i18next';
import { Button } from '@/components/ui/button';

interface PresentationProps {
  slides: ISlide[];
  onOpenChange: () => void;
  isOpen: boolean;
  isEmbedded?: boolean;
}

const SwiperPresentation: React.FC<PresentationProps> = ({ slides, onOpenChange, isOpen, isEmbedded = false }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<any>(null);
  const [activeSlide, setActiveSlide] = useState<number>(1);

  const [audioTracks, setAudioTracks] = useState<{ url: string; duration: number; startTime: number; index: number }[]>(
    []
  );
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();
  const initialControlsTimeoutRef = useRef<NodeJS.Timeout>();
  const [isControlsVisible, setIsControlsVisible] = useAtom(isControlsVisibleAtom);
  const [, setCurrentTrackIndex] = useAtom(currentTrackIndexAtom);
  const [captions] = useAtom(captionsAtom);
  const [isFullscreen, setIsFullscreen] = useAtom(isFullscreenAtom);
  const [_, setAtomsCleaner] = useAtom(atomsCleaner);
  const [totalDuration, setTotalDuration] = useAtom(totalDurationAtom);
  const [showCenterPlayButton, setShowCenterPlayButton] = useState(false);
  const [isAudioPlaying, setIsAudioPlaying] = useState(false);

  const showControls = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    setIsControlsVisible(true);
    if (isEmbedded) {
      setShowCenterPlayButton(true);
    }
    hideControls();
  };

  const hideControls = () => {
    controlsTimeoutRef.current = setTimeout(() => {
      setIsControlsVisible(false);
      if (isEmbedded) {
        setShowCenterPlayButton(false);
      }
    }, 3000);
  };

  const hideControlsImmediate = () => {
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    setIsControlsVisible(false);
    if (isEmbedded) {
      setShowCenterPlayButton(false);
    }
  };

  useEffect(() => {
    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current);
        setAtomsCleaner(null);
      }
    };
  }, []);

  useEffect(() => {
    if (isEmbedded) {
      setIsControlsVisible(true);
      setShowCenterPlayButton(true);
      initialControlsTimeoutRef.current = setTimeout(() => {
        setIsControlsVisible(false);
        setShowCenterPlayButton(false);
      }, 5000);
    }

    return () => {
      if (initialControlsTimeoutRef.current) {
        clearTimeout(initialControlsTimeoutRef.current);
      }
    };
  }, [isEmbedded, setIsControlsVisible]);

  // Monitor audio state for center play button
  useEffect(() => {
    const updateAudioState = () => {
      const audioElement = document.querySelector('audio') as HTMLAudioElement;
      if (audioElement) {
        setIsAudioPlaying(!audioElement.paused);
      }
    };

    const audioElement = document.querySelector('audio') as HTMLAudioElement;
    if (audioElement) {
      audioElement.addEventListener('play', updateAudioState);
      audioElement.addEventListener('pause', updateAudioState);
      audioElement.addEventListener('ended', updateAudioState);

      // Initial state
      updateAudioState();

      return () => {
        audioElement.removeEventListener('play', updateAudioState);
        audioElement.removeEventListener('pause', updateAudioState);
        audioElement.removeEventListener('ended', updateAudioState);
      };
    }
  }, [audioTracks]);

  const toggleFullscreen = useCallback(() => {
    if (!containerRef.current) return;

    if (!document.fullscreenElement) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      } else if ((containerRef.current as any).webkitRequestFullscreen) {
        (containerRef.current as any).webkitRequestFullscreen();
      }
      setIsFullscreen(true);
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      }
      setIsFullscreen(false);
    }
  }, []);

  useEffect(() => {
    const initializeAudioTracks = async () => {
      const tracksToProcess = slides
        .map((slide, index) => ({
          url: slide.voice_over_url,
          index,
          duration: 0,
          startTime: 0,
        }))
        .filter((track) => track.url);

      if (tracksToProcess.length === 0) {
        setAudioTracks([]);
        setTotalDuration(0);
        return;
      }
      setAudioTracks(tracksToProcess);

      const loadDurationPromises = tracksToProcess.map(
        (track) =>
          new Promise<{ index: number; duration: number }>((resolve) => {
            const audio = new Audio(track.url);

            audio.addEventListener('loadedmetadata', () => {
              resolve({ index: track.index, duration: audio.duration });
            });

            audio.addEventListener('error', () => {
              resolve({ index: track.index, duration: 0 });
            });
          })
      );

      const loadedDurations = await Promise.all(loadDurationPromises);

      let cumulativeTime = 0;
      const updatedTracks = [...tracksToProcess].map((track) => {
        const loadedTrack = loadedDurations.find((t) => t.index === track.index);
        const duration = loadedTrack ? loadedTrack.duration : 0;
        const startTime = cumulativeTime;
        cumulativeTime += duration;

        return {
          ...track,
          duration,
          startTime,
        };
      });

      setAudioTracks(updatedTracks);
      setTotalDuration(cumulativeTime);
    };

    if (isOpen && slides.length > 0) {
      initializeAudioTracks();
    }
  }, [isOpen, slides, setTotalDuration]);

  const handleSlideChange = useCallback(
    (swiper: any) => {
      const currentIndex = swiper.activeIndex;
      setActiveSlide(currentIndex + 1);

      const trackIndex = audioTracks.findIndex((track) => track.index === currentIndex);
      if (trackIndex !== -1) {
        setCurrentTrackIndex(trackIndex);
      }
    },
    [audioTracks, setCurrentTrackIndex]
  );

  const handleTrackChange = (trackIndex: number) => {
    if (sliderRef.current) {
      sliderRef.current.swiper.slideTo(audioTracks[trackIndex].index);
    }
  };

  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    };
  }, []);

  const renderContent = () => (
    <>
      <div
        onDoubleClick={toggleFullscreen}
        className={`flex-grow flex items-center justify-center relative ${isEmbedded ? '' : 'w-[80%] mx-auto'}`}
      >
        {/* Center Play Button for Embedded Mode */}
        {isEmbedded && !isFullscreen && (
          <div
            className={`absolute inset-0 flex items-center justify-center z-20 pointer-events-none transition-all duration-300 ease-in-out ${
              showCenterPlayButton ? 'opacity-100 scale-100' : 'opacity-0 scale-95 pointer-events-none'
            }`}
          >
            <Button
              variant="ghost"
              size={'icon'}
              className={`rounded-full p-6 transition-all duration-300 ease-in-out pointer-events-auto border-0   ${
                showCenterPlayButton ? 'scale-100 opacity-70' : 'scale-90 opacity-0'
              }`}
              onClick={() => {
                const audioElement = document.querySelector('audio') as HTMLAudioElement;
                if (audioElement) {
                  if (audioElement.paused) {
                    audioElement.play().catch(console.error);
                  } else {
                    audioElement.pause();
                  }
                }
              }}
            >
              <div className={`transition-all duration-200 ${isAudioPlaying ? 'scale-110' : 'scale-100'}`}>
                {isAudioPlaying ? (
                  <Icon icon="solar:pause-bold" className="text-primary drop-shadow-lg" width={70} />
                ) : (
                  <Icon icon="solar:play-bold" className="text-primary drop-shadow-lg" width={70} />
                )}
              </div>
            </Button>
          </div>
        )}
        <Swiper
          ref={sliderRef}
          modules={[Navigation, Keyboard]}
          spaceBetween={50}
          slidesPerView={1}
          effect="fade"
          className={` relative ${isFullscreen ? '' : isEmbedded ? '' : '[&_.swiper-wrapper]:w-[1300px]'} text-center`}
          onSlideChange={handleSlideChange}
          keyboard={{ enabled: true }}
        >
          {slides?.map((slide, index) => (
            <SwiperSlide key={slide.slide_number}>
              <div
                className={`[&_.ck-content]:space-y-7 [&_.ck-content]:flex [&_.ck-content]:items-center [&_.ck-content]:justify-center rounded-md shadow-m w-full flex justify-center items-center  ${
                  isFullscreen
                    ? 'fullscreen-slideshow-container'
                    : isEmbedded
                    ? 'custom-slideshow-container h-[calc(100dvh-80px)] overflow-hidden'
                    : 'custom-slideshow-container h-[550px]'
                }`}
                dangerouslySetInnerHTML={{ __html: slide.content }}
              ></div>
              {captions && (
                <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-full max-w-2xl px-4 bg-background rounded-md">
                  <p className="p-2 bg-background backdrop-blur-sm text-center text-sm">{slide.voice_over}</p>
                </div>
              )}
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
      <div className="relative z-10">
        {audioTracks.length > 0 && (
          <AudioPlayer
            audioTracks={audioTracks}
            setAudioTracks={setAudioTracks}
            slides={slides}
            onTrackChange={handleTrackChange}
            toggleFullscreen={toggleFullscreen}
          />
        )}
      </div>
    </>
  );

  return isEmbedded ? (
    <div
      onMouseEnter={showControls}
      onMouseLeave={hideControlsImmediate}
      onMouseMove={showControls}
      ref={containerRef}
      dir="ltr"
      className={`relative h-full  w-full ${
        isFullscreen ? 'bg-background h-screen w-screen flex flex-col justify-between' : 'flex flex-col'
      } ${isControlsVisible ? '' : 'cursor-none'}`}
    >
      {isOpen && renderContent()}
    </div>
  ) : (
    <Modal
      open={isOpen}
      onOpenChange={onOpenChange}
      width="1500px"
      modalHeader={`${t('slide')} ${activeSlide} : ${slides[activeSlide - 1]?.title}`}
    >
      <div
        onMouseEnter={showControls}
        onMouseLeave={hideControls}
        onMouseMove={showControls}
        ref={containerRef}
        dir="ltr"
        className={`overflow-hidden relative ${
          isFullscreen ? 'bg-background h-screen w-screen flex flex-col justify-between' : ''
        } ${isControlsVisible ? '' : 'cursor-none'}`}
      >
        {isOpen && renderContent()}
      </div>
    </Modal>
  );
};

export default SwiperPresentation;
