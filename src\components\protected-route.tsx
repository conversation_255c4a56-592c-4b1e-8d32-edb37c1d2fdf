import React, { lazy, Suspense } from 'react';
import { useHasPermission } from '@/modules/auth/store';
import { PageLoader } from './loading';

const NotAuthorizedPage = lazy(() => import('@/NotAuthorizedPage'));

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions: string | string[];
  fallbackRoute?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredPermissions, fallbackRoute = '/403' }) => {
  const hasPermission = useHasPermission(requiredPermissions);

  if (!hasPermission) {
    return (
      <Suspense fallback={<PageLoader />}>
        <NotAuthorizedPage />
      </Suspense>
    );
  }

  return <>{children}</>;
};

export { ProtectedRoute };
