import React, { Suspense } from 'react';
import { useHasPermission } from '@/modules/auth/store';
import { PageLoader } from './loading';
import { ChunkErrorBoundary } from './chunk-error-boundary';
import { lazyWithRetry } from '@/utils/lazy-retry';

const NotAuthorizedPage = lazyWithRetry(() => import('@/NotAuthorizedPage'));

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions: string | string[];
  fallbackRoute?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children, requiredPermissions, fallbackRoute = '/403' }) => {
  const hasPermission = useHasPermission(requiredPermissions);

  if (!hasPermission) {
    return (
      <ChunkErrorBoundary>
        <Suspense fallback={<PageLoader />}>
          <NotAuthorizedPage />
        </Suspense>
      </ChunkErrorBoundary>
    );
  }

  return <>{children}</>;
};

export { ProtectedRoute };
