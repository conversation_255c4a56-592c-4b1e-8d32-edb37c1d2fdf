import { Api } from '@/index';
import {
  IAnalyzeOrganizationVisionPayload,
  IAnalyzeOrganizationVisionResponse,
  IContentNeedJob,
  IContentNeed,
} from '../types';

export const findContentNeedsById = async (id: string): Promise<IContentNeed> => {
  const { data } = await Api.get(`/content/${id}`);
  return data.data;
};

export const analyzeOrganizationVision = async (
  payload: IAnalyzeOrganizationVisionPayload
): Promise<IAnalyzeOrganizationVisionResponse> => {
  const formData = new FormData();
  formData.append('file', payload.file);
  formData.append('organization_vision', payload.organization_vision);
  formData.append('url', payload.url as any);

  const { data } = await Api.post(`/content/analyze`, formData);
  return data?.data;
};

export const analyzeGoals = async (id: number): Promise<IContentNeedJob[]> => {
  const { data } = await Api.post(`/content/goals/${id}`);
  return data.data;
};

export const analyzeJobsAndRoles = async (id: number): Promise<IContentNeedJob[]> => {
  const { data } = await Api.post(`/content/jobs-and-roles/${id}`);
  return data.data;
};

export const analyzeJobsAndSkills = async (id: number): Promise<IContentNeedJob[]> => {
  const { data } = await Api.post(`/content/jobs-and-skills/${id}`);
  return data.data;
};

export const deleteContentNeed = async (id: number) => {
  const { data } = await Api.post(`/content/delete/${id}`, { _method: 'delete' });
  return data.data;
};

// Content needs Goals endpoints

export const deleteContentNeedGoal = async (id: number) => {
  const { data } = await Api.delete(`/content/goals/${id}`);
  return data.data;
};

export const editContentNeedGoal = async ({ id, title }: { id: number; title: string }) => {
  const { data } = await Api.put(`/content/goals/${id}`, { title });
  return data.data;
};

export const addContentNeedGoal = async ({ content_id, title }: { content_id: number; title: string }) => {
  const { data } = await Api.post(`/content/goals`, { content_id, title });
  return data.data;
};

// Content needs Jobs endpoints

export const addNewContentNeedJob = async (id: number) => {
  const { data } = await Api.post(`/content/add-new-job/${id}`);
  return data.data;
};

export const deleteContentNeedJob = async (id: number) => {
  const { data } = await Api.delete(`/content/jobs/${id}`);
  return data.data;
};

export const editContentNeedJob = async ({
  id,
  content_id,
  title,
}: {
  id: number;
  content_id: number;
  title: string;
}) => {
  const { data } = await Api.put(`/content/jobs/${id}`, { content_id, title });
  return data.data;
};

// Content needs Roles endpoints
export const addContentNeedRole = async ({ id, job_id }: { id: number; job_id: number }) => {
  const { data } = await Api.post(`/content/add-new-role/${id}`, { job_id });
  return data.data;
};

export const deleteContentNeedRole = async (id: number) => {
  const { data } = await Api.delete(`/content/jobs/roles/${id}`);
  return data.data;
};

export const editContentNeedRole = async ({ id, title }: { id: number; title: string }) => {
  const { data } = await Api.put(`/content/jobs/roles/${id}`, { title });
  return data.data;
};

// content neds skills endpoints

export const deleteContentNeedSkill = async (id: number) => {
  const { data } = await Api.delete(`/content/jobs/skills/${id}`);
  return data.data;
};

export const editContentNeedSkill = async ({
  id,
  title,
  correlation,
  description,
  level_id,
  type,
}: {
  id: number;
  title: string;
  description: string;
  level_id: number;
  correlation: string;
  type: string;
}) => {
  const { data } = await Api.put(`/content/jobs/skills/${id}`, { title, description, level_id, correlation, type });
  return data.data;
};
