import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Icon } from '@/components';
import {
  useChangeNotificationReadStatus,
  useGetInitialNotifications,
  useGetUnreadNotificationsCount,
  useMarkAllNotificationAsRead,
} from '../../notification/apis/queries';
import { t } from 'i18next';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import NotificationCard from './notification-card';
import LanguageLink from '@/components/language-link';
import { useListener } from './listener';
import { useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { toast } from 'sonner';
import { INotification } from '../../notification/types';
import { BellRing } from 'lucide-react';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { handleNotificationRouting } from '@/utils/helpers';
const Notifications = ({ userId }: { userId: string | number }) => {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const { data: notifications, isPending: isGettingNotifications } = useGetInitialNotifications();
  const { mutate: toggleNotificationStatus } = useChangeNotificationReadStatus();
  const { mutate: markAllAsRead, isPending: isMarkingAllNotificationAsRead } = useMarkAllNotificationAsRead();
  const { data: unreadCount } = useGetUnreadNotificationsCount();
  const queryClient = useQueryClient();
  const notificationSound = new Audio('/notification-sound.mp3');
  const navigate = useLanguageNavigate();
console.log(notifications[0]);

  // Listener
  useListener({
    channelName: `private-notification.${userId}`,
    eventName: 'notification_event',
    evenCallback: (notification: INotification) => {
      queryClient.invalidateQueries({ queryKey: ['initialNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotificationCount'] });
      notificationSound.play();
      toast(t('notification'), {
        className: 'gap-3',
        description: notification?.data?.message,
        duration: 5000,
        icon: <BellRing className="size-5" />,
        action: {
          label: <Button variant={'ghost'}>{t('view')}</Button>,
          onClick: () => {
            const fullNotification = notifications?.find((n) => n.id === notification.id);
            if (fullNotification) {
              console.log('Full notification data:', fullNotification);

              navigate(handleNotificationRouting(fullNotification));
              toggleNotificationStatus(fullNotification.id);
            } else {
              console.log('Notification not found in the list:', notification.id);

              // Fallback to default route if notification not found
              navigate('/app/my-content');
              toggleNotificationStatus(notification.id);
            }
          },
        },
      });
    },
  });

  const test = () => {};
  return (
    <Popover open={isNotificationOpen} onOpenChange={setIsNotificationOpen}>
      <PopoverTrigger onClick={test}>
        <div className="relative">
          {unreadCount > 0 && (
            <h2 className="absolute top-0 end-0 -translate-y-1/2 translate-x-1/3 bg-red-500 text-white text-xs w-4 aspect-square flex items-center justify-center rounded-full">
              {unreadCount}
            </h2>
          )}
          <Icon icon="hugeicons:notification-02" width={20} className="cursor-pointer" />
        </div>
      </PopoverTrigger>
      <PopoverContent align="end" className="w-[356px] p-0 overflow-hidden">
        <div className="relative p-4">
          <div className="flex gap-3 justify-between items-center mb-2.5">
            <h2 className="text-xs text-gray-400">{t('notification.title')}</h2>
            {unreadCount > 0 && (
              <Button
                className="text-xs px-1 !py-0 h-6 text-gray-900"
                variant={'ghost'}
                onClick={() => markAllAsRead()}
                loading={isMarkingAllNotificationAsRead}
                disabled={isMarkingAllNotificationAsRead}
              >
                {t('notification.markAllAsRead')}
              </Button>
            )}
          </div>
          <ScrollArea className="max-h-[400px] mb-6 ">
            <div className="space-y-1">
              {notifications?.map((notification) => (
                <NotificationCard
                  key={notification.id}
                  notification={notification}
                  setIsNotificationOpen={setIsNotificationOpen}
                />
              ))}
            </div>
          </ScrollArea>
          <LanguageLink
            to={'/app/notifications'}
            onClick={() => setIsNotificationOpen(false)}
            className="absolute bottom-0 left-0 w-full h-9 bg-gray-100 flex justify-center items-center "
          >
            <span className="text-sm text-primary cursor-pointer"> {t('notification.seeAll')}</span>
          </LanguageLink>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default Notifications;
