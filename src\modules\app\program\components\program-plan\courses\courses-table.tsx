import { useState } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { CourseCustomeRow } from './course-custome-row';

import { Button } from '@/components/ui/button';
import { programPlanAtom } from '../../../store';
import ProgramEditDialog from './course-edit';
import { useDeleteCourse } from '@/modules/app/program/apis/queries';
const CourseTable = ({ list }: any) => {
  //  State
  const { mutate: deleteCourse, isPending: isDeleting, variables } = useDeleteCourse();
  const [data] = useAtom(programPlanAtom);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [learningOutcomeDialogOpen, setLearningOutcomeDialogOpen] = useState(false);
  const [rowBeingEdited, setRowBeingEdited] = useState<{
    course_id: number;
    title: string;
    difficulty_level_id: number;
    learning_hours: number;
    program_id: number;
  }>({
    course_id: 0,
    title: '',
    difficulty_level_id: 0,
    learning_hours: 0,
    program_id: 0,
  });

  //  Hooks
  const { t, i18n } = useTranslation();

  const labelKeyString = i18n.dir() === 'ltr';

  return (
    <>
      <div className="mt-[-1.75rem] flex items-center justify-center ">
        <div className="max-w-[1500px] w-full bg-background  p-6">
          {/* <Table
            slots={{
              actions: (_: any, row: any) => {
                return (
                  <div className="flex">
                    <Button
                      size="icon"
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setRowBeingEdited({
                          program_id: data.id,
                          course_id: row.id,
                          title: row.title,
                          learning_hours: row.learning_hours,
                          difficulty_level_id: row.difficulty_level_id,
                        });
                        setLearningOutcomeDialogOpen(true);
                      }}
                      className="border-primary"
                      variant={'ghost'}
                    >
                      <Icon icon="basil:edit-outline" width={25} className="text-primary cursor-pointer" />
                    </Button>

                    <Button
                      size="icon"
                      loading={isDeleting && variables.course_id === row.id}
                      disabled={isDeleting && variables.course_id === row.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteCourse({ id: data.id, course_id: row.id });
                      }}
                      variant={'ghost'}
                    >
                      <Icon icon="gg:trash" width={25} className="text-red-500 cursor-pointer" />
                    </Button>
                  </div>
                );
              },
              level_id: (_: any, row: any) => {
                return <div>{labelKeyString ? row.difficulty_level?.name_en : row.difficulty_level?.name_ar}</div>;
              },
            }}
            breakpoint={'lg'}
            ready={true}
            loading={false}
            rows={list}
            count={list?.length}
            title={'List of Courses'}
            jumbotron={false}
            searchInupt={false}
            customeRows={<CourseCustomeRow data={data} />}
            selectedRows={selectedRows}
            onSelectedRowsChange={setSelectedRows}
            isMultiSelectTable={false}
            columns={[
              {
                key: 'title',
                label: t('Course Title'),
              },
              {
                key: 'level_id',
                label: t('cousrePlanContentPage.table.level'),
                width: '250px',
              },
              {
                key: 'learning_hours',
                label: t('Learning Hours'),
                width: '250px',
              },
              {
                key: 'actions',
                label: t('topicContentPage.table.actions'),
                width: '100px',
              },
            ]}
          /> */}
          {learningOutcomeDialogOpen && (
            <ProgramEditDialog
              isOpen={learningOutcomeDialogOpen}
              setIsOpen={() => setLearningOutcomeDialogOpen(false)}
              rowBeingEdited={rowBeingEdited}
            />
          )}
        </div>
      </div>
    </>
  );
};

export default CourseTable;
