import { keepPreviousData, useQuery, useQueryClient } from '@tanstack/react-query';
import { useImmer } from 'use-immer';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import queryString from 'query-string';
import { Api, useDebounce, DnaEnums } from '@/index';

interface FilterOption {
  enum?: string;
  api?: string;
  placeholder?: string;
  dynamicEnum?: { label: string; value: any }[];
  type?: 'single' | 'multi';
}

interface Pagination {
  page_num: number;
  page_size: number;
}

interface Options {
  filters?: Record<string, FilterOption>;
  search?: string;
  pagination?: Pagination;
}

interface Filter {
  label: string;
  key: string;
  placeholder: string;
  options: Array<{ label: string; value: any }>;
  selectedValue: any;
  onChange: (value: any) => void;
  reset: () => void;
  type: 'single' | 'multi';
}

interface FetchListReturn<T> {
  loading: boolean;
  ready: boolean;
  list: T[];
  additionalData: any | null;
  count: number;
  refresh: () => void;
  search: {
    value: string;
    update: (newSearch: string) => void;
  };
  pagination: Pagination & {
    update: (updates: Partial<Pagination>) => void;
  };
  filters: Filter[];
  setPagination: React.Dispatch<React.SetStateAction<Pagination>>;
  setSearch: React.Dispatch<React.SetStateAction<string>>;
}

const STALE_TIME = 5 * 60 * 1000;
const GC_TIME = 10 * 60 * 1000;

export const useFetchList = <T>(endpoint: string, queryKey: string, options: Options = {}): FetchListReturn<T> => {
  const queryClient = useQueryClient();
  const [searchParams, setSearchParams] = useSearchParams();
  const didMount = useRef(0);

  const [search, setSearch] = useState<string>(searchParams.get('search') || options.search || '');
  const debouncedSearch = useDebounce(search, 500);
  const [pagination, setPagination] = useImmer<Pagination>({
    page_num: Number(searchParams.get('page_num')) || options.pagination?.page_num || 1,
    page_size: options.pagination?.page_size || 30,
  });

  const initialFilters = useMemo(() => {
    return Object.keys(options.filters || {}).reduce((acc, key) => {
      const filterConfig = options.filters?.[key];
      if (filterConfig?.type === 'multi') {
        let values: string[] = [];

        const arrayValues = searchParams.getAll(key + '[]');
        if (arrayValues.length > 0) {
          values = arrayValues;
        } else {
          const singleValue = searchParams.get(key);
          if (singleValue) {
            values = singleValue.split(',').filter(Boolean);
          }
        }

        acc[key] = values.map((val) => {
          // If the filter values are numbers, convert them
          const numVal = Number(val);
          return !isNaN(numVal) ? numVal : val;
        }) as string[] | number[];
      } else {
        const value = searchParams.get(key);
        if (value !== null) {
          // Convert to number if it's a numeric value
          const numVal = Number(value);
          acc[key] = !isNaN(numVal) ? numVal : value;
        }
      }
      return acc;
    }, {} as Record<string, string | string[] | number | number[]>);
  }, [options.filters, searchParams]);

  const [filters, setFilters] = useImmer<Record<string, string | string[] | number | number[]>>(initialFilters);
  const [filterOptions, setFilterOptions] = useState<Record<string, { label: string; value: any }[]>>({});

  const handleGet = async (endpoint: string) => {
    const response = await Api.get(endpoint);
    return response.status === 200 ? response.data.data || response.data || response : null;
  };

  const query = useMemo(
    () => ({
      ...filters,
      search: search,
      page_num: pagination.page_num,
      page_size: pagination.page_size,
    }),
    [filters, search, pagination]
  );

  const buildUrl = useCallback(
    (baseEndpoint: string, params: any) => {
      // Handle multi-select filters specially to create the tags[]=value format
      const processedParams: any = {};

      Object.entries(params).forEach(([key, value]) => {
        const filterConfig = options.filters?.[key];
        if (filterConfig?.type === 'multi' && Array.isArray(value) && value.length > 0) {
          // For multi-select filters, create multiple entries with key[]
          processedParams[`${key}[]`] = value;
        } else if (value !== undefined && value !== null && value !== '' && !Array.isArray(value)) {
          // For regular filters, use the key as-is (but skip empty arrays)
          processedParams[key] = value;
        }
      });

      const queryStr = queryString.stringify(processedParams, { arrayFormat: 'none' });
      return baseEndpoint.includes('?') ? `${baseEndpoint}&${queryStr}` : `${baseEndpoint}?${queryStr}`;
    },
    [options.filters]
  );

  const finalQuery = useMemo(() => buildUrl(endpoint, query), [endpoint, query, buildUrl]);

  const queryKeyforCache = useMemo(
    () => [queryKey, debouncedSearch, pagination.page_num, pagination.page_size, JSON.stringify(filters), endpoint],
    [queryKey, debouncedSearch, pagination.page_num, pagination.page_size, filters, endpoint]
  );

  const { data, isFetching, refetch } = useQuery({
    queryKey: queryKeyforCache,
    queryFn: () => handleGet(finalQuery),
    placeholderData: keepPreviousData,
    staleTime: STALE_TIME,
    gcTime: GC_TIME,
    retry: 1,
  });

  const syncUrlWithState = useCallback(() => {
    const urlParams: any = {};

    Object.entries(query).forEach(([key, value]) => {
      const filterConfig = options.filters?.[key];
      if (filterConfig?.type === 'multi' && Array.isArray(value) && value.length > 0) {
        // For multi-select filters, add each value as a separate parameter with key[]
        value.forEach((val) => {
          const paramKey = `${key}[]`;
          if (!urlParams[paramKey]) {
            urlParams[paramKey] = [];
          }
          urlParams[paramKey].push(val);
        });
      } else if (value !== undefined && value !== null && value !== '' && !Array.isArray(value)) {
        if (key === 'page_num' || key === 'page_size') {
          urlParams[key] = value.toString();
        } else {
          urlParams[key] = value;
        }
      }
    });

    setSearchParams(urlParams, { replace: true });
  }, [query, setSearchParams]);

  useEffect(() => {
    syncUrlWithState();
  }, [query]);

  const handlePaginationReset = useCallback(() => {
    if (didMount.current >= 2) {
      setPagination((draft) => {
        draft.page_num = 1;
      });
    } else {
      didMount.current += 1;
    }
  }, [debouncedSearch]);

  const prefetchNextPage = useCallback(() => {
    if (data?.total && pagination.page_num * pagination.page_size < data.total) {
      const nextPage = pagination.page_num + 1;
      const nextPageQuery = { ...query, page_num: nextPage };
      const nextPageUrl = buildUrl(endpoint, nextPageQuery);
      const nextPageQueryKey = [
        queryKey,
        debouncedSearch,
        nextPage,
        pagination.page_size,
        JSON.stringify(filters),
        endpoint,
      ];

      queryClient.prefetchQuery({
        queryKey: nextPageQueryKey,
        queryFn: () => handleGet(nextPageUrl),
        staleTime: STALE_TIME,
      });
    }
  }, [data, pagination, query, endpoint, queryKey, debouncedSearch, filters, queryClient, buildUrl]);

  useEffect(() => {
    handlePaginationReset();
  }, [debouncedSearch, handlePaginationReset]);

  useEffect(() => {
    prefetchNextPage();
  }, [data, pagination.page_num]);

  const handleFilterChange = useCallback(
    (key: string, value: any) => {
      setFilters((draft) => {
        const filterConfig = options.filters?.[key];
        if (filterConfig?.type === 'multi') {
          // For multi-select, value should be an array
          draft[key] = Array.isArray(value) ? value : [value];
        } else {
          // For single-select, value is a single item
          draft[key] = value;
        }
      });
      setPagination((draft) => {
        draft.page_num = 1;
      });
    },
    [setFilters, setPagination]
  );

  const resetFilters = useCallback(() => {
    // Reset filters to their initial state based on type
    const resetFiltersState = Object.keys(options.filters || {}).reduce((acc, key) => {
      const filterConfig = options.filters?.[key];
      if (filterConfig?.type === 'multi') {
        acc[key] = [];
      }
      // For single-select, we don't set anything (undefined/null)
      return acc;
    }, {} as Record<string, string | string[]>);

    setFilters(resetFiltersState);
    setSearch('');
    setPagination((draft) => {
      draft.page_num = 1;
    });
  }, [setFilters, setSearch, setPagination]);

  const fetchFilterOptions = useCallback(
    async (filter: FilterOption, key: string) => {
      if (filter.api && !filterOptions[key]) {
        try {
          const response = await Api.get(filter.api);

          // Check if the response has the expected structure
          let items = [];
          if (response.data?.data?.items) {
            items = response.data.data.items;
          } else if (response?.data?.data) {
            items = response?.data?.data;
          } else if (response.data?.items) {
            items = response.data.items;
          }

          // Map the items to the required format
          const options = items.map((item: any) => ({
            label: item.name === 'super_admin' ? item.name : item.name.charAt(0).toUpperCase() + item.name.slice(1),
            value: item.id,
            email: item.email,
            searchValue: `${item.name} ${item.email}`.toLowerCase(),
          }));

          // Update filter options
          setFilterOptions((prev) => ({ ...prev, [key]: options }));
        } catch (error) {
          console.error(`Failed to fetch filter options for ${key}:`, error);
        }
      }
    },
    [filterOptions]
  );

  const generateFilters = useCallback((): Filter[] => {
    if (!options.filters) return [];

    const createFilterOption = (key: string, filter: FilterOption): Filter | null => {
      const baseFilter = {
        label: filter.placeholder || key,
        placeholder: filter.placeholder || key,
        key,
        selectedValue: filters[key],
        onChange: (value: any) => handleFilterChange(key, value),
        reset: resetFilters,
        type: filter.type || 'single', // Default to 'single' if not specified
      };

      if (filter.enum) {
        return {
          ...baseFilter,
          options: (DnaEnums as any)[filter.enum].map((opt: any) => ({
            label: opt.label,
            value: opt.value,
          })),
        };
      }

      if (filter.dynamicEnum) {
        return {
          ...baseFilter,
          options: filter.dynamicEnum,
        };
      }

      if (filter.api) {
        useEffect(() => {
          fetchFilterOptions(filter, key);
        }, [filter.api]);
        return {
          ...baseFilter,
          options: filterOptions[key] || [],
        };
      }

      return null;
    };

    return Object.entries(options.filters)
      .map(([key, filter]) => createFilterOption(key, filter))
      .filter((filter): filter is Filter => !!filter);
  }, [filters, filterOptions, handleFilterChange, resetFilters, fetchFilterOptions, options]);

  return {
    loading: isFetching,
    ready: !!data,
    list: data?.items || data?.data || [],
    additionalData: data?.additional_data || null,
    count: data?.total || 0,
    refresh: refetch,
    search: {
      value: search,
      update: setSearch,
    },
    pagination: {
      ...pagination,
      update: (updates: Partial<Pagination>) => setPagination((draft) => ({ ...draft, ...updates })),
    },
    filters: generateFilters(),
    setPagination,
    setSearch,
  };
};
