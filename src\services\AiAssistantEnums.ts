import { defineEnum } from '../utils/defineEnum';

export const AiAssistantEnums = Object.freeze({
  IntroductionType: defineEnum([
    {
      prompt:
        "using the same language, Write an introduction for the topic: '{topic}'. It should have a hook that attracts the learner's attention, the hook can be a fact, a question, or a short story about the given subject. The intro should be more than 35 words and less than 40 words and all the information should be from reliable.",
      label: 'Normal',
      id: 'normal',
    },
    {
      prompt:
        "using the same language, Write an introduction for the topic: '{topic}', it should have an engaging example in a story or scenarios form that relates the subject to enhance understanding about this subject. The intro should be more than 35 words and less than 40 words.",
      label: 'Story',
      id: 'story',
    },
    {
      prompt:
        "using the same language, Write an introduction for the topic: '{topic}', that provides statistics and numbers about the given subject that attracts the audience's attention, and it should be from reliable sources. The intro should be more than 35 words and less than 40 words.",
      label: 'Facts & numbers',
      id: 'facts',
    },
    {
      prompt:
        "using the same language, Write a real-world problem that aligns with the '{topic}' as an introduction for the content. Present the problem at the beginning of the introduction to spark curiosity. End the introduction with a question related to the problem. The intro should be more than 35 words and less than 40 words and all the information should be from reliable sources.",
      label: 'Problem-oriented',
      id: 'problem',
    },
  ]),
  Enhancements: defineEnum([
    {
      id: 'addExample',
      label: 'Add an example',
      prompt:
        'I have a text that presents various ideas, and I need examples to illustrate these ideas clearly. Please create one example that effectively explain the concepts and enhance understanding. Ensure the example are relevant and easy to comprehend. make the selected text and the example as one paragraph',
    },
    {
      id: 'addMoreDetails',
      label: 'Add more details, Explain',
      prompt:
        'I have a text that presents several ideas, but I need more details to enhance the explanation and clarity of these concepts. Please expand on the ideas, providing additional information and context to make them more comprehensive and understandable.',
    },
    {
      id: 'makeItShorter',
      label: 'Make it shorter',
      prompt:
        'Shorten the following text while preserving its main idea and key points. Ensure that the essential meaning and context are maintained',
    },
  ]),
  Corrections: defineEnum([
    {
      id: 'diacritics',
      label: 'التشكيل (العربية)',
      prompt: 'قم بالتشكيل البسيط ليكون النطق بشكل صحيح',
    },
    {
      id: 'textProofreading',
      label: 'Text proofreading التدقيق اللغوي (العربية)',
      prompt:
        'I have an Arabic text that needs thorough proofreading and linguistic checking. Please review the text for any grammatical, syntactical, or punctuation errors. Additionally, ensure the language is clear, coherent, and stylistically appropriate. Make necessary corrections.',
    },
  ]),
  Translations: defineEnum([
    {
      id: 'translateEnglish',
      label: 'Translate to English',
      prompt: 'Translate to English language.',
    },
    {
      id: 'translateArabic',
      label: 'Translate to Arabic',
      prompt: 'Translate to Arabic language.',
    },
    {
      id: 'translateSpanish',
      label: 'Translate to Spanish',
      prompt: 'Translate to Spanish language.',
    },
    {
      id: 'translateFrench',
      label: 'Translate to French',
      prompt: 'Translate to French language.',
    },
    {
      id: 'translateGerman',
      label: 'Translate to German',
      prompt: 'Translate to German language.',
    },
    {
      id: 'translatePortuguese',
      label: 'Translate to Portuguese',
      prompt: 'Translate to Portuguese language.',
    },
    {
      id: 'translateChinese',
      label: 'Translate to Chinese',
      prompt: 'Translate to Chinese (simplified) language.',
    },
  ]),
});
