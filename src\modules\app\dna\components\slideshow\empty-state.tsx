import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';

const EmptySlideshow = () => {
  const { t } = useTranslation();
  const { dnaId } = useParams();

  return (
    <div className="flex flex-col justify-center items-center ms-2 min-h-96">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.slideshow.noSlideshowTitle')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.noSlideshowDescription')}</p>
      </div>
      {/* <Form onSubmit={handleSubmit}>
        <div className="grid grid-cols-2 gap-4 mb-5 w-80">
          <div className="col-span-2">
            <ComboboxInput
              name="slideshow_sound_type"
              label={t('slideshow.generation.label')}
              placeholder={t('slideshow.generation.placeholder')}
              options={DnaEnums.voice_type}
              value={form.slideshow_sound_type || ''}
              onChange={setFieldValue('slideshow_sound_type')}
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="model"
              placeholder={t('slideshow.model')}
              label={t('slideshow.model')}
              options={DnaEnums.ai_base_model}
              value={form.model}
              onChange={setFieldValue('model')}
              validators={[isRequired()]}
            />
          </div>
        </div>

        <ProtectedComponent requiredPermissions={'dna_slideshow_create'}>
          <Button loading={isPending} className="flex gap-2 px-5 items-center mx-auto">
            {t('generate')}
          </Button>
        </ProtectedComponent>
      </Form> */}
    </div>
  );
};

export default EmptySlideshow;
