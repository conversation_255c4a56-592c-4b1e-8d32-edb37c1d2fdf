import { useAddProgramPlanCourse } from '@/modules/app/program/apis/queries';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';

const CourseCustomeRow = ({ data }: { data: any }) => {
  // hooks
  const { t } = useTranslation();
  const { mutate, isPending } = useAddProgramPlanCourse();
  //refs

  return (
    <tr>
      <td colSpan={3} className="p-4 border-b border-border">
        <div
          className={`text-primary rtl:text-right font-semibold border border-border py-2 px-2 rounded-lg cursor-default`}
        >
          {t('programPlan.steps.learningOutcome.row.placeholder')}
        </div>
      </td>

      <td className="p-4 border-b border-border">
        <Button
          type="button"
          className="border-primary"
          loading={isPending}
          disabled={isPending}
          onClick={() => mutate({ id: data.id, courses: data.courses })}
          variant={'outline'}
          size={'sm'}
        >
          + {t('CoursePlanCreationPage.add')}
        </Button>
      </td>
    </tr>
  );
};

export { CourseCustomeRow };
