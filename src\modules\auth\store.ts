import { atom } from 'jotai';
import { IUser } from './types';
import { useAtomValue } from 'jotai';
// the atom that holdes the user info
const userAtom = atom<IUser | null>(null);
const notVerifiedUserAtom = atom<IUser | null>(null);

const useHasPermission = (requiredPermissions: string | string[], requireAll: boolean = true) => {
  const user = useAtomValue(userAtom);

  if (!user || !user.roles) return false;
  const permissionsArray = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions];

  const userPermissions = new Set<string>();
  user.roles.forEach((role) => {
    role.permissions.forEach((permission) => {
      userPermissions.add(permission.name);
    });
  });

  if (requireAll) {
    return permissionsArray?.every((permission) => userPermissions.has(permission));
  } else {
    return permissionsArray?.some((permission) => userPermissions.has(permission));
  }
};

export { userAtom, useHasPermission, notVerifiedUserAtom };
