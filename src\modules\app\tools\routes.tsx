import { lazy } from 'react';
import { ProtectedRoute } from '@/components';
import { ToolsMainLayout } from './layout';

const ToolsPage = lazy(() => import('./list'));
const TopicCreatePage = lazy(() => import('../topic/pages/create'));
const CoursePlanPage = lazy(() => import('../course/pages/create'));
const DnaCreation = lazy(() => import('../dna/pages/create'));
const CreationDnaTable = lazy(() => import('../dna/pages/creation-dna-table'));
const FileAnalyzer = lazy(() => import('./file-analyzer/page'));
const ContentNeedPage = lazy(() => import('../content-need/pages/create'));
const ProgramPlanPage = lazy(() => import('../program/pages/create'));
const RewriteContent = lazy(() => import('./adaptive-content/page'));
const RewriteDnaContent = lazy(() => import('./adaptive-content/[id]/page'));
const PdfSummarize = lazy(() => import('./pdf-summarize'));
export default [
  {
    path: 'tools',
    element: <ToolsMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.toolsPage.tools',
      };
    },
    children: [
      // Default tools page
      {
        path: '',
        element: <ToolsPage />,
      },
      // Routes
      {
        path: 'dna-creation',
        element: (
          <ProtectedRoute requiredPermissions={'dna_create'}>
            <DnaCreation />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.dnaCreation',
            title: 'breadcrumb.toolsPage.dnaCreation',
          };
        },
        children: [
          {
            path: ':dnaId',
            element: (
              <ProtectedRoute requiredPermissions={'dna_create'}>
                <CreationDnaTable />
              </ProtectedRoute>
            ),
            loader() {
              return {
                label: 'breadcrumb.toolsPage.dnaCreationTable',
                title: 'breadcrumb.toolsPage.dnaCreationTable',
              };
            },
          },
        ],
      },

      {
        path: 'topic-creation',
        element: (
          <ProtectedRoute requiredPermissions={'topic_create'}>
            <TopicCreatePage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.topicCreation',
            title: 'breadcrumb.toolsPage.topicCreation',
          };
        },
      },
      {
        path: 'course-plan',
        element: (
          <ProtectedRoute requiredPermissions={'course_create'}>
            <CoursePlanPage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.coursePlan',
            title: 'breadcrumb.toolsPage.coursePlan',
          };
        },
      },
      {
        path: 'file-analyzer',
        element: (
          <ProtectedRoute requiredPermissions={'file_analyzer'}>
            <FileAnalyzer />
          </ProtectedRoute>
        ),
      },
      {
        path: 'content-need',
        element: (
          <ProtectedRoute requiredPermissions={'content_create'}>
            <ContentNeedPage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.contentNeed',
            title: 'breadcrumb.toolsPage.contentNeed',
          };
        },
      },
      {
        path: 'program-plan',
        element: (
          <ProtectedRoute requiredPermissions={'program_plan_create'}>
            <ProgramPlanPage />
          </ProtectedRoute>
        ),

        loader() {
          return {
            label: 'breadcrumb.toolsPage.programPlan',
            title: 'breadcrumb.toolsPage.programPlan',
          };
        },
      },
      {
        path: 'rewrite-content',
        element: (
          <ProtectedRoute requiredPermissions={'content_dna_reformat'}>
            <RewriteContent />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.rewriteContent',
            title: 'breadcrumb.toolsPage.rewriteContent',
          };
        },
      },
      {
        path: 'rewrite-content/:id',
        element: (
          <ProtectedRoute requiredPermissions={'content_dna_reformat'}>
            <RewriteDnaContent />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.toolsPage.rewriteContent',
            title: 'breadcrumb.toolsPage.rewriteContent',
          };
        },
      },
      {
        path: 'pdf-summarize',
        element: (
          <ProtectedRoute requiredPermissions={'for_administration'}>
            <PdfSummarize />
          </ProtectedRoute>
        ),
      },
    ],
  },
];
