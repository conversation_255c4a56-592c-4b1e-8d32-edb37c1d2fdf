import { defineEnum } from '../utils/defineEnum';

export const TopicEnums = Object.freeze({
  subject: defineEnum([
    {
      value: 'Education',
      label: 'Education',
    },
    {
      value: 'Tourism',
      label: 'Tourism',
    },
    {
      value: 'Energy',
      label: 'Energy',
    },
    {
      value: 'Mining and minerals',
      label: 'Mining and minerals',
    },
    {
      value: 'Industry and manufacturing',
      label: 'Industry and manufacturing',
    },
    {
      value: 'Transportation and logistics services',
      label: 'Transportation and logistics services',
    },
    {
      value: 'Financial services',
      label: 'Financial services',
    },
    {
      value: 'Health care',
      label: 'Health care',
    },
    {
      value: 'Sports',
      label: 'Sports',
    },
    {
      value: 'Law',
      label: 'Law',
    },
    {
      value: 'Entertainment',
      label: 'Entertainment',
    },
    {
      value: 'Art',
      label: 'Art',
    },
    {
      value: 'Design',
      label: 'Design',
    },
    {
      value: 'Real estate',
      label: 'Real estate',
    },
    {
      value: 'Human capital',
      label: 'Human capital',
    },
    {
      value: 'innovation',
      label: 'innovation',
    },
    {
      value: 'Environmental services',
      label: 'Environmental services',
    },
    {
      value: 'Chemicals',
      label: 'Chemicals',
    },
    {
      value: 'Pharmaceuticals and biotechnology',
      label: 'Pharmaceuticals and biotechnology',
    },
    {
      value: 'Agriculture',
      label: 'Agriculture',
    },
    {
      value: 'Food industries',
      label: 'Food industries',
    },
    {
      value: 'Aviation and defence',
      label: 'Aviation and defence',
    },
    {
      value: 'Information and Communication Technology',
      label: 'Information and Communication Technology',
    },
    {
      value: 'Business Management',
      label: 'Business Management',
    },
    {
      value: 'Retail and wholesale trade',
      label: 'Retail and wholesale trade',
    },
    {
      value: 'Other (write a subject)',
      label: 'Other (write a subject)',
    },
  ]),
  audience: defineEnum([
    {
      value: 'Early childhood education',
      label: 'Early childhood education',
    },
    {
      value: 'Primary education',
      label: 'Primary education',
    },
    {
      value: 'Intermediate education',
      label: 'Intermediate education',
    },
    {
      value: 'Secondary education',
      label: 'Secondary education',
    },
    {
      value: 'Undergraduate',
      label: 'Undergraduate',
    },
    {
      value: 'Postgraduate',
      label: 'Postgraduate',
    },
    {
      value: 'Employees',
      label: 'Professionals',
    },
  ]),
  language: defineEnum([
    {
      value: 'Arabic',
      label: 'Arabic',
    },
    {
      value: 'English',
      label: 'English',
    },
  ]),
});
