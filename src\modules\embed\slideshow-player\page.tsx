import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useAtom, useSetAtom } from 'jotai';
import {
  finalSlidesAtom,
  selectedSlideshowAudioLanguageAtom,
  selectedSlideshowIdAtom,
  selectedSlideshowSlidesLanguageAtom,
  selectedSlideshowSubtitleLanguageAtom,
  slideshowPlayDataAtom,
} from '@/modules/app/dna/store';
import SwiperPresentation from '@/modules/app/dna/components/slideshow/presentation';
import { Icon } from '@/components';
import { useGetSharedSlideshow } from '@/modules/app/dna/apis/slideshow/queries';
import '@/modules/app/dna/components/slideshow/styles.css';

const EmbedSlideshow = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const { data: slideshowPlayData, isLoading } = useGetSharedSlideshow(uuid);
  const [_, setSlideshowPlayData] = useAtom(slideshowPlayData<PERSON>tom);
  const setSlidesLang = useSetAtom(selectedSlideshowSlidesLanguageAtom);
  const setSlidesAudio = useSetAtom(selectedSlideshowAudioLanguageAtom);
  const setSlidesSubtitle = useSetAtom(selectedSlideshowSubtitleLanguageAtom);
  const setSelectedSlideshowId = useSetAtom(selectedSlideshowIdAtom);
  const [finalSlides] = useAtom(finalSlidesAtom);

  useEffect(() => {
    if (slideshowPlayData) {
      setSlideshowPlayData(slideshowPlayData);
      const defaultLang = slideshowPlayData?.additional_data?.[0]?.id || '0';
      setSlidesLang(defaultLang);
      setSlidesAudio(defaultLang);
      setSlidesSubtitle(defaultLang);
      setSelectedSlideshowId(uuid || '0');
    }
  }, [slideshowPlayData, uuid]);

  if (isLoading) {
    return (
      <div className="h-screen w-full flex items-center justify-center">
        <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary" width={40} />
      </div>
    );
  }

  return (
    <div className="overflow-hidden">
      {finalSlides && finalSlides.length > 0 ? (
        <SwiperPresentation slides={finalSlides} isOpen={true} onOpenChange={() => {}} isEmbedded={true} />
      ) : (
        <div className="h-full w-full flex items-center justify-center">
          <p className="text-muted-foreground">No slides available</p>
        </div>
      )}
    </div>
  );
};

export default EmbedSlideshow;
