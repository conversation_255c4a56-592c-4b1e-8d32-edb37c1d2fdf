export interface INotification {
  id: string;
  type: string;
  data: {
    message: string;
    data: {
      assigned_to: number;
      content_type: string;
      content_id: number;
      priority: string;
      due_date: string;
      description: string;
      code: string;
      initiated_by: number;
      task_status_id: number;
      status: string;
      updated_at: string;
      created_at: string;
      id: number;
      dna_id: number;
      course_id: number;
      assigned_user: {
        id: number;
        name: string;
        email: string;
        email_verified_at: string;
        info: string;
        phone: string;
        birth_date: string;
        sex: string;
        nationality_id: number;
        nationally_number: string;
        profession: string;
        education_id: number;
        university_id: number;
        personal_skills: null;
        account_status: string;
        created_at: string;
        updated_at: string;
      };
      created_by: {
        id: number;
        name: string;
        email: string;
        avatar: string;
      };
    };
  };
  read_at: null | string;
  created_at: string;
}

export interface NotificationURL {
  value: string; 
  url: (type: INotification) => string;
}