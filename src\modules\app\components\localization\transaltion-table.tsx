import { useMemo, useState } from 'react';
import { Icon, ProtectedComponent } from '@/index';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import EditTranslation from './edit-translation';
import { useDeleteTransaltion } from '@/modules/app/dna/apis/queries';
import { useParams } from 'react-router-dom';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';
import { ITableColumn } from '@/components/theTable/types';
import { handelExportToWord, formatUserByNameAndEmail, getTransalationStatus } from '@/utils/helpers';

import { useConfirmation } from '@/components/confirmation-popup/hooks';

const TranslationTable = ({ details, viewOnly = false }: any) => {
  const [editDialog, setEditDialog] = useState<boolean>(false);
  const [danData, setDanData] = useState<any>(null);

  const { taskId } = useParams();
  const { mutate, variables, isPending } = useDeleteTransaltion();
  const { confirm } = useConfirmation();

  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const countMin = (count: number) => {
    if (count >= 630) {
      return '7 min';
    } else if (count >= 540) {
      return '6 min';
    } else if (count >= 450) {
      return '5 min';
    } else if (count >= 360) {
      return '4 min';
    } else if (count >= 270) {
      return '3 min';
    } else if (count >= 180) {
      return '2 min';
    } else if (count >= 90) {
      return '1 min';
    } else {
      return 'Less than 1 min';
    }
  };

  const handleDeleteTranslation = ({ id }: { id: number }) => {
    confirm({
      variant: 'info',
      title: t('translation.delete.confirmation.title'),
      description: t('translation.delete.confirmation.description'),
      onConfirm: () => mutate({ id: details.id, code: taskId as string, translation_ids: [id] }),
    });
  };

  const columns: ITableColumn<any>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '220px',
        cell: ({ row }) => {
          return (
            <div
              className={`px-4 text-center py-1.5 rounded-md whitespace-nowrap w-full ${getTransalationStatus(
                row?.status
              )}`}
            >
              {row?.status}
            </div>
          );
        },
      },
      {
        accessorKey: 'title',
        header: t('dnaConentPgae.table.dnaTitle'),
        width: '250px',
      },
      {
        accessorKey: 'lang',
        header: t('dnaConentPgae.table.topic'),
        width: '100px',
        cell: ({ row }) => {
          return (
            <div className="flex gap-2 items-center">
              <p className="font-medium">{labelKey ? row.language.name_en : row.language.name_ar}</p>
            </div>
          );
        },
      },
      {
        accessorKey: 'count',
        header: t('dnaConentPgae.table.length'),
        width: '200px',
        cell: ({ row }) => {
          return (
            <p className="rtl:text-right">
              {countMin(row.words_count)} ({row.words_count} Words)
            </p>
          );
        },
      },

      {
        accessorKey: 'creator.email',
        header: t('dnaConentPgae.table.author'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.creator);
        },
      },
      {
        accessorKey: 'editor.email',
        header: t('dnaConentPgae.table.editor'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.editor);
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '150px',
        cell: ({ row }) => {
          return (
            <>
              <div className="flex">
                <Button
                  size="icon"
                  type="button"
                  variant={'ghost'}
                  onClick={() => {
                    setEditDialog(true), setDanData(row);
                  }}
                >
                  <Icon icon="basil:edit-outline" width={25} />
                </Button>
                <ProtectedComponent requiredPermissions={'dna_download'}>
                  <Button variant={'ghost'} onClick={() => handelExportToWord(row.content, row.title, i18n.language)}>
                    <Icon icon="material-symbols:download-rounded" width={25} />
                  </Button>
                </ProtectedComponent>
                {!viewOnly && (
                  <ProtectedTaskComponent requiredPermissions="translation_delete">
                    <Button
                      size="icon"
                      onClick={() => {
                        handleDeleteTranslation({ id: row.id });
                      }}
                      loading={isPending && row.id === variables.translation_ids[0]}
                      disabled={isPending && row.id === variables.translation_ids[0]}
                      variant={'ghost'}
                    >
                      <Icon icon="gg:trash" width="25" className="text-red-500 cursor-pointer" />
                    </Button>
                  </ProtectedTaskComponent>
                )}
              </div>
            </>
          );
        },
      },
    ];
  }, [variables]);

  return (
    <div>
      <Table rows={details?.content_translations} columns={columns}>
        <TableContent>
          <TableContentBody />
        </TableContent>
      </Table>

      {editDialog && (
        <EditTranslation
          viewOnly={viewOnly}
          onOpen={editDialog}
          details={details}
          onOpenChange={() => {
            setEditDialog(false), setDanData(null);
          }}
          data={danData}
        />
      )}
    </div>
  );
};

export default TranslationTable;
