import React, { memo } from 'react';
import { cn } from '@/lib/utils';
import { Input } from '../ui/input';
import { useTranslation } from 'react-i18next';

interface TableSearchProps {
  className?: string;
  placeholder: string;
  search: {
    value: string;
    update: (value: string) => void;
  };
}

const TableSearch: React.FC<TableSearchProps> = ({ className, placeholder = 'search', search }) => {
  const { t } = useTranslation();
  return (
    <Input
      type="text"
      placeholder={t(placeholder)}
      className={cn(className, 'shadow')}
      value={search?.value}
      onInput={(e) => search.update(e.currentTarget.value)}
    />
  );
};

export default memo(TableSearch);
