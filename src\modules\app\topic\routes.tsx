import { lazy } from 'react';

const TopicList = lazy(() => import('./pages/list'));
const SingleTopicPage = lazy(() => import('./pages/single'));
export default [
  {
    path: 'topics',
    element: <TopicList />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.topic',
        title: 'breadcrumb.myContentPage.topic',
      };
    },
  },
  {
    path: 'topics/:id',
    element: <SingleTopicPage />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.topic',
        title: 'breadcrumb.myContentPage.topic',
      };
    },
  },
];
