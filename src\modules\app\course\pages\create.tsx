import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Icon } from '@/index';
import { Button } from '@/components/ui/button';
import { CoursePlanTable } from '../components/course-plan-table';
import {
  useCreateCoursePlanAi,
  useCreateCoursePlanFile,
  useGetCourseById,
  useMarkCourseAsReadyForReview,
} from '@/modules/app/course/apis/queries';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import CoursePlanFileForm from '../components/course-plan-file-form';
import CoursePlanAiForm from '../components/course-plan-form';
import { useAddChecklist } from '@/apis/tool-checklist/queries';
import AddChecklistDialog from '@/modules/app/components/add-checklist-dialog';
import ToolCreation from '@/components/creation-ui/creation';

const CoursePlanPage = () => {
  // State
  const [open, setOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  // Hooks
  const { t, i18n } = useTranslation();
  const [searchParams, setSearchParams] = useSearchParams();
  const { mutate: createCoursePlan, isPending } = useCreateCoursePlanAi();
  const { mutate: createCoursePlanFile, isPending: pending, error } = useCreateCoursePlanFile();
  const { data: courseData } = useGetCourseById(searchParams.get('courseId') || '');
  const [counterToResetForm, setCounterToResetForm] = useState(0);
  const { mutate: markCourseAsReadyForReview, isPending: isMarkingPending } = useMarkCourseAsReadyForReview();
  const { mutate: addChecklist, isPending: isPendingChecklist } = useAddChecklist();

  const handleMarkAsReadyForReview = (checklist: (string | number)[]) => {
    if (courseData) {
      markCourseAsReadyForReview(courseData.id, {
        onSuccess: () => {
          addChecklist(
            { check_list_ids: checklist, tool_id: courseData.id, tool_type: 'Course' },
            {
              onSuccess: () => {
                setOpen(false);
              },
            }
          );
        },
      });
    }
  };

  const activeTab = searchParams.get('tab') || 'ai-form';

  const setTabInUrl = (tab: any) => {
    setSearchParams(
      (params) => {
        params.set('tab', tab);
        return params;
      },
      { replace: true }
    );
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  useEffect(() => {
    if (courseData) {
      setIsCollapsed(true);
    }
  }, [courseData]);

  const courseTable = () => {
    if (courseData) {
      return (
        <div>
          <CoursePlanTable />
        </div>
      );
    } else {
      return (
        <div className="flex flex-col justify-center items-center ms-2 min-h-full border relative rounded-lg bg-white dark:bg-background ">
          <img src="/empty-activity.svg" className="" />
          <img
            src="/arrow.png"
            className={`absolute top-[20%] ${i18n.dir() === 'ltr' ? 'left-[25%] rotate-[223deg]' : 'right-[24%]'} `}
          />{' '}
          <div className="flex flex-col gap-2 my-5 ml-6">
            <h1 className="self-center  text-2xl">{t('empty.courseTitle')}</h1>
            <p className="self-center text-slate-500">{t('empty.courseDescription')}</p>
          </div>
        </div>
      );
    }
  };

  return (
    <div className="grid relative">
      <div className={`flex  transition-all duration-500 ease-in-out h-[calc(100dvh-152px)]`}>
        <div
          className={`
          transition-all duration-500 ease-in-out
          ${isCollapsed ? 'w-0 opacity-0' : 'w-[27%] opacity-100 '}
        `}
        >
          <Tabs className="rtl:text-right w-full h-full" onValueChange={setTabInUrl} defaultValue={activeTab}>
            <ToolCreation>
              <ToolCreation.Header>
                <TabsList className="flex justify-around bg-transparent p-1 ">
                  <TabsTrigger
                    className="min-w-[120px] data-[state=active]:bg-semi-primary data-[state=active]:text-primary dark:text-white p-2"
                    disabled={courseData ? true : false}
                    value="ai-form"
                  >
                    {t('topic.aiGeneration')}
                  </TabsTrigger>
                  <TabsTrigger
                    className="min-w-[120px] data-[state=active]:bg-semi-primary data-[state=active]:text-primary dark:text-white p-2"
                    disabled={courseData ? true : false}
                    value="file-form"
                  >
                    {t('topic.file')}
                  </TabsTrigger>
                </TabsList>
              </ToolCreation.Header>
              <ToolCreation.Content>
                <TabsContent value="ai-form">
                  <CoursePlanAiForm
                    courseData={courseData}
                    error={error}
                    counterToResetForm={counterToResetForm}
                    onSubmit={createCoursePlan}
                    id="ai-form"
                  />
                </TabsContent>
                <TabsContent value="file-form">
                  <CoursePlanFileForm
                    error={error}
                    counterToResetForm={counterToResetForm}
                    courseData={courseData}
                    onSubmit={createCoursePlanFile}
                    id="file-form"
                  />
                </TabsContent>
              </ToolCreation.Content>
              <ToolCreation.Buttons>
                <div className="flex justify-between gap-2 w-full ">
                  <Button
                    type="submit"
                    className="w-full"
                    form={activeTab}
                    loading={isPending || pending}
                    disabled={isPending || pending}
                  >
                    {courseData ? t('CoursePlanCreationPage.reGenerate') : t('generate')}
                  </Button>
                  <Button
                    type="reset"
                    onClick={() => setCounterToResetForm(counterToResetForm + 1)}
                    form={activeTab}
                    variant={'outline'}
                    className="min-w-[100px]"
                  >
                    {t('clear')}
                  </Button>
                </div>
              </ToolCreation.Buttons>
            </ToolCreation>
          </Tabs>
        </div>

        <div
          className={`bg-transparent self-center cursor-pointer w-[10px] ${isCollapsed ? 'mx-2' : 'mx-2'} `}
          onClick={toggleCollapse}
        >
          <Icon
            className={`${i18n.language === 'ar' ? 'rotate-180' : ''}`}
            icon={`line-md:chevron-small-${isCollapsed ? 'right' : 'left'}`}
            width="25"
          />
        </div>
        <div
          className={`transition-all duration-500 ease-in-out overflow-x-auto grid  w-full
            `}
        >
          <div className="overflow-x-auto">{courseTable()}</div>
        </div>
      </div>
      {open && (
        <AddChecklistDialog
          open={open}
          setOpen={setOpen}
          callback={handleMarkAsReadyForReview}
          loading={isMarkingPending || isPendingChecklist}
          type="ownerCourse"
        />
      )}
    </div>
  );
};

export default CoursePlanPage;
