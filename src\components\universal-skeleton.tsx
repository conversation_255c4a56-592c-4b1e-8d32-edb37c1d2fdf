import { ScrollArea } from '@/components/ui/scroll-area';
import { memo } from 'react';
import { cn } from '@/lib/utils';

// Types
export type SkeletonTemplate = 'table' | 'cards' | 'information' | 'image' | 'flexible';
export type SkeletonPreset =
  | 'dataTable'
  | 'fileManager'
  | 'dashboard'
  | 'articleList'
  | 'userList'
  | 'productGrid'
  | 'settingsPage';

interface UniversalSkeletonProps {
  /** The template type to render (optional when using preset) */
  template?: SkeletonTemplate;
  /** Use a predefined preset configuration */
  preset?: SkeletonPreset;
  /** Additional CSS classes */
  className?: string;
  /** Number of rows/items to render (default: 6) */
  rows?: number;
  /** Number of columns for table/grid layouts (default: 4) */
  columns?: number;
  /** Whether to wrap content in ScrollArea (default: true) */
  showScrollArea?: boolean;
  /** Height of the scroll area (default: '500px') */
  height?: string;
  /** Whether to show animation (default: true) */
  animate?: boolean;
}

// Preset configurations
const SKELETON_PRESETS = {
  dataTable: { template: 'table' as const, rows: 10, columns: 5, height: '600px', showScrollArea: true },
  fileManager: { template: 'image' as const, rows: 12, columns: 4, height: '500px', showScrollArea: true },
  dashboard: { template: 'cards' as const, rows: 8, columns: 4, height: '700px', showScrollArea: true },
  articleList: { template: 'information' as const, rows: 6, columns: 4, height: '500px', showScrollArea: true },
  userList: { template: 'flexible' as const, rows: 8, columns: 1, height: '600px', showScrollArea: true },
  productGrid: { template: 'cards' as const, rows: 9, columns: 4, height: '600px', showScrollArea: true },
  settingsPage: { template: 'information' as const, rows: 5, columns: 4, height: '400px', showScrollArea: false },
} as const;

const UniversalSkeleton = ({
  template,
  className,
  rows = 6,
  columns = 4,
  showScrollArea = true,
  height = '500px',
  animate = true,
  preset,
}: UniversalSkeletonProps) => {
  // Apply preset configuration if provided
  const config = preset ? SKELETON_PRESETS[preset] : null;
  const finalProps = {
    template: template || config?.template || 'cards',
    rows: rows || config?.rows || 6,
    columns: columns || config?.columns || 4,
    showScrollArea: showScrollArea ?? config?.showScrollArea ?? true,
    height: height || config?.height || '500px',
    animate,
  };

  const renderSkeleton = () => {
    switch (finalProps.template) {
      case 'table':
        return <TableSkeleton rows={finalProps.rows} columns={finalProps.columns} />;
      case 'cards':
        return <CardsSkeleton rows={finalProps.rows} />;
      case 'information':
        return <InformationSkeleton rows={finalProps.rows} />;
      case 'image':
        return <ImageSkeleton rows={finalProps.rows} />;
      case 'flexible':
        return <FlexibleSkeleton rows={finalProps.rows} columns={finalProps.columns} />;
      default:
        return <CardsSkeleton rows={finalProps.rows} />;
    }
  };

  const content = (
    <div className={cn('relative overflow-hidden', className)}>
      {/* Animated gradient overlay - EXACT same as SkeletonGrid */}
      {finalProps.animate && (
        <div className="absolute inset-0 z-10 pointer-events-none">
          <div className="animate-shine h-full w-full bg-[linear-gradient(110deg,transparent_40%,rgba(255,255,255,0.25)_50%,transparent_60%)] bg-[length:200%_100%]" />
        </div>
      )}
      {renderSkeleton()}
    </div>
  );

  if (finalProps.showScrollArea) {
    return (
      <ScrollArea className={cn('pe-3')} style={{ height: finalProps.height }}>
        {content}
      </ScrollArea>
    );
  }

  return content;
};

// Table Skeleton Template
const TableSkeleton = ({ rows, columns }: { rows: number; columns: number }) => (
  <div className="space-y-3">
    {/* Table Header */}
    <div className="flex items-center gap-3 py-4">
      <div className="h-6 w-48 rounded bg-gray-200/80 dark:bg-gray-700/80" />
      <div className="h-6 w-48 rounded bg-gray-200/80 dark:bg-gray-700/80" />
      <div className="h-6 w-12 rounded bg-gray-200/50 dark:bg-gray-700/50" />
      <div className="h-6 w-24 rounded bg-gray-200/50 dark:bg-gray-700/50 ms-auto" />
    </div>
    <div
      className="grid gap-4 p-4 border rounded-lg bg-gray-50/50 dark:bg-gray-800/50"
      style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
    >
      {[...Array(columns)].map((_, i) => (
        <div key={i} className="h-4 rounded bg-gray-200/80 dark:bg-gray-700/80" />
      ))}
    </div>

    {/* Table Rows */}
    {[...Array(rows)].map((_, rowIndex) => (
      <div
        key={rowIndex}
        className="grid gap-4 p-4 border rounded-lg dark:border-gray-700"
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {[...Array(columns)].map((_, colIndex) => (
          <div key={colIndex} className="h-4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        ))}
      </div>
    ))}
  </div>
);

// Cards Skeleton Template
const CardsSkeleton = ({ rows }: { rows: number }) => (
  <div className="grid grid-cols-[repeat(auto-fill,minmax(280px,1fr))] gap-4">
    {[...Array(rows)].map((_, i) => (
      <div key={i} className="group relative space-y-4 p-6 border rounded-lg overflow-hidden dark:border-gray-700">
        {/* Card Header */}
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-full bg-gray-200/80 dark:bg-gray-700/80" />
          <div className="space-y-2 flex-1">
            <div className="h-4 rounded bg-gray-200/80 dark:bg-gray-700/80" />
            <div className="h-3 w-2/3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          </div>
        </div>

        {/* Card Content */}
        <div className="space-y-3">
          <div className="h-4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-4 w-4/5 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-4 w-3/5 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>

        {/* Card Footer */}
        <div className="flex justify-between items-center pt-2">
          <div className="h-8 w-20 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-8 w-16 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>
      </div>
    ))}
  </div>
);

// Information/Text Skeleton Template
const InformationSkeleton = ({ rows }: { rows: number }) => (
  <div className="space-y-6">
    {[...Array(rows)].map((_, i) => (
      <div key={i} className="space-y-4 p-6 border rounded-lg dark:border-gray-700">
        {/* Title */}
        <div className="h-6 w-1/3 rounded bg-gray-200/80 dark:bg-gray-700/80" />

        {/* Paragraph lines */}
        <div className="space-y-3">
          <div className="h-4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-4 w-11/12 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-4 w-4/5 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>

        {/* Meta info */}
        <div className="flex space-x-4 pt-2">
          <div className="h-3 w-16 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-3 w-20 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-3 w-12 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>
      </div>
    ))}
  </div>
);

// Image Skeleton Template
const ImageSkeleton = ({ rows }: { rows: number }) => (
  <div className="grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-4">
    {[...Array(rows)].map((_, i) => (
      <div key={i} className="group relative space-y-3 overflow-hidden">
        {/* Image skeleton */}
        <div className="aspect-[4/3] w-full rounded-lg bg-gray-200/80 dark:bg-gray-700/80 relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-300/30 to-gray-100/30 dark:from-gray-600/30 dark:to-gray-800/30" />
          {/* Play button or overlay */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="h-12 w-12 rounded-full bg-gray-300/50 dark:bg-gray-600/50" />
          </div>
        </div>

        {/* Caption */}
        <div className="space-y-2">
          <div className="h-4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-3 w-3/4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>

        {/* Action buttons */}
        <div className="flex justify-between items-center">
          <div className="h-6 w-16 rounded bg-gray-200/50 dark:bg-gray-700/50" />
          <div className="h-6 w-6 rounded bg-gray-200/50 dark:bg-gray-700/50" />
        </div>
      </div>
    ))}
  </div>
);

// Flexible Skeleton Template - Can be customized for various layouts
const FlexibleSkeleton = ({ rows, columns }: { rows: number; columns: number }) => (
  <div className="space-y-6">
    {[...Array(rows)].map((_, rowIndex) => (
      <div key={rowIndex} className="space-y-4">
        {/* Dynamic layout based on row index for variety */}
        {rowIndex % 3 === 0 ? (
          // Layout 1: Full width with sidebar
          <div className="flex gap-6">
            <div className="flex-1 space-y-3">
              <div className="h-6 w-1/2 rounded bg-gray-200/80 dark:bg-gray-700/80" />
              <div className="h-4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
              <div className="h-4 w-4/5 rounded bg-gray-200/50 dark:bg-gray-700/50" />
              <div className="h-4 w-3/5 rounded bg-gray-200/50 dark:bg-gray-700/50" />
            </div>
            <div className="w-48 space-y-3">
              <div className="aspect-square w-full rounded bg-gray-200/80 dark:bg-gray-700/80" />
              <div className="h-3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
              <div className="h-3 w-3/4 rounded bg-gray-200/50 dark:bg-gray-700/50" />
            </div>
          </div>
        ) : rowIndex % 3 === 1 ? (
          // Layout 2: Grid layout
          <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${Math.min(columns, 3)}, 1fr)` }}>
            {[...Array(Math.min(columns, 3))].map((_, colIndex) => (
              <div key={colIndex} className="space-y-3 p-4 border rounded-lg dark:border-gray-700">
                <div className="h-5 w-3/4 rounded bg-gray-200/80 dark:bg-gray-700/80" />
                <div className="h-3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                <div className="h-3 w-2/3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                <div className="flex justify-between items-center mt-4">
                  <div className="h-6 w-16 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                  <div className="h-6 w-6 rounded-full bg-gray-200/50 dark:bg-gray-700/50" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Layout 3: List with thumbnails
          <div className="space-y-3">
            {[...Array(3)].map((_, itemIndex) => (
              <div key={itemIndex} className="flex items-center gap-4 p-4 border rounded-lg dark:border-gray-700">
                <div className="h-16 w-16 rounded bg-gray-200/80 dark:bg-gray-700/80 flex-shrink-0" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-1/3 rounded bg-gray-200/80 dark:bg-gray-700/80" />
                  <div className="h-3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                  <div className="h-3 w-2/3 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                </div>
                <div className="flex flex-col gap-2">
                  <div className="h-6 w-20 rounded bg-gray-200/50 dark:bg-gray-700/50" />
                  <div className="h-6 w-6 rounded bg-gray-200/50 dark:bg-gray-700/50 ml-auto" />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    ))}
  </div>
);

export default memo(UniversalSkeleton);

// Export types for external use
export type { UniversalSkeletonProps };
