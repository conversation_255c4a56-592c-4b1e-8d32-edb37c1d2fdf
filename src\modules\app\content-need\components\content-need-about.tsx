import { useEffect, useState } from 'react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

import { Form, useForm, useValidate, Textarea, TextInput } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useAnalyzeOrganizationVision } from '@/modules/app/content-need/apis/queries';
import { useAtom } from 'jotai';
import { contentNeedsAtom, currentStepAtom } from '../../tools/store/contnet-need';
import { useSearchParams } from 'react-router-dom';

const AboutStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  const [currentStep] = useAtom<number>(currentStepAtom);
  const { isRequired } = useValidate();
  const [data] = useAtom(contentNeedsAtom);
  const { t } = useTranslation();
  const { mutate, isPending } = useAnalyzeOrganizationVision();
  const [toggle, setToggle] = useState<any>('URL');
  const [searchParams, setSearchParams] = useSearchParams();

  const { form, setFieldValue, setFormValue } = useForm({
    organization_vision: data.organization_vision || '',
    url: data.url || '',
    file: data.file || '',
  });

  const handleFileUpload = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('dna', file);
      setFieldValue('file')(file);
    }
  };

  useEffect(() => {
    if (data) {
      setFormValue({
        organization_vision: data.organization_vision || '',
        url: data.url || '',
        file: data.file || '',
      });
    }
  }, [data]);

  const handleSubmit = () => {
    if (currentStep < data.step) {
      moveToNextStep();
      return;
    }
    mutate(form, {
      onSuccess: (data) => {
        moveToNextStep();
        setSearchParams({ contentId: JSON.stringify(data.id) }, { replace: true });
      },
    });
  };

  const handleToggleChange = (newValue: any) => {
    setToggle(newValue);
  };

  return (
    <div className="flex justify-center items-center mt-[80px]">
      <div className="flex flex-col gap-20">
        <p className="text-center w-fit sm:w-[450px]">{t('ontentNeed.steps.about.title')}</p>
        <Form className="space-y-4" onSubmit={handleSubmit}>
          <Textarea
            name="organization_vision"
            label={t('contentNeed.steps.about.text.label')}
            placeholder={t('contentNeed.steps.about.text.placeholder')}
            rows={6}
            value={form.organization_vision}
            onChange={setFieldValue('organization_vision')}
            isRequired
            validators={[isRequired()]}
          />
          <div className="w-fit">
            <p className="text-sm font-medium">{t('dnaCreationPage.form.sources')}</p>

            <ToggleGroup
              className="pt-4"
              variant="outline"
              value={toggle || 'Url'}
              type="single"
              onValueChange={handleToggleChange}
            >
              <ToggleGroupItem value="URL" aria-label="Toggle URL">
                <p>{t('URL')}</p>
              </ToggleGroupItem>
              <ToggleGroupItem value="File" aria-label="Toggle File">
                <p>{t('File')}</p>
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          <div className=" space-y-1">
            {toggle === 'URL' && (
              <div className="">
                <TextInput
                  name="context"
                  label={t('contentNeed.steps.about.toggle.url.lable')}
                  placeholder={t('contentNeed.steps.about.toggle.url.placeholder')}
                  value={form.url}
                  onChange={(value: string) => {
                    setFieldValue('url')(value);
                  }}
                />
              </div>
            )}
            {toggle === 'File' && (
              <div className="self-center">
                <p>UNDER DEVELOPMENT</p>
              </div>
            )}
          </div>

          <div className="flex justify-center pt-8">
            <Button disabled={isPending} loading={isPending} type="submit">
              {t('contentNeed.steps.about.button')}
            </Button>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default AboutStep;
