import { ComboboxInput, Modal, ProtectedComponent, Form } from '@/components';
import { DnaEnums } from '@/services';
import { useTranslation } from 'react-i18next';
import { useGenerateArticulateAnalysis } from '../../apis/queries';
import { useForm, useValidate } from '@/hooks';
import { Button } from '@/components/ui/button';
import { useEffect, useMemo } from 'react';
import { generateEnum } from '@/utils/helpers';
import { IDNA } from '../../types';

const GenerationDialog = ({ open, onOpenChange, dna }: { open: boolean; onOpenChange: any; dna: IDNA }) => {
  const { t, i18n } = useTranslation();
  const { mutate, isPending } = useGenerateArticulateAnalysis();
  const currentLanguageLabel = i18n.language === 'en' ? 'name_en' : 'name_ar';

  //hooks
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    id: dna?.id,
    model: 'gpt-4.1',
    language_id: dna?.language.id,
  });
  useEffect(() => {
    if (dna?.id) {
      setFieldValue('id')(dna?.id);
    }
    if (dna?.language?.id) {
      setFieldValue('language_id')(dna?.language?.id);
    }
  }, [dna.id, open]);
  const handleSubmit = () => {
    mutate(form, {
      onSuccess: () => {
        onOpenChange(false);
      },
    });
  };

  const allLanguages = useMemo(() => {
    if (!dna) return [];
    let languages = [];
    languages.push(dna.language);
    for (let i = 0; i < dna.content_translations.length; i++) {
      languages.push(dna.content_translations[i].language);
    }
    return languages;
  }, [dna]);
  return (
    <Modal open={open} onOpenChange={onOpenChange} width={400} modalHeader={t('analysis.regenerateDialogTitle')}>
      <Form onSubmit={handleSubmit}>
        <div className="mb-5 ">
          <ComboboxInput
            name="model"
            placeholder={t('analysis.model')}
            label={t('analysis.model')}
            options={DnaEnums.ai_base_model}
            value={form.model}
            onChange={setFieldValue('model')}
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="language"
            placeholder={t('analysis.language')}
            label={t('analysis.language')}
            options={generateEnum(allLanguages, 'id', currentLanguageLabel)}
            value={form.language_id}
            onChange={setFieldValue('language_id')}
            validators={[isRequired()]}
          />
        </div>

        <ProtectedComponent requiredPermissions={'dna_analysis_create'}>
          <Button loading={isPending} className="flex gap-2 px-5 items-center">
            {t('analysis.reGenerate')}
          </Button>
        </ProtectedComponent>
      </Form>
    </Modal>
  );
};

export default GenerationDialog;
