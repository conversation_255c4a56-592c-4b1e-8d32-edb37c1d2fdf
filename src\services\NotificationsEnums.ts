import { defineEnum } from '../utils/defineEnum';
import { INotification } from '@/modules/app/notification/types';

export const NotificationsEnums = Object.freeze({
  urls: defineEnum([
    {
      value: 'new_task_assigned',
      url: (notification: INotification) => `/app/my-tasks?search=${notification.data?.data?.code}&page_num=1&page_size=10`,
    },
    {
      value: 'dna_feedback_in_edit_phase',
      url: (notification: INotification) => `/app/my-content/courses/${notification.data?.data?.course_id}/${notification.data?.data?.dna_id}`,
    },
  ]),
});
