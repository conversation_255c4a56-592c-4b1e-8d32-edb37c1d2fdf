import { ICourse, ICreateCourseAI, ICreateCourseFile } from '../types';
import { Api } from '@/services';
import { COURSES } from '@/services';

export const getCourseById = async (id: string): Promise<ICourse> => {
  const { data } = await Api.get(`${COURSES}/${id}`);
  return data.data;
};

export const getTaskByCourseId = async (id: string): Promise<ICourse> => {
  const { data } = await Api.get(`${COURSES}/${id}/tasks`);
  return data.data;
};

export const getParentsTaskCodes = async (id: string): Promise<any> => {
  const { data } = await Api.get(`/course/${id}/return-the-parents-task-code`);
  return data.data;
};

export const createCourseAi = async (payload: { info: ICreateCourseAI } | any): Promise<ICourse> => {
  const { data } = await Api.post(`${COURSES}`, payload);
  return data.data;
};

export const approveCoursesInReview = async (id: number) => {
  const { data } = await Api.post(`/courses/${id}/approve-review`, { _method: 'put' });
  return data.data;
};
export const approveCoursesInEdit = async (id: number) => {
  const { data } = await Api.post(`/courses/${id}/approve`, { _method: 'put' });
  return data.data;
};

export const createCourseFile = async (payload: { info: ICreateCourseFile } | any): Promise<ICourse> => {
  const formData = new FormData();
  formData.append('file', payload.file);
  formData.append('info', payload.info);
  formData.append('subject_id', payload.subject_id);
  formData.append('audience_id', payload.audience_id);
  formData.append('language_id', payload.language_id);
  formData.append('difficulty_level_id', payload.difficulty_level_id);
  formData.append('credit_hours_id', payload.credit_hours_id);
  formData.append('model', payload.model);

  const { data } = await Api.post(`/courses/course-import`, formData);
  return data.data;
};

export const addNewTopic = async (id: string, payload: { new_topic_info: string }): Promise<ICourse> => {
  const { data } = await Api.put(`${COURSES}/${id}/add-topic`, { ...payload, _method: 'put' });
  return data.data;
};

export const editCourseTitle = async ({ id, title }: any) => {
  const { data } = await Api.put(`${COURSES}/${id}`, { title, _method: 'put' });
  return data.data;
};

export const deleteCourse = async (id: string | undefined) => {
  const { data } = await Api.delete(`${COURSES}/${id}`);
  return data;
};

export const generateTopicDna = async (id: number) => {
  const { data } = await Api.post(`${COURSES}/${id}/generate-dnas`, { _method: 'put' });
  return data.data;
};

export const deleteCourses = async (ids: number[]) => {
  const { data } = await Api.post(`/courses/courses/delete`, {
    _method: 'delete',
    ids,
  });
  return data.data;
};

export const markCourseAsReadyForReview = async (id: number | string) => {
  const { data } = await Api.post(`${COURSES}/${id}/ready-for-review`, { _method: 'put' });
  return data.data;
};

export const markCourseAsApproved = async (id: number | string) => {
  const { data } = await Api.post(`/courses/${id}/approve`, { _method: 'put' });
  return data.data;
};

export const updateCourse = async ({ id, payload }: any) => {
  const { data } = await Api.put(`/courses/${id}`, payload);
  return data.data;
};
