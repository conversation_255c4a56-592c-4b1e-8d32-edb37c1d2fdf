import { ProtectedComponent, useConfirmDialog, useNotify } from '@/index';
import { Icon } from '@/index';
import { AddTopicDialog } from './add-topic-dialog';
import { ICourse, ICourseTopic, ICourseTopicDNA } from '@/modules/app/course/types';
import { useEffect, useMemo, useState } from 'react';
import EditDna from '../../components/edit-dna';
import {
  useApproveCoursesInEdit,
  useApproveCoursesInReview,
  useGenerateAllDnasInTopic,
  useGetCourseById,
  useMarkCourseAsApproved,
  useMarkCourseAsReadyForReview,
} from '@/modules/app/course/apis/queries';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import {
  dateAndTimeFormat,
  getContentStatusStyle,
  handleStatusConversion,
  formatUserByNameAndEmail,
} from '@/utils/helpers';
import { useDeleteDna, useG<PERSON>rateDna, useReorderDna, useUpdateDnaData } from '@/modules/app/dna/apis/queries';
import { useDeleteTopic, useGenerateDNATopic, useUpdateTopicOrder } from '../../topic/apis/queries';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import TableContentHeader from '@/components/theTable/table-content-header';
import LanguageLink from '@/components/language-link';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useAddChecklist } from '@/apis/tool-checklist/queries';
import AddChecklistDialog from '@/modules/app/components/add-checklist-dialog';
import { EditCourseDialog } from './edit-course-dialog';
import AssignTaskDialog from './assign-task-dialog';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Separator } from '@/components/ui/separator';
import TableRowsExpander from '@/components/theTable/table-rows-expander';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import ChangeDnaData from '../../dna/components/change-title-dialog';
import PromptViewerDialog from '../../components/prompt-viewer-dialog';
import { useParams, useSearchParams } from 'react-router-dom';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import WorkflowStatusChecklist from '../../components/work-flow-status-checklist';
import { IDNA } from '../../dna/types';
import { IUser } from '@/modules/auth/types';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { EditTopicDialog } from './edit-topic-dialog';
import { set } from 'lodash';
import NotesDialog from '../../tasks/components/notes-dialog';

const CoursePlanTable = () => {
  // State
  const [isLoading, setIsLoading] = useState<any>(false);
  const [editDialog, setEditDialog] = useState<boolean>(false);
  const [selectedDnaId, setSelectedDnaId] = useState<string | null>(null);
  const [isEditingCourseOpen, setIsEditingCourseOpen] = useState(false);
  const [isEditTopicDialogOpen, setIsEditTopicDialogOpen] = useState(false);
  const [selectedTopic, setSelectedTopic] = useState<any>(null);
  const [isAssigningTaskDialogOpen, setIsAssigningTaskDialogOpen] = useState(false);
  const [notesDialog, setNotesDialog] = useState(false);
  const [dnaNotes, setDnaNotes] = useState<any>(null);
  const [open, setOpen] = useState(false);
  const [changeDataDialog, setChangeDataDialog] = useState<boolean>(false);
  const [selectedDna, setSelectedDna] = useState<any>(null);
  const [fakeLoading, setFakeLoading] = useState<boolean>(false);
  const [searchParams] = useSearchParams();

  // Hooks
  const { id } = useParams();
  const { data: course, isPending: isFetchingCourse } = useGetCourseById(searchParams.get('courseId') || id);
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { confirm } = useConfirmation();
  const { data: allmetaData } = useGetSingleMetadata('');
  const bloomTakOptions = allmetaData?.filter((item) => item.type === 'bloom"s_taxonomy') || [];
  const { notify } = useNotify();
  const { mutate: changeStatus } = useUpdateDnaData();
  const { mutate: generateTopicDna, isPending: isUpdating, variables: dnaVariables } = useGenarateDna();
  const { mutate: deleteTopic } = useDeleteTopic();
  const { mutate: deleteDna } = useDeleteDna();
  const { t, i18n } = useTranslation();
  const { mutate: addDnaInsideTopic, isPending: isGeneratingDna, variables: addDnaVariables } = useGenerateDNATopic();
  const { mutate: updateTopicOrder, isPending: isUpdatingTopicOrder } = useUpdateTopicOrder();
  const { mutate: updateDnaOrder, isPending: isUpdatingDnaOrder } = useReorderDna();
  const { mutate: approveCourse, isPending: isApproving } = useApproveCoursesInReview();
  const { mutate: approveEditCourse, isPending: isApprovingEdit } = useApproveCoursesInEdit();

  const {
    mutate: markCourseAsReadyForReview,
    isPending: isMarkingPending,
    variables: courseBeingMarked,
  } = useMarkCourseAsReadyForReview();
  const {
    mutate: generateAllDnasInTopic,
    isPending: isGeneratingAllDnas,
    variables: generateAllDnasPayload,
  } = useGenerateAllDnasInTopic();
  const { mutate: addChecklist, isPending: isPendingChecklist } = useAddChecklist();

  useEffect(() => {
    if (fakeLoading) {
      setTimeout(() => {
        setFakeLoading(false);
      }, 10000);
    }
  }, [fakeLoading]);

  // Functions
  const handleMarkAsReadyForReview = (checklist: (string | number)[]) => {
    if (course) {
      markCourseAsReadyForReview(course.id, {
        onSuccess: () => {
          addChecklist(
            { check_list_ids: checklist, tool_id: course.id, tool_type: 'Course' },
            {
              onSuccess: () => {
                setOpen(false);
              },
            }
          );
        },
      });
    }
  };

  const handelReGenerateDna = (id: number) => {
    confirm({
      variant: 'default',
      title: t('regenerate.dna'),
      description: t('regenerate.dna.description'),
      onConfirm: () => {
        generateDna(id);
      },
    });
  };
  const handelReGenerateTopic = (id: number) => {
    confirm({
      variant: 'default',
      title: t('regenerate.topic'),
      description: t('regenerate.topic.description'),
      onConfirm: () => {
        handleGenerateMultipleDnas(id);
      },
    });
  };

  const allTopicsReadyForReview = course?.topics?.every(
    (topic: any) =>
      topic.topic_status.phase_with_status === StatusClass.COURSE.EDIT.RFR ||
      topic.topic_status.phase_with_status === StatusClass.COURSE.EDIT.APPROVED
  );
  const hanleLevelText = () => {
    return (
      <div>
        <div className="flex items-center justify-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('The generated content will be reset. Are you sure you want to continue?')}</p>
      </div>
    );
  };
  const handleLevelChange = (data: any, value: number) => {
    showConfirm(hanleLevelText(), {
      async onConfirm() {
        changeStatus({
          id: data.id,
          payload: {
            bloom_tax_id: value,
          },
        });
        hideConfirm();
      },
    });
  };

  const generateDna = (id: any) => {
    setIsLoading((prev: any) => ({ ...prev, [id]: true }));
    generateTopicDna(id);
    setIsLoading(false);
  };

  const TopicConfirmText = () => {
    return (
      <div>
        <div className="flex items-center justify-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('topicCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeletTopic = async (id: number) => {
    showConfirm(TopicConfirmText(), {
      danger: true,
      async onConfirm() {
        deleteTopic(id, {
          onSuccess: () => {
            hideConfirm();
          },
        });
      },
    });
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex items-center justify-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeleteDna = async (dnaId: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        deleteDna(dnaId, {
          onSuccess: () => {
            notify.success('DNA deleted successfully');
            hideConfirm();
          },
        });
      },
    });
  };

  const handleNewDnaInsideTopic = async (id: string) => {
    addDnaInsideTopic(id, {
      onSuccess: () => {
        notify.success('DNA Added successfully');
      },
    });
  };
  const currentDnaData = selectedDnaId
    ? course?.topics.flatMap((topic: any) => topic.dnas).find((dna: any) => dna.id === Number(selectedDnaId))
    : null;

  const canEditCourseOrDna = [
    StatusClass.COURSE.EDIT.NO_CONTENT,
    StatusClass.COURSE.EDIT.DRAFT,
    StatusClass.COURSE.EDIT.FEEDBACK,
  ].includes(course?.courseStatus.phase_with_status || '');

  const handleDragEnd = (active: any, over: any) => {
    if (!over) return;
    if (active.data.current.level === 0) {
      updateTopicOrder({
        id: active?.id,
        next_topic_id: over?.id,
      });
    } else if (active.data.current.level === 1) {
      updateDnaOrder({
        id: active?.id,
        next_dna_id: over?.id,
        new_topic_id: over?.parentId,
      });
    }
  };

  const infoItems = [
    {
      label: t('topicContentPage.table.author'),
      value: formatUserByNameAndEmail(course?.user as IUser),
    },
    {
      label: t('cousrePlanContentPage.table.date'),
      value: dateAndTimeFormat(course?.created_at || '', i18n.dir()),
    },
    {
      label: t('course.table.unitNumber'),
      value: course?.topics?.length,
    },
    {
      label: t('breadcrumb.myContentPage.DNAs'),
      value: course?.topics?.reduce((total: any, topic: any) => total + (topic?.dnas?.length || 0), 0),
    },
    {
      label: t('CoursePlanCreationPage.form.subject'),
      value: i18n.language === 'en' ? course?.subject?.name_en : course?.subject?.name_ar,
    },
    {
      label: t('Audience'),
      value: i18n.language === 'en' ? course?.audience?.name_en : course?.audience?.name_ar,
    },
    {
      label: t('CoursePlanCreationPage.form.learningStrategy'),
      value: i18n.language === 'en' ? course?.learningStrategy?.name_en : course?.learningStrategy?.name_ar,
    },
    {
      label: t('CoursePlanCreationPage.form.language'),
      value: i18n.language === 'en' ? course?.language?.name_en : course?.language?.name_ar,
    },
    {
      label: t('CoursePlanCreationPage.form.creditHours'),
      value: i18n.language === 'en' ? course?.creditHours?.name_en : course?.creditHours?.name_ar,
    },
    {
      label: t('CoursePlanCreationPage.form.difficultyLevel'),
      value: i18n.language === 'en' ? course?.difficultyLevel?.name_en : course?.difficultyLevel?.name_ar,
    },
    {
      label: t('CoursePlanCreationPage.form.model'),
      value: course?.model,
      permission: 'for_administration',
    },
  ];

  const handleGenerateMultipleDnas = (id: number) => {
    generateAllDnasInTopic(id);
    setFakeLoading(true);
  };

  const allApproved = course?.topics.every((topic) => topic.topic_status?.action === 'approved');

  const columns: ITableColumn<ICourse & ICourseTopic & IDNA>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('CoursePlanCreationPage.table.title'),
        width: '350px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex items-start gap-2 py-1 transition-opacity rtl:text-right group">
                  <div className="">
                    <div className="flex gap-2">
                      <p className="font-medium">{row.title}</p>
                      <div className="transition-opacity duration-300 ease-in-out opacity-0 group-hover:opacity-100">
                        <ConditionalComponent
                          multiConditions={[
                            { status: row.topic_status, wantedStatus: [StatusClass.TOPIC.EDIT.RFR] },
                            { status: row.course_status, wantedStatus: [StatusClass.COURSE.EDIT.FEEDBACK] },
                          ]}
                          multiConditionOperator="allTogetherShouldNotPass"
                        >
                          <ConditionalComponent
                            status={row.course_status}
                            operator="not"
                            wantedStatus={StatusClass.COURSE.EDIT.RFR}
                          >
                            {/* Edit Button */}
                            <ConditionalComponent
                              status={row.topic_status}
                              wantedStatus={[StatusClass.TOPIC.EDIT.DRAFT, StatusClass.TOPIC.EDIT.FEEDBACK]}
                            >
                              <Icon
                                onClick={() => {
                                  setIsEditTopicDialogOpen(true);
                                  setSelectedTopic(row);
                                }}
                                icon="basil:edit-outline"
                                width={20}
                                className="cursor-pointer text-primary"
                              />
                            </ConditionalComponent>
                          </ConditionalComponent>
                        </ConditionalComponent>
                      </div>
                    </div>
                    <p className="text-gray-500">{row.learning_objectives}</p>
                  </div>
                </div>
              );
            case 1:
              const element = !row.dna_content ? (
                <span>{row.title}</span>
              ) : (
                <LanguageLink
                  to={`/app/my-content/courses/${course?.id}/${row.id}`}
                  className="font-medium underline cursor-pointer text-primary"
                >
                  {row.title}
                </LanguageLink>
              );
              return (
                <div className="transition-opacity rtl:text-right rtl:ms-5 group">
                  <div className="flex items-center gap-2 p-0">
                    <div className={`break-words `}>{element}</div>
                    <div className="transition-opacity duration-300 ease-in-out opacity-0 group-hover:opacity-100">
                      <ConditionalComponent
                        multiConditions={[
                          { status: row.dna_status, wantedStatus: [StatusClass.DNA.EDIT.RFR] },
                          { status: row.course_status, wantedStatus: [StatusClass.COURSE.EDIT.FEEDBACK] },
                        ]}
                        multiConditionOperator="allTogetherShouldNotPass"
                      >
                        <ConditionalComponent
                          status={row.course_status}
                          operator="not"
                          wantedStatus={StatusClass.COURSE.EDIT.RFR}
                        >
                          {/* Edit Button */}
                          <ConditionalComponent
                            status={row.dna_status}
                            wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK]}
                          >
                            <Icon
                              onClick={() => {
                                setChangeDataDialog(true);
                                setSelectedDna(row);
                              }}
                              icon="basil:edit-outline"
                              width={25}
                              className="cursor-pointer text-primary"
                            />
                          </ConditionalComponent>
                        </ConditionalComponent>
                      </ConditionalComponent>
                    </div>
                  </div>
                  <p className="font-medium text-gray-500">{row.learning_objectives}</p>
                  {['review', 'production'].includes(row?.dna_status?.phase?.action?.toLowerCase()) && (
                    <WorkflowStatusChecklist dna={row} needsPermissions={false} />
                  )}
                </div>
              );
            default:
              '_';
          }
        },
      },

      {
        accessorKey: 'dnas',
        header: t('CoursePlanCreationPage.table.finishedDnas'),
        width: '100px',
        cell: ({ row, level }) => {
          const countDnasWithAction = row?.dnas?.filter((dna) => dna?.dna_status?.action === 'ready_for_review').length;
          switch (level) {
            case 0:
              return (
                <div className="rtl:text-right">
                  {countDnasWithAction} / {row?.dnas?.length}
                </div>
              );
          }
        },
      },
      {
        accessorKey: 'length',
        header: t('CoursePlanCreationPage.table.length'),
        width: '120px',
        cell: ({ row, level }) => {
          const length =
            level === 0
              ? row.dnas.reduce((acc, lesson) => acc + parseInt(lesson?.audio_length?.split('min')?.[0] || '0'), 0)
              : row.audio_length.split('min')?.[0] || '0';
          return (
            <div className={`font-medium rtl:text-right ${level !== 0 ? 'max-w-15' : ''}`}>
              <span>
                {length} {t('minute')}
              </span>
            </div>
          );
        },
      },
      {
        accessorKey: 'bloom_tax',
        header: t('CoursePlanCreationPage.table.bloomTax'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return (
                <div className="rtl:text-right">
                  {/* <Combobox
                    placeholder={t('CoursePlanCreationPage.table.bloomTax')}
                    disabled={
                      course?.courseStatus?.action === 'ready_for_review' ||
                      course?.courseStatus?.action === 'ready_for_production' ||
                      course?.courseStatus?.action === 'approved' ||
                      course?.courseStatus?.phase?.action === 'Review' ||
                      row.dna_status?.action === 'approved'
                    }
                    options={generateEnum(bloomTakOptions, 'id', labelKey)}
                    value={row?.bloom_tax?.id}
                    onChange={(value: any) => handleLevelChange(row, value)}
                  /> */}
                  {i18n.language === 'en' ? row?.bloom_tax.name_en : row?.bloom_tax.name_ar}
                </div>
              );
          }
        },
      },
      {
        accessorKey: 'status',
        header: t('CoursePlanCreationPage.table.status'),
        width: '120px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return getContentStatusStyle(row?.topic_status);
            case 1:
              return getContentStatusStyle(row?.dna_status);
          }
        },
      },
      {
        accessorKey: 'notes',
        header: t('CoursePlanCreationPage.table.notes'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return row.dnas.filter((dna) => dna.reviewer_notes !== null)?.length || '_';
            case 1:
              return row.reviewer_notes ? (
                <TooltipProvider delayDuration={100}>
                  <Tooltip>
                    <TooltipTrigger>
                      <Icon
                        onClick={() => {
                          setNotesDialog(true), setDnaNotes(row);
                        }}
                        icon={'tabler:notes'}
                        width={25}
                        className="cursor-pointer text-primary"
                      />
                    </TooltipTrigger>
                    <TooltipContent> {t('myTaskTable.reviewerNotes')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                '_'
              );
          }
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '200px',
        cell: ({ row, level, parentRow }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex items-center justify-end">
                  {row?.multi_dnas_generation_status !== 'completed ' && (
                    <ConditionalComponent status={row.topic_status} wantedStatus={[StatusClass.TOPIC.EDIT.NO_CONTENT]}>
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Button
                              loading={
                                row?.multi_dnas_generation_status === 'processing' ||
                                (isGeneratingAllDnas && generateAllDnasPayload === row.id) ||
                                (fakeLoading && generateAllDnasPayload === row.id)
                              }
                              onClick={() => {
                                handleGenerateMultipleDnas(row.id);
                              }}
                              className="flex items-center gap-2 "
                              type="button"
                            >
                              {row.topic_status.phase_with_status === StatusClass.TOPIC.EDIT.NO_CONTENT
                                ? t('generateAllDnasInsideTopic')
                                : row?.multi_dnas_generation_status === 'failed'
                                ? t('generateAllDnasInsideTopic.tryAgain')
                                : ''}
                              <Icon icon="mdi:stars-outline" width={20} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            {row?.multi_dnas_generation_status === 'failed'
                              ? t('generateAllDnasInsideTopic.tryAgain')
                              : t('generateAllDnasInsideTopic')}
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </ConditionalComponent>
                  )}
                  <ConditionalComponent status={row.topic_status} wantedStatus={[StatusClass.TOPIC.EDIT.DRAFT]}>
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            loading={
                              row?.multi_dnas_generation_status === 'processing' ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.id) ||
                              (fakeLoading && generateAllDnasPayload === row.id)
                            }
                            onClick={() => {
                              handelReGenerateTopic(row.id);
                            }}
                            variant={row?.multi_dnas_generation_status === 'failed' ? 'outline' : 'ghost'}
                            size={row?.multi_dnas_generation_status === 'failed' ? 'default' : 'icon'}
                            className="flex items-center gap-2 w-auto mx-1.5"
                            type="button"
                          >
                            {row.topic_status.phase_with_status === StatusClass.TOPIC.EDIT.NO_CONTENT
                              ? t('generateAllDnasInsideTopic')
                              : row?.multi_dnas_generation_status === 'failed'
                              ? t('generateAllDnasInsideTopic.tryAgain')
                              : ''}
                            <Icon icon="mdi:stars-outline" width={20} className="text-primary" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          {row?.multi_dnas_generation_status === 'failed'
                            ? t('generateAllDnasInsideTopic.tryAgain')
                            : t('reGenerateAllDnasInsideTopic')}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ConditionalComponent>
                  <ConditionalComponent
                    status={course?.courseStatus}
                    wantedStatus={[StatusClass.COURSE.EDIT.DRAFT, StatusClass.COURSE.EDIT.NO_CONTENT]}
                  >
                    <ProtectedComponent requiredPermissions={'dna_create'}>
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Button
                              type="button"
                              disabled={
                                row?.multi_dnas_generation_status === 'processing' ||
                                (isGeneratingAllDnas && generateAllDnasPayload === row.id) ||
                                (fakeLoading && generateAllDnasPayload === row.id)
                              }
                              variant={'ghost'}
                              size={'icon'}
                              loading={isGeneratingDna && addDnaVariables === row.id}
                              onClick={() => handleNewDnaInsideTopic(row.id)}
                            >
                              <Icon icon="ep:circle-plus" width={25} className="text-primary" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent> {t('CoursePlanCreationPage.addDna')}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </ProtectedComponent>
                  </ConditionalComponent>
                  {/* <ConditionalComponent
                    status={course?.courseStatus}
                    wantedStatus={[
                      StatusClass.COURSE.EDIT.DRAFT,
                      StatusClass.COURSE.EDIT.NO_CONTENT,
                      StatusClass.COURSE.EDIT.FEEDBACK,
                    ]}
                  >
                    <ProtectedComponent requiredPermissions={'topic_edit'}>
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Button
                              onClick={() => {
                                setIsEditTopicDialogOpen(true);
                                setSelectedTopic(row as any);
                              }}
                              className="aspect-square"
                              variant={'outline'}
                              size={'icon'}
                              type="button"
                            >
                              <Icon icon="basil:edit-outline" className="text-primary" width={25} />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent> {t('CoursePlanCreationPage.editTopic')}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </ProtectedComponent>
                  </ConditionalComponent> */}
                  <ConditionalComponent
                    status={course?.courseStatus}
                    wantedStatus={[
                      StatusClass.COURSE.REVIEW.PRODUCTION,
                      StatusClass.COURSE.REVIEW.APPROVED,
                      StatusClass.COURSE.EDIT.RFR,
                      StatusClass.COURSE.REVIEW.FEEDBACK,
                      StatusClass.COURSE.REVIEW.DRAFT,
                    ]}
                    operator="not"
                  >
                    <ConditionalComponent
                      status={row.topic_status}
                      wantedStatus={[StatusClass.COURSE.EDIT.APPROVED]}
                      operator="not"
                    >
                      <ProtectedComponent requiredPermissions={'topic_delete'}>
                        {row?.topic_status?.phase?.action != 'Production' && (
                          <Button
                            disabled={
                              row?.multi_dnas_generation_status === 'processing' ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.id) ||
                              (fakeLoading && generateAllDnasPayload === row.id)
                            }
                            onClick={() => handleDeletTopic(row.id)}
                            variant={'ghost'}
                            size={'icon'}
                            type="button"
                          >
                            <Icon icon="gg:trash" width={25} className="text-red-500 cursor-pointer" />
                          </Button>
                        )}
                      </ProtectedComponent>
                    </ConditionalComponent>
                  </ConditionalComponent>
                </div>
              );
            case 1:
              return (
                <div className="flex items-center justify-end gap-2">
                  {/* Generate Button */}
                  <ConditionalComponent status={row.dna_status} wantedStatus={[StatusClass.DNA.EDIT.NO_CONTENT]}>
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            loading={
                              isLoading[row.id] ||
                              (isUpdating && row.id === dnaVariables) ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.topic_id)
                            }
                            disabled={
                              parentRow?.multi_dnas_generation_status === 'processing' ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.topic_id) ||
                              (fakeLoading && generateAllDnasPayload === row.topic_id)
                            }
                            onClick={() => generateDna(row.id)}
                            className="flex items-center gap-2 w-auto mx-1.5"
                            type="button"
                          >
                            {t('generate')}
                            <Icon icon="mdi:stars-outline" width={20} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('generate')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ConditionalComponent>
                  <ConditionalComponent status={row.dna_status} wantedStatus={[StatusClass.DNA.EDIT.DRAFT]}>
                    <TooltipProvider delayDuration={100}>
                      <Tooltip>
                        <TooltipTrigger>
                          <Button
                            loading={
                              isLoading[row.id] ||
                              (isUpdating && row.id === dnaVariables) ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.topic_id)
                            }
                            variant={'ghost'}
                            size={'icon'}
                            disabled={
                              parentRow?.multi_dnas_generation_status === 'processing' ||
                              (isGeneratingAllDnas && generateAllDnasPayload === row.topic_id) ||
                              (fakeLoading && generateAllDnasPayload === row.topic_id)
                            }
                            onClick={() => handelReGenerateDna(row.id)}
                            className="flex items-center gap-2 w-auto mx-1.5"
                            type="button"
                          >
                            <Icon icon="mdi:stars-outline" width={20} className="text-primary" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('regenerate.dna')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ConditionalComponent>
                  <ConditionalComponent
                    multiConditions={[
                      { status: row.dna_status, wantedStatus: [StatusClass.DNA.EDIT.RFR] },
                      { status: row.course_status, wantedStatus: [StatusClass.COURSE.EDIT.FEEDBACK] },
                    ]}
                    multiConditionOperator="allTogetherShouldNotPass"
                  >
                    <ConditionalComponent
                      status={row.course_status}
                      operator="not"
                      wantedStatus={StatusClass.COURSE.EDIT.RFR}
                    >
                      {/* Edit Button */}
                      <ConditionalComponent
                        status={row.dna_status}
                        wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK]}
                      >
                        <TooltipProvider delayDuration={100}>
                          <Tooltip>
                            <TooltipTrigger>
                              <Icon
                                onClick={() => {
                                  setEditDialog(true);
                                  setSelectedDnaId(row?.id);
                                }}
                                icon="basil:edit-outline"
                                width={22}
                                className="cursor-pointer text-primary"
                              />
                            </TooltipTrigger>
                            <TooltipContent>{t('edit')}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </ConditionalComponent>
                    </ConditionalComponent>
                    {/* Delete Button */}
                    <ProtectedComponent requiredPermissions={'dna_delete'}>
                      <ConditionalComponent
                        status={row.dna_status}
                        wantedStatus={[StatusClass.DNA.EDIT.NO_CONTENT, StatusClass.DNA.EDIT.DRAFT]}
                      >
                        {row?.dna_status?.phase?.action != 'Production' && (
                          <TooltipProvider delayDuration={100}>
                            <Tooltip>
                              <TooltipTrigger>
                                <Button
                                  size="icon"
                                  type="button"
                                  variant={'ghost'}
                                  disabled={
                                    parentRow?.multi_dnas_generation_status === 'processing' ||
                                    (isGeneratingAllDnas && generateAllDnasPayload === row.topic_id) ||
                                    (fakeLoading && generateAllDnasPayload === row.topic_id)
                                  }
                                  onClick={() => handleDeleteDna(row.id)}
                                >
                                  <Icon icon="gg:trash" width="25" className="text-red-500 cursor-pointer" />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>{t('delete')}</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                      </ConditionalComponent>
                    </ProtectedComponent>
                  </ConditionalComponent>
                </div>
              );
            default:
              return '_';
          }
        },
      },
    ];
  }, [
    t,
    isLoading,
    isUpdating,
    bloomTakOptions,
    isGeneratingDna,
    isUpdatingDnaOrder,
    isEditingCourseOpen,
    selectedDnaId,
    isAssigningTaskDialogOpen,
    addDnaVariables,
    isUpdatingTopicOrder,
    isMarkingPending,
    courseBeingMarked,
    course,
    isFetchingCourse,
    fakeLoading,
  ]);

  if (!course) {
    return null;
  }
  const ConditionalTitle = (courseStatus: string) => {
    if (courseStatus === StatusClass.COURSE.EDIT.FEEDBACK) {
      return <p>• {t('CourseAlertTitle.feedback')}</p>;
    } else if (courseStatus === StatusClass.COURSE.EDIT.RFR) {
      return <p>• {t('CourseAlertTitle.readyForReview')}</p>;
    } else {
      return <p>• {t('CourseAlertTitle')}</p>;
    }
  };

  return (
    <div className="space-y-3">
      <Alert className="text-indigo-700 bg-blue-50 dark:bg-background">
        <AlertTitle className="text-xl font-medium">
          <div className="flex items-center gap-1">
            <Icon className="mt-1 text-indigo-700" icon="mdi:alert-circle" width={18} />
            <p>{t('dnaSinglePage.dnaAlertTitle')}</p>
          </div>
        </AlertTitle>
        <AlertDescription className="ml-2 text-sm">
          {ConditionalTitle(course?.courseStatus?.phase_with_status)}
        </AlertDescription>
      </Alert>
      <div className="bg-white border rounded-md shadow-sm dark:bg-background border-1">
        <Accordion type="single" collapsible>
          <AccordionItem className="border-none" value="item-1">
            <AccordionTrigger className="gap-5 p-4 font-light border hover:no-underline bg-muted rounded-t-md border-border">
              <div className="flex justify-between w-full">
                <h3 className="flex items-center gap-1 text-xl capitalize">
                  {course?.title}
                  <ConditionalComponent
                    status={course?.courseStatus}
                    wantedStatus={[
                      StatusClass.COURSE.EDIT.DRAFT,
                      StatusClass.COURSE.EDIT.NO_CONTENT,
                      StatusClass.COURSE.EDIT.FEEDBACK,
                    ]}
                  >
                    <Icon
                      onClick={() => setIsEditingCourseOpen(true)}
                      icon="mynaui:edit-one"
                      width={22}
                      className="cursor-pointer mt-1.5 ml-1"
                    />
                  </ConditionalComponent>
                </h3>
                <p className="text-sm">{getContentStatusStyle(course?.courseStatus)}</p>
              </div>
              <Separator orientation="vertical" className="h-8" />
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                {infoItems.map((item, index) => (
                  <ProtectedComponent requiredPermissions={item.permission || 'public'}>
                    <div key={index} className="space-y-1">
                      <p className="opacity-50">{item.label}</p>
                      <p>{item.value || '-'}</p>
                    </div>
                  </ProtectedComponent>
                ))}
                <ProtectedComponent requiredPermissions={'for_administration'}>
                  <div className="space-y-1">
                    <p className="p-2 opacity-50">{t('prompt')}</p>
                    <PromptViewerDialog prompt={course?.prompt} model={course?.model} />
                  </div>
                </ProtectedComponent>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
      <Table
        rows={course?.topics || []}
        columns={columns}
        nestedConfig={{
          enabled: true,
          childProperties: ['dnas'],
        }}
        draggingConfig={{
          enabled: canEditCourseOrDna,
          onDragEnd: handleDragEnd,
        }}
      >
        <TableContent>
          <TableContentHeader className="flex justify-between mt-0">
            <div className="flex justify-between w-full">
              <div className="flex items-center gap-4">
                <TableColumsExpander />
                <p>{t('breadcrumb.myContentPage.DNAs')}</p>
                <TableRowsExpander />
              </div>
              <div className="flex gap-4">
                {/* {course?.has_tasks && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Icon
                          icon="material-symbols:assignment-turned-in-rounded"
                          className="text-green-500"
                          width="24"
                          height="24"
                        />
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.assigned')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )} */}
                {course?.tasks?.can_create_new_task && (
                  <ProtectedComponent requiredPermissions={'task_create'}>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => {
                              setIsAssigningTaskDialogOpen(true);
                            }}
                          >
                            <Icon
                              className="text-primary"
                              icon="material-symbols:assignment-add-outline-rounded"
                              width={25}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('cousrePlanContentPage.assignTask')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ProtectedComponent>
                )}
                <ConditionalComponent
                  status={course?.courseStatus}
                  wantedStatus={[StatusClass.COURSE.EDIT.DRAFT, StatusClass.COURSE.EDIT.FEEDBACK]}
                >
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex gap-2">
                          <ProtectedComponent requiredPermissions={'course_ready_for_review'}>
                            <Button
                              disabled={
                                isMarkingPending ||
                                course?.courseStatus.phase_with_status === StatusClass.COURSE.EDIT.RFR ||
                                !allTopicsReadyForReview
                              }
                              className="flex gap-2 items-center min-w-[120px]"
                              loading={isMarkingPending && course.id === courseBeingMarked}
                              onClick={() => setOpen(true)}
                            >
                              {t('cousrePlanContentPage.finish')}
                            </Button>
                          </ProtectedComponent>
                          {/* <ProtectedComponent requiredPermissions={'course_approve'}>
                          <Button
                            disabled={course.courseStatus.phase_with_status !== StatusClass.COURSE.EDIT.RFR}
                            loading={isPendingApprove}
                            className="flex items-center gap-2"
                            onClick={() => mutateApproveCourse(course.id)}
                          >
                            {t('cousrePlanContentPage.approve')}
                            {course.courseStatus.action === 'approved' && (
                              <Icon icon="solar:check-read-linear" width={25} className={'text-green-500'} />
                            )}
                          </Button>
                        </ProtectedComponent> */}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        {allTopicsReadyForReview
                          ? t('cousrePlanContentPage.markAsReadyForReview')
                          : t('cousrePlanContentPage.markAsReadyForReview.disabled')}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </ConditionalComponent>
                <ConditionalComponent
                  status={course?.courseStatus}
                  wantedStatus={[StatusClass.COURSE.REVIEW.PRODUCTION]}
                >
                  <Button onClick={() => approveCourse(Number(course.id))} disabled={isApproving}>
                    {t('reviewTask.approveCourse')}
                  </Button>
                </ConditionalComponent>
                <ProtectedComponent requiredPermissions={'course_approve'}>
                  <ConditionalComponent status={course?.courseStatus} wantedStatus={[StatusClass.COURSE.EDIT.RFR]}>
                    <Button
                      onClick={() => approveEditCourse(Number(course.id))}
                      disabled={!allApproved || isApprovingEdit}
                      loading={isApprovingEdit}
                    >
                      {t('courseEdit.approveCourse')}
                    </Button>
                  </ConditionalComponent>
                </ProtectedComponent>
              </div>
            </div>
          </TableContentHeader>

          <TableContentBody />
          <ConditionalComponent
            status={course?.courseStatus}
            wantedStatus={[StatusClass.COURSE.EDIT.DRAFT, StatusClass.COURSE.EDIT.NO_CONTENT]}
          >
            <div className="flex justify-center p-6 border border-border">
              <AddTopicDialog courseId={course.id} />
            </div>
          </ConditionalComponent>
        </TableContent>
      </Table>
      {editDialog && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false);
            setSelectedDnaId(null);
          }}
          data={currentDnaData}
        />
      )}

      {isEditingCourseOpen && (
        <EditCourseDialog course={course} open={isEditingCourseOpen} setOpen={setIsEditingCourseOpen} />
      )}
      {open && (
        <AddChecklistDialog
          open={open}
          setOpen={setOpen}
          callback={handleMarkAsReadyForReview}
          loading={isMarkingPending || isPendingChecklist}
          type="ownerCourse"
        />
      )}
      {isAssigningTaskDialogOpen && (
        <AssignTaskDialog
          onOpen={isAssigningTaskDialogOpen}
          onClose={() => {
            setIsAssigningTaskDialogOpen(false);
          }}
          course={course}
        />
      )}
      {changeDataDialog && (
        <ChangeDnaData
          data={selectedDna}
          onOpen={changeDataDialog}
          onOpenChange={() => {
            setChangeDataDialog(false), setSelectedDna(null);
          }}
        />
      )}
      {isEditTopicDialogOpen && (
        <EditTopicDialog
          topic={selectedTopic}
          onOpen={isEditTopicDialogOpen}
          onOpenChange={() => {
            setIsEditTopicDialogOpen(false), setSelectedTopic(null);
          }}
        />
      )}
      {notesDialog && <NotesDialog isOpen={notesDialog} data={dnaNotes} setIsOpen={setNotesDialog} viewOnly />}
    </div>
  );
};

export { CoursePlanTable };
