import { Api } from '@/services';
import { api } from '@/services/axios';
export const getLanguages = async () => {
  const { data } = await Api.get('/languages');
  return data.data;
};
export const createTranslationKey = async (payload: {
  key: string;
  translations: { language_id: number; value: string }[];
}) => {
  const { data } = await Api.post('/translation-keys', payload);
  return data.data;
};

export const updateLanguageKey = async (translations: { language_id: number; key_id: number; value: string } | any) => {
  const { data } = await Api.post('/translations/bulk-save/all-languages', { translations });
  return data.data;
};

export const importTranslations = async (payload: { excel: File }) => {
  const { data } = await api.post('/translations/import-data', payload, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data.data;
};

export const exportTranslations = async () => {
  const response = await api.get(`/translations/export-data`, {
    responseType: 'blob',
  });

  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', 'translations.xlsx');
  document.body.appendChild(link);
  link.click();
  link.remove();
  window.URL.revokeObjectURL(url);

  return response.data;
};
