import { useTranslation } from 'react-i18next';
import { toast } from 'sonner';

export function useNotify() {
  const { t } = useTranslation();

  const notify = (message: string) => {
    toast(message || t('somethingwentwrong'));
  };

  notify.error = (message: string) => {
    toast.error(message || t('somethingwentwrong'));
  };

  notify.success = (message: string) => {
    toast.success(message || t('somethingwentwrong'));
  };

  notify.info = (message: string) => {
    toast.info(message || t('somethingwentwrong'));
  };

  return { notify };
}
