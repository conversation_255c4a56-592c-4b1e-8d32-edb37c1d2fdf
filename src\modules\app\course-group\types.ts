import { ICourse } from '../course/types';
import { IStatus } from '../dashboard/modules-status/types';

export interface ICoursesSimplified {
  id: string;
  title: string;
  info: string;
}

export interface ICreateCourseGroup {
  title: string;
  description: string;
  courses_ids: string[];
}

export interface IUpdateCourseGroup extends ICreateCourseGroup {
  id: string;
}

export interface ICourseGroupList {
  id: number;
  title: string;
  description: string;
  total_notes: number | string;
  tags: IMetadata[];
  status: IStatus;
  courses: ICourse[];
}
