import React from 'react';
import { useGetTaskByCode } from '@/modules/app/tasks/apis/queries';
import { useParams } from 'react-router-dom';

interface ProtectedComponentProps {
  children: React.ReactNode;
  requiredPermissions: string | string[];
  fallbackComponent?: React.ReactNode;
  requireAll?: boolean;
}

const ProtectedTaskComponent: React.FC<ProtectedComponentProps> = ({
  children,
  requiredPermissions,
  fallbackComponent = null,
  requireAll = true,
}) => {
  if (requiredPermissions === 'public') {
    return <>{children}</>;
  }
  const { taskId } = useParams();
  const { data } = useGetTaskByCode(taskId || '');

  const permissionFinder = (permissions: string | string[]) => {
    if (!data?.operations) return false;

    if (Array.isArray(permissions)) {
      return requireAll
        ? permissions?.every((permission) =>
            data.operations.some((operation) => operation.permissions.some((p) => p.name === permission))
          )
        : permissions.some((permission) =>
            data.operations.some((operation) => operation.permissions.some((p) => p.name === permission))
          );
    } else {
      return data.operations.some((operation) => operation.permissions.some((p) => p.name === permissions));
    }
  };

  const hasPermission = permissionFinder(requiredPermissions);

  if (!hasPermission) {
    return <>{fallbackComponent}</>;
  }

  return <>{children}</>;
};

export { ProtectedTaskComponent };
