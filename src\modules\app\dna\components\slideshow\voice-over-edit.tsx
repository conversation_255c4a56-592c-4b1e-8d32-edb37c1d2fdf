import { Form, Icon, Textarea } from '@/components';
import { useForm, useValidate } from '@/hooks';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { DnaEnums } from '@/services';
import { Modal } from '@/components';
import { Button } from '@/components/ui/button';
import { useGenerateVoiceOverSample, useUpdateVoiceOverTextAndAudio } from '../../apis/queries';
import { ISlide } from '../../apis/slideshow/types';
import AudioPlayer from 'react-h5-audio-player';
interface Props {
  slide: ISlide;
  slideshow_id: number;
  dnaId: number;
  open: boolean;
  setOpen: any;
}

const VoiceOverEdit = ({ slide, slideshow_id, dnaId, open, setOpen }: Props) => {
  const { t } = useTranslation();
  const { isRequired } = useValidate();
  const { mutate, isPending } = useUpdateVoiceOverTextAndAudio();
  const [generatedAudio, setGeneratedAudio] = useState('');
  const { mutate: generateVoiceOverSample, isPending: isGeneratingVoiceSample } = useGenerateVoiceOverSample();

  const { form, setFieldValue } = useForm({
    voice_over: slide.voice_over || '',
    voice_over_sound: slide.sound_type,
  });
  const icons = {
    play: <Icon icon="qlementine-icons:play-16" className="text-primary " width={18} />,
    next: <Icon icon="solar:skip-next-broken" className="text-foreground" width={23} />,
    previous: <Icon icon="solar:skip-previous-broken" className="text-foreground" width={23} />,
    pause: <Icon icon="qlementine-icons:pause-16" className="text-primary" width={18} />,
  };

  useEffect(() => {
    setFieldValue('voice_over')(slide.voice_over);
    setFieldValue('voice_over_sound')(slide.sound_type);
  }, [slide]);

  const handleSubmit = () => {
    let payload: any = {
      dnaId: dnaId,
      slideshow_id: slideshow_id,
      slide_id: slide.id,
      voice_over_sound: form.voice_over_sound,
      voice_over: form.voice_over,
    };
    if (generatedAudio) {
      payload.audio = generatedAudio;
    }
    mutate(payload, {
      onSuccess: () => {
        setOpen(false);
      },
    });
  };

  return (
    <Modal open={open} width={420} onOpenChange={setOpen} modalHeader={t('slieshow.voiceOverEdit')}>
      <Form onSubmit={handleSubmit} id="slide-editor-voice">
        <div className="space-y-4 voice-over-dialog">
          <Textarea
            name="voice_over"
            label={t('slieshow.slideVoiceOver')}
            placeholder={t('slieshow.slideDescription')}
            value={form.voice_over}
            onChange={setFieldValue('voice_over')}
          />
          <div className="flex gap-2 items-end">
            <div className="w-full">
              <ComboboxInput
                placeholder={t('Select voice sound type')}
                label={'Voice sound type'}
                options={DnaEnums.voice_type}
                value={form.voice_over_sound}
                onChange={setFieldValue('voice_over_sound')}
                dropIcon
                validators={[isRequired()]}
              />
            </div>
            <Button
              variant={'outline'}
              type="button"
              onClick={() =>
                generateVoiceOverSample(
                  { voice_over: form.voice_over, voice_over_sound: form.voice_over_sound },
                  {
                    onSuccess: (data) => {
                      setGeneratedAudio(data);
                    },
                  }
                )
              }
              loading={isGeneratingVoiceSample}
              disabled={isGeneratingVoiceSample}
            >
              {t('createAudio')}
            </Button>
          </div>

          <AudioPlayer
            src={generatedAudio || slide.voice_over_url || ''}
            autoPlay={false}
            autoPlayAfterSrcChange={false}
            className="p-0 !px-0.5 !mt-4"
            layout="horizontal"
            showSkipControls={false}
            showJumpControls={false}
            showFilledVolume={false}
            customIcons={icons}
          />
        </div>
        <div className="grid grid-cols-3 gap-4 mt-10">
          <Button className="col-span-2" type="submit" loading={isPending} disabled={isPending}>
            {t('save')}
          </Button>
          <Button variant={'outline'} onClick={() => setOpen(false)}>
            {t('cancel')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default VoiceOverEdit;
