import { cn } from '@/lib/utils';
import React, { memo } from 'react';
import TableTabs from './table-tabs';
interface TableHeaderProps {
  children?: React.ReactNode;
  className?: string;
  tabs?: { tab: string; route: string }[];
}

const TableContentHeader: React.FC<TableHeaderProps> = ({ children, className, tabs }) => {
  return (
    <div className={cn('relative', tabs && tabs.length > 0 && '')}>
      {tabs && tabs.length > 0 && <TableTabs tabs={tabs} />}
      <div className={cn('flex items-center gap-3 p-4 py-5 overflow-y-auto', className)}>{children}</div>
    </div>
  );
};

export default memo(TableContentHeader);
