import { useMemo, useState } from 'react';
import { useFetchList, ProtectedComponent, Icon } from '@/index';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { dateAndTimeFormat, formatUserByNameAndEmail, getAllNames, getContentStatusStyle } from '@/utils/helpers';
import { ICourse } from '@/modules/app/course/types';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import LanguageLink from '@/components/language-link';
import { useGetOperations } from '../../dashboard/operation/apis/queries';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import AssignTaskDialog from '../components/assign-task-dialog';
const ContentLibrary = () => {
  //  Hooks
  const { t, i18n } = useTranslation();
  const { data: operations } = useGetOperations();
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<ICourse | null>(null);
  const operationsIds = useMemo(() => operations?.map((operation) => operation.id) || [], [operations]);

  const { loading, list, count, refresh, search, filters, pagination } = useFetchList(
    '/content-lib',
    'contentLibrary',
    {
      search: '',
      pagination: {
        page_num: 1,
        page_size: 30,
      },
      filters: {
        ...(useHasPermission('user_index')
          ? {
              user_id: {
                api: 'user/lookup',
                placeholder: 'dnaConentPgae.userFIlter',
              },
            }
          : {}),
      },
    }
  );

  const labelKey = i18n.dir() === 'ltr';

  const columns = useMemo((): ITableColumn<ICourse>[] => {
    return [
      {
        accessorKey: 'status',
        header: t('cousrePlanContentPage.table.status'),
        width: '250px',
        cell: ({ row }) => getContentStatusStyle(row?.courseStatus),
      },
      {
        accessorKey: 'title',
        header: t('cousrePlanContentPage.table.title'),
        width: '200px',
        cell: ({ row }) => (
          <div className="w-full justify-between flex rtl:text-right">
            <LanguageLink to={`/app/my-content/courses/${row.id}`} className="font-semibold text-primary">
              {row.title}
            </LanguageLink>
          </div>
        ),
      },
      {
        accessorKey: 'subject',
        header: t('topicContentPage.table.subject'),
        width: '180px',
        cell: ({ row }) => getAllNames(row.subject, i18n.language),
      },
      {
        accessorKey: 'audience',
        header: t('topicContentPage.table.audience'),
        width: '200px',
        cell: ({ row }) => getAllNames(row.audience, i18n.language),
      },
      {
        accessorKey: 'language',
        header: t('topicContentPage.table.language'),
        width: '100px',
        cell: ({ row }) => getAllNames(row.language, i18n.language),
      },
      {
        accessorKey: 'level',
        header: t('cousrePlanContentPage.table.level'),
        width: '100px',
        cell: ({ row }) => (
          <p className="rtl:text-right">{labelKey ? row.difficultyLevel.name_en : row.difficultyLevel.name_ar}</p>
        ),
      },
      {
        accessorKey: 'date',
        header: t('cousrePlanContentPage.table.date'),
        width: '110px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'user',
        header: t('cousrePlanContentPage.table.author'),
        width: '200px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.user);
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '100px',
        cell: ({ row }) => (
          <div className="flex  gap-1 ">
            {/* Assign task Button */}
            {row.tasks.can_create_new_task && (
              <ProtectedComponent requiredPermissions={'task_create'}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => {
                          setIsTaskDialogOpen(true);
                          setSelectedCourse(row);
                        }}
                      >
                        <Icon
                          className="text-primary"
                          icon="material-symbols:assignment-add-outline-rounded"
                          width={25}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('cousrePlanContentPage.assignTask')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ProtectedComponent>
            )}
          </div>
        ),
      },
    ];
  }, [operationsIds, t, i18n]);

  return (
    <>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader>
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {isTaskDialogOpen && (
        <AssignTaskDialog
          onOpen={isTaskDialogOpen}
          onClose={() => {
            setIsTaskDialogOpen(false), setSelectedCourse(null);
          }}
          course={selectedCourse}
        />
      )}
    </>
  );
};

export default ContentLibrary;
