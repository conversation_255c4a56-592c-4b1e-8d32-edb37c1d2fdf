import { useTable } from './context';
import { TableRow, TableHeader, TableHead, TableBody, Table, TableCell } from '../ui/table';
import { Icon, useNotify } from '@/index';
import { memo, useCallback, useState } from 'react';
import { Input } from '../ui/input';
import { useTranslation } from 'react-i18next';
import {
  DndContext,
  PointerSensor,
  useSensor,
  useSensors,
  DragStartEvent,
  DragEndEvent,
  DragOverEvent,
  DragOverlay,
  closestCenter,
} from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { renderRows } from './helpers';

function TableContent() {
  const {
    rows,
    setRows,
    columns,
    selectedRows,
    onSelectAll,
    loading,
    isMultipleSelect,
    isDraggable,
    rowKey,
    isRowSelected,
    onSelectRow,
    nestedConfig,
    draggingConfig,
    expandedRows,
    setExpandedRows,
  } = useTable();

  const { notify } = useNotify();
  const { t } = useTranslation();

  // Track the item being dragged for the DragOverlay
  const [activeItem, setActiveItem] = useState<any>(null);

  // Sensors
  const sensors = useSensors(useSensor(PointerSensor));

  // If you want to get child rows from the current row
  function getChildRows(row: any, level: number) {
    if (!nestedConfig.enabled) return null;
    const propertyName = nestedConfig.childProperties[level];
    if (!propertyName) return null;
    const children = row[propertyName];
    return Array.isArray(children) && children.length > 0 ? children : null;
  }

  // Create or remove expansions
  const toggleRow = useCallback((rowPath: string) => {
    setExpandedRows((prev: any) => {
      const newExpanded = new Set(prev);
      if (newExpanded.has(rowPath)) {
        newExpanded.delete(rowPath);
      } else {
        newExpanded.add(rowPath);
      }
      return newExpanded;
    });
  }, []);

  // ---- DnD Handlers ----
  function handleDragStart({ active: { data } }: DragStartEvent) {
    setActiveItem(data.current);
    const dragLevel = data.current?.level;
    const newExpanded = new Set(expandedRows);
    for (const path of expandedRows as any) {
      const pathLevel = path.split('->').length - 1;
      if (pathLevel === dragLevel) {
        newExpanded.delete(path);
      }
    }
    newExpanded.delete(data.current?.rowPath);
    setExpandedRows(newExpanded);
  }

  function handleDragOver({ active, over }: DragOverEvent) {
    const { level: ActiveLevel, parentId: ActiveParentId } = active?.data?.current || {};
    const { level: OverLevel, parentId: OverParentId } = over?.data?.current || {};

    if (ActiveParentId == OverParentId) {
      return;
    }

    if (ActiveLevel > OverLevel) {
      let newRows = [...rows];
      const oldTopicIndex = newRows.findIndex((t) => t?.id == ActiveParentId);
      if (oldTopicIndex == -1) return;
      const oldTopic = { ...newRows[oldTopicIndex], dnas: [...newRows[oldTopicIndex].dnas] };
      const dnaIndex = oldTopic.dnas.findIndex((dna: any) => dna?.id == active?.id);
      if (dnaIndex == -1) return;
      const [movedDNA] = oldTopic.dnas.splice(dnaIndex, 1);
      newRows[oldTopicIndex] = oldTopic;
      const newTopicIndex = newRows.findIndex((t) => t?.id == over?.id);
      if (newTopicIndex == -1) return;
      const newTopic = { ...newRows[newTopicIndex], dnas: [...newRows[newTopicIndex].dnas] };
      newTopic.dnas.push({ ...movedDNA, just_moved: true });
      newRows[newTopicIndex] = newTopic;
      expandedRows.add(over?.id?.toString() || '');

      setRows(newRows);
      return;
    }

    let newRows = [...rows];
    const oldTopicIndex = newRows.findIndex((t) => t?.id == ActiveParentId);
    if (oldTopicIndex == -1) return;
    const oldTopic = { ...newRows[oldTopicIndex], dnas: [...newRows[oldTopicIndex].dnas] };
    const dnaIndex = oldTopic.dnas.findIndex((dna: any) => dna?.id == active?.id);
    if (dnaIndex == -1) return;
    const [movedDNA] = oldTopic.dnas.splice(dnaIndex, 1);
    newRows[oldTopicIndex] = oldTopic;
    const newTopicIndex = newRows.findIndex((t) => t?.id == OverParentId);
    if (newTopicIndex == -1) return;
    const newTopic = { ...newRows[newTopicIndex], dnas: [...newRows[newTopicIndex].dnas] };
    newTopic.dnas.push({ ...movedDNA, just_moved: true });
    newRows[newTopicIndex] = newTopic;
    setRows(newRows);
  }

  function handleDragEnd(event: DragEndEvent) {
    setActiveItem(null);
    const { active, over } = event;

    if (!over) return; // dropped outside
    if (!active) return;
    // if active and over are the same check if they just moved or simply the same item
    if (active.id === over.id) {
      let oldTopic = rows.find((row) => row.id == active.data.current?.parentId);
      let ActiveDna = oldTopic.dnas.find((dna: any) => dna.id == active.id);
      if (ActiveDna.just_moved) {
        draggingConfig?.onDragEnd?.(active, { id: null, parentId: over.data.current?.parentId });
      }
      return;
    }
    const { parentId: oldParentId, level: activeLevel, id: activeId } = active.data.current || {};
    const { parentId: newParentId, level: overLevel, id: overId } = over.data.current || {};
    // If different levels aren't allowed:
    if (activeLevel < overLevel) {
      notify.error('You cannot drag and drop across levels');
      return;
    }

    if (
      active.data.current?.sortable.containerId !== over.data.current?.sortable.containerId &&
      over.id === active.id
    ) {
      // In this case the old parent id is basically the new topic id
      draggingConfig?.onDragEnd?.(active, { id: null, parentId: oldParentId });
    }

    const newList = [...rows];

    // If same container => reorder within that container
    if (oldParentId === newParentId) {
      if (activeLevel === 0 && overLevel === 0) {
        const oldIndex = newList.findIndex((t) => t.id == activeId);
        const newIndex = newList.findIndex((t) => t.id == overId);
        const [movedItem] = newList.splice(oldIndex, 1);
        newList.splice(newIndex, 0, movedItem);
        let finalOverId = newList[newIndex + 1]?.id || null;
        draggingConfig?.onDragEnd?.(active, { id: finalOverId });
        setRows(newList);
        return;
      }

      // reorder in oldParent’s array
      const parentIndex = newList.findIndex((t) => t.id == oldParentId);
      if (parentIndex === -1) return;

      const parentCopy = { ...newList[parentIndex], dnas: [...newList[parentIndex].dnas] };
      const oldIndex = parentCopy.dnas.findIndex((dna: any) => dna.id == activeId);
      const newIndex = parentCopy.dnas.findIndex((dna: any) => dna.id == overId);

      if (oldIndex === -1 || newIndex === -1) {
        // Possibly no reorder needed if you dropped on something weird
        setRows(newList);

        return;
      }

      // reorder => classic “remove from oldIndex, insert at newIndex”
      const [movedItem] = parentCopy.dnas.splice(oldIndex, 1);
      parentCopy.dnas.splice(newIndex, 0, movedItem);

      newList[parentIndex] = parentCopy;
      let finalOverId = newList[parentIndex]?.dnas[newIndex + 1]?.id || null;

      setRows(newList);
      draggingConfig?.onDragEnd?.(active, { id: finalOverId, parentId: newParentId });

      return;
    } else {
      const newParentIndex = newList.findIndex((t) => t.id == newParentId);
      if (newParentIndex === -1) return;

      const parentCopy = { ...newList[newParentIndex], dnas: [...newList[newParentIndex].dnas] };
      const oldIndex = parentCopy.dnas.findIndex((dna: any) => dna.id == activeId);
      const newIndex = parentCopy.dnas.findIndex((dna: any) => dna.id == overId);

      if (oldIndex === -1 || newIndex === -1) {
        setRows(newList);
        let finalOverId = newList[newParentId]?.dnas[newIndex + 1]?.id || null;
        draggingConfig?.onDragEnd?.(active, { id: finalOverId, parentId: newParentId });

        return;
      }

      // reorder within the new array
      const [movedItem] = parentCopy.dnas.splice(oldIndex, 1);
      parentCopy.dnas.splice(newIndex, 0, movedItem);

      newList[newParentIndex] = parentCopy;
      setRows(newList);

      // THEN call your API
      let finalOverId = newList[newParentId]?.dnas[newIndex + 1]?.id || null;

      draggingConfig?.onDragEnd?.(active, { id: finalOverId, parentId: newParentId });
      return;
    }

    // if if reordering same container without parent
  }
  if (loading && !rows) {
    return (
      <div className="flex items-center justify-center p-4">
        <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary" width={30} />
      </div>
    );
  }

  const topLevelIds = rows.map((row: any) => row.id);

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <div className="dark:border-border dark:border dark:rounded-md overflow-auto relative">
        <Table className="table-fixed w-full text-sm text-start text-foreground">
          {/* TABLE HEADER */}
          <TableHeader className="text-gray-500 dark:text-gray-400 rtl">
            <TableRow>
              {(isMultipleSelect || isDraggable) && (
                <TableHead className="bg-secondary" style={{ width: '20px' }}>
                  {isMultipleSelect ? (
                    <Input
                      className="!rounded-full size-3.5"
                      type="checkbox"
                      checked={selectedRows?.length === rows?.length}
                      onChange={onSelectAll}
                    />
                  ) : (
                    ''
                  )}
                </TableHead>
              )}
              {columns.map((column, index) => (
                <TableHead key={index} className="p-4 bg-secondary rtl:text-right" style={{ width: column.width }}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>

          {/* TABLE BODY */}
          <TableBody>
            {/* Single SortableContext for TOP-LEVEL rows */}
            {rows.length > 0 ? (
              <SortableContext items={topLevelIds} id="top-level" strategy={verticalListSortingStrategy}>
                {renderRows(
                  rows,
                  0, // level
                  '', // parentPath
                  rowKey,
                  columns,
                  isMultipleSelect,
                  isDraggable,
                  isRowSelected,
                  onSelectRow,
                  expandedRows,
                  toggleRow,
                  getChildRows,
                  t
                )}
              </SortableContext>
            ) : (
              <TableRow className="h-[300px] w-full justify-center items-center">
                <TableCell className={'p-4 py-3'} colSpan={columns.length + (isMultipleSelect ? 1 : 0)}>
                  <Icon icon="healthicons:rdt-result-out-stock-outline" width={100} />
                  <h5 className="text-center text-xl text-gray-500 dark:text-gray-400">{t('noDataFound')}</h5>
                  <p className="text-center text-gray-500 dark:text-gray-400 mt-2">
                    {t('yourSearchDidNotMatchAnyResults')}
                  </p>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>

        {loading && (
          <div className="absolute left-0 right-0 bottom-0 top-0 flex items-center justify-center backdrop-blur-sm">
            <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
          </div>
        )}
      </div>

      <DragOverlay>{activeItem ? <DragOverlayRow activeItem={activeItem} /> : null}</DragOverlay>
    </DndContext>
  );
}

export default memo(TableContent);

function DragOverlayRow({ activeItem }: { activeItem: any }) {
  return (
    <div className="bg-background shadow-sm rounded flex gap-2 items-center p-4 w-fit">
      <Icon className="text-lg text-neutral-n70 cursor-grab" icon="ci:drag-vertical" />
      {activeItem?.title || 'Dragging...'}
    </div>
  );
}
