import { useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';
import { Form, TextInput, useForm, useValidate, Icon } from '@/index';
import { useLogin } from '@/modules/auth/apis/queries';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';

export const LoginPage = () => {
  // State
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  // Hooks
  const navigate = useLanguageNavigate();
  const { isRequired } = useValidate();
  const { mutate: doLogin, isPending } = useLogin();
  const { t, i18n } = useTranslation();

  // useGetUser();
  const { form, setFieldValue } = useForm({
    email: '',
    password: '',
  });
  // Functions
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleSubmit = async () => {
    doLogin(form, {
      onSuccess: (data) => {
        const { user } = data;

        if (user.account_status == 'pending') {
          setErrorMessage(t('account.pending'));
          return;
        }
        if (user.account_status == 'blocked') {
          setErrorMessage(t('account.blocked'));
          return;
        }
        if (user.email_verified_at === null) {
          navigate('/auth/verify-email');
        }
      },
      onError: (error: any) => {
        setErrorMessage(t(error.message) || 'Something went wrong');
      },
    });
  };

  const isRTL = i18n.dir() === 'rtl';

  return (
    <div className="bg-gray-50">
      <section className="bg-gray-50 dark:bg-background h-screen">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto h-full lg:py-0">
          {errorMessage && (
            <div className="shadow-sm absolute top-[75px] left-1/2 w-full max-w-[446px] -translate-x-1/2 border border-border p-3 rounded-md text-red-400 flex gap-2 items-center">
              {' '}
              <span className="bg-red-50 p-2 rounded-md">
                <Icon icon="material-symbols:error-circle-rounded-outline-sharp" width={30} />
              </span>{' '}
              {errorMessage}
            </div>
          )}

          <a
            onClick={() => navigate('/')}
            className="flex items-center justify-start mb-6 text-2xl font-semibold   cursor-pointer"
          >
            {t('login.title')}
          </a>
          <div className="w-full bg-card rounded-xl shadow border border-border md:mt-0 mb-20 sm:max-w-md xl:p-0  ">
            <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
              <h1 className="text-xl font-bold leading-tight tracking-tight  md:text-2xl "> {t('login.cardTitle')}</h1>
              <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
                <TextInput
                  name="email"
                  label={t('email')}
                  placeholder={t('email')}
                  disabled={isPending}
                  value={form.email}
                  onChange={setFieldValue('email')}
                  validators={[isRequired()]}
                />

                <div className="flex w-full">
                  <div className="w-full">
                    <TextInput
                      name={'password'}
                      label={t('password')}
                      placeholder={t('password')}
                      type={showPassword ? 'text' : 'password'}
                      disabled={isPending}
                      value={form.password}
                      onChange={setFieldValue('password')}
                      validators={[isRequired()]}
                    />
                  </div>

                  <div className="mt-8" onClick={() => togglePasswordVisibility()}>
                    <Icon
                      className={`${
                        isRTL ? 'mr-3 ' : 'ml-3'
                      } p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400`}
                      width="25"
                      icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                    />
                  </div>
                </div>
                <Button type="submit" disabled={isPending} loading={isPending} className="mt-4 flex gap-2 items-center">
                  <Icon className={`mt-1 ${i18n.dir() === 'rtl' ? 'rotate-180' : ''}`} icon="mdi:send" width={20} />
                  <p>{t('login.cardLogin')}</p>
                </Button>
                <div>
                  <p className=" text-center grid md:block">
                    {t('login.cardRegister')}&nbsp;
                    <span
                      onClick={() => navigate('/auth/signup')}
                      className="cursor-pointer text-primary hover:underline font-medium"
                    >
                      {t('login.cardSignUp')}
                    </span>
                  </p>
                </div>
                <div>
                  <p className=" text-center grid md:block">
                    {t('login.ForgetPassword')}&nbsp;
                    <span
                      onClick={() => navigate('/auth/forget-password')}
                      className="cursor-pointer text-primary hover:underline font-medium"
                    >
                      {t('login.forgetPassword.clickHere')}
                    </span>
                  </p>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
