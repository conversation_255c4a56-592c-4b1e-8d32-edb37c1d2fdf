import { useEffect } from 'react';
import { IDNA } from '../types';
import { Modal, useForm, TextInput, Form, ComboboxInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useUpdateDnaData } from '../apis/queries';
import { useGetSingleMetadata } from '../../dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';

const ChangeDnaData = ({ data, onOpen, onOpenChange }: { data: IDNA; onOpen: any; onOpenChange: any }) => {
  const { mutate: changeData, isPending } = useUpdateDnaData();
  const { data: allmetaData } = useGetSingleMetadata('');
  const bloomTakOptions = allmetaData?.filter((item) => item.type === 'bloom"s_taxonomy') || [];
  const { t, i18n } = useTranslation();

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { form, setFieldValue } = useForm({
    title: data?.title,
    bloom_tax_id: data?.bloom_tax.id,
    learning_objectives: data?.learning_objectives,
    method: 1,
  });

  useEffect(() => {
    if (data) {
      setFieldValue('title')(data.title);
      setFieldValue('id')(data.id);
      setFieldValue('bloom_tax_id')(data.bloom_tax.id);
      setFieldValue('learning_objectives')(data.learning_objectives);
    }
  }, [data]);

  const handleUpdate = () => {
    changeData(
      { id: data?.id, payload: { ...form } },
      {
        onSuccess: () => {
          onOpenChange();
        },
      }
    );
  };

  return (
    <Modal open={onOpen} onOpenChange={onOpenChange} modalHeader={t('editDnaModal.title')} width={500}>
      <Form className="space-y-3" onSubmit={handleUpdate}>
        <TextInput
          name="title"
          label={t('dnaChangeDataDialog.title')}
          placeholder={t('dnaChangeDataDialog.title')}
          value={form.title}
          onChange={setFieldValue('title')}
        />
        <ComboboxInput
          placeholder={t('dnaChangeDataDialog.bloomTax')}
          label={t('dnaChangeDataDialog.bloomtax.label')}
          options={generateEnum(bloomTakOptions, 'id', labelKey)}
          value={form.bloom_tax_id}
          dnaChangeDataDialog
          onChange={setFieldValue('bloom_tax_id')}
        />
        <TextInput
          name="learning_objectives"
          label={t('dnaChangeDataDialog.learningObjective')}
          placeholder={t('dnaChangeDataDialog.learningObjective')}
          value={form.learning_objectives}
          onChange={setFieldValue('learning_objectives')}
        />
        <div className="flex gap-3 pt-5">
          <Button className="min-w-[150px]" loading={isPending} disabled={isPending}>
            {t('submit')}
          </Button>
          <Button type="button" variant={'outline'} onClick={onOpenChange} className="min-w-[100px]">
            {t('activity.cancel')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ChangeDnaData;
