import 'react-toastify/dist/ReactToastify.css';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';
import { useAtomValue } from 'jotai';
import { notVerifiedUserAtom } from '../store';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { useNotify } from '@/hooks';

export const VerifiedEmail = () => {
  // Hooks
  const [isPending, setIsPending] = useState(false);
  const { notify } = useNotify();

  const { t } = useTranslation();
  const navigate = useLanguageNavigate();
  const user = useAtomValue(notVerifiedUserAtom);
  const reSendEmail = async () => {
    const VITE_API_BASE_URL = (import.meta as any).env.VITE_API_BASE_URL;

    try {
      setIsPending(true);
      const token = localStorage.getItem('not_verified_token');
      const response = await fetch(`${VITE_API_BASE_URL}/email/verify/resend`, {
        method: 'POST',
        headers: {
          ...(token && { Authorization: `Bearer ${token}` }),
        },
      });
      notify('Email sent successfully');
      navigate('/auth/login');
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (err: any) {
      console.log(err);
    } finally {
      setIsPending(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen dark:bg-background px-4">
      <div className="bg-card max-w-md w-full shadow-md overflow-hidden rounded-xl border border-border">
        <div className="px-6 py-8 space-y-6 ">
          <h1 className="text-xl text-primary font-medium text-center">{t('verify.email.header')}</h1>
          <p>
            {t('verify.email.description')}
            <span className="font-medium"> {user?.email}</span>
          </p>
          <p className="font-medium">• {t('verify.email.didntGetEmail')}</p>
          <div className="flex justify-center">
            <Button className="min-w-[250px]" loading={isPending} disabled={isPending} onClick={() => reSendEmail()}>
              {t('verify.email.resend')}
            </Button>
          </div>
          <p>
            {t('verify.email.forHelp')},{' '}
            <a className="font-medium text-primary" target="_blank" href="https://forms.gle/r1TU8N15TWAePGvV6">
              {t('clickHere')}
            </a>{' '}
            {t('thankYou')}!
          </p>
        </div>
      </div>
    </div>
  );
};
