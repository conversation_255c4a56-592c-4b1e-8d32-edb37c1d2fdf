export interface IContentNeedGoal {
  id: number;
  title: string;
  content_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface IContentNeedLevel {
  id: number;
  type: string;
  name_en: string;
  name_ar: string;
  description: string | null;
}
export interface IContentNeedRole {
  id: number;
  title: string;
  job_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface IContentNeedSkill {
  id: number;
  title: string;
  description: string;
  type: string;
  level: IContentNeedLevel;
  correlation: string;
  job_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  level_id: number;
}
export interface IContentNeedJob {
  id: number;
  title: string;
  learning_outcome: string | null;
  content_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  roles: IContentNeedRole[];
  skills: IContentNeedSkill[];
}

export interface IContentNeed {
  id: number;
  step: number;
  organization_vision: string;
  file: string | null;
  url: string | null;
  user_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  goals: IContentNeedGoal[];
  jobs: IContentNeedJob[] | null;
}

// step 1 payload and response
export interface IAnalyzeOrganizationVisionPayload {
  organization_vision: string;
  url?: string;
  file?: any;
}
export interface IAnalyzeOrganizationVisionResponse extends Omit<IContentNeed, 'jobs'> {}

// place holder for now
export interface IContentNeeds {
  id: number;
  title: string;
  learning_outcome: string;
  content_id: number;
  deleted_at: null | string;
  created_at: string;
  updated_at: string;
  skills: IContentNeedSkill[]; // Array of skills associated with the content
}
