import { useEffect, useState, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, useNotify, Icon } from '@/index';
import { Editor } from 'src/components/CKEditor';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { IDNA } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useChangeDnaStatusInsideTask, useGetTaskByCode } from '@/modules/app/tasks/apis/queries';
import { Separator } from '@/components/ui/separator';
import AddChecklistDialog from '@/modules/app/components/add-checklist-dialog';
import { useAddChecklist } from '@/apis/tool-checklist/queries';
import MetaData from '../../components/task-meta-data';
import EditDna from '../../components/edit-dna-task';
import NotesDialog from '../../components/notes-dialog';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import Activity from '@/modules/app/components/activity';
import TranslationTab from '@/modules/app/components/localization/translation';
import { getContentStatusStyle } from '@/utils/helpers';
import { ILanguage } from '@/modules/app/dashboard/transaltion/types';
import TranslationContentTabs from '@/modules/app/components/translation-content-tabs';
import WorkflowStatusChecklist from '@/modules/app/components/work-flow-status-checklist';
import SlideshowList from '../../components/slideshow/slideshow-list';
import Tabs, { ITab } from '@/components/tabs';
import UniversalSkeleton from '@/components/universal-skeleton';

const SingleDna = () => {
  const { taskId, dnaId } = useParams();
  const { data, isPending } = useGetTaskByCode(taskId || '');
  const details: IDNA | any = useMemo(() => {
    if (data) {
      const dna = data?.content?.tool_data?.topics?.flatMap((t) => t.dnas).find((d) => d.id === Number(dnaId));
      return dna;
    }
  }, [data]);
  const [open, setOpen] = useState(false);

  const [editDialog, setEditDialog] = useState<any>(false);
  const [notesDialog, setNotesDialog] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [selectedContentLanguage, setSelectedContentLanguage] = useState<ILanguage>();
  const [selectedContent, setSelectedContent] = useState<string>('');
  const [isExpanded, setIsExpanded] = useState<boolean>(() => {
    if (!dnaId) return false;
    const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
    return savedState ? JSON.parse(savedState) : false;
  });

  const { t, i18n } = useTranslation();
  const { notify } = useNotify();
  const navigate = useNavigate();
  const { mutate: changeDnaStatus, isPending: isChangingStatus } = useChangeDnaStatusInsideTask();
  const { mutate: addChecklist, isPending: isPendingChecklist } = useAddChecklist();

  useEffect(() => {
    if (details) {
      const allDnaIds = data?.content?.tool_data?.all_dnas_ids || [];
      setIds(allDnaIds);
      setSelectedContentLanguage(details?.language);
      setSelectedContent(details?.dna_content);
      const index = allDnaIds.findIndex((id: number) => id === Number(dnaId));
      setCurrentIndex(index);
    }
    if (dnaId) {
      const savedState = localStorage.getItem(`dnaContentExpanded-${dnaId}`);
      setIsExpanded(savedState ? JSON.parse(savedState) : false);
    }
    if (details && !details.dna_content) {
      notify.error(
        'No content has been generated for this DNA yet. Please create the content before accessing this page.'
      );
    }
  }, [details, dnaId]);

  useEffect(() => {
    if (dnaId) {
      localStorage.setItem(`dnaContentExpanded-${dnaId}`, JSON.stringify(isExpanded));
    }
  }, [isExpanded, dnaId]);

  const activeTabComponent: Record<string, JSX.Element> = {
    'meta-data': <MetaData details={details as any} />,
    activity: <Activity details={details} />,
    slideshow: <SlideshowList />,
    localization: <TranslationTab details={details} />,
  };

  const theTabs: ITab[] = [
    {
      label: t('dnaSinglePage.tabs.metaData'),
      value: 'meta-data',
      permission: ['dna_sub_data', 'dna_resource_data'],
      component: activeTabComponent['meta-data'],
    },
    {
      label: t('dnaSinglePage.tabs.activity'),
      value: 'activity',
      permission: 'dna_activity_show',
      component: activeTabComponent['activity'],
    },
    {
      label: t('dnaSinglePage.tabs.Localization'),
      value: 'localization',
      permission: 'translation_show',
      component: activeTabComponent['localization'],
    },
    {
      label: t('dnaSinglePage.tabs.slideShow'),
      value: 'slideshow',
      permission: 'dna_slideshow_show',
      component: activeTabComponent['slideshow'],
    },
  ];

  const filteredTabs = theTabs.filter((tab) => {
    if (!tab.permission) return true;
    if (Array.isArray(tab.permission)) {
      return tab.permission.some((permission) =>
        data?.operations.some((operation) => operation.permissions.some((p) => p.name === permission))
      );
    } else {
      return data?.operations.some((operation) => operation.permissions.some((p) => p.name === tab.permission));
    }
  });

  const defaultTab = filteredTabs.length > 0 ? filteredTabs[0].value : 'meta-data';

  const toggleHeight = () => setIsExpanded((prev: any) => !prev);

  const handleApprove = async (checklist: (string | number)[]) => {
    if (data && details) {
      changeDnaStatus(
        {
          code: data?.code,
          payload: {
            course_id: data.content.tool_id,
            dna_status: 'ready for production',
            dna_id: details.id,
            topic_id: details.topic_id,
          },
        },
        {
          onSuccess: () => {
            addChecklist(
              {
                check_list_ids: checklist,
                tool_id: details.id,
                tool_type: 'Dna',
              },
              { onSuccess: () => setOpen(false) }
            );
          },
        }
      );
    }
  };

  const permissionFinder = (
    permissionsName: 'dna_edit' | 'can_reviewed_dna' | 'can_report_dna' | 'can_add_dna_notes'
  ) => {
    return (
      data?.operations.some((operation) => {
        return operation.permissions.some((permission) => permission.name === permissionsName);
      }) || null
    );
  };

  const handleNext = () => {
    if (ids.length > 0) {
      if (currentIndex < ids.length - 1) {
        const nextIndex = currentIndex + 1;
        navigate(`/app/my-tasks/course/${data?.code}/dna/${ids[nextIndex]}`);
        setCurrentIndex(nextIndex);
      }
    } else {
      navigate(`/app/my-tasks/course/${data?.code}/dna/${Number(dnaId) + 1}`);
    }
  };

  const handlePrevious = () => {
    if (ids.length > 0) {
      if (currentIndex > 0) {
        const prevIndex = currentIndex - 1;
        navigate(`/app/my-tasks/course/${data?.code}/dna/${ids[prevIndex]}`);
        setCurrentIndex(prevIndex);
      }
    } else {
      navigate(`/app/my-tasks/course/${data?.code}/dna/${Number(dnaId) - 1}`);
    }
  };

  const canGoNext = ids.length > 0 ? currentIndex < ids.length - 1 : true;
  const canGoPrevious = ids.length > 0 ? currentIndex > 0 : Number(dnaId) > 1;

  return (
    <div className="my-3 space-y-4">
      <ConditionalComponent
        status={details?.dna_status}
        wantedStatus={[StatusClass.DNA.REVIEW.PRODUCTION]}
        operator="not"
      >
        <Alert className="bg-blue-50 dark:bg-background text-indigo-700">
          <AlertTitle className="text-xl font-medium">
            <div className="flex items-center gap-1">
              <Icon className="mt-1 text-indigo-700" icon="mdi:alert-circle" width={18} />
              <p>{t('dnaSinglePage.dnaAlertTitle')}</p>
            </div>
          </AlertTitle>
          <AlertDescription className="text-sm ml-2">• {t('dnaSinglePage.dnaAlert')}</AlertDescription>
        </Alert>
      </ConditionalComponent>
      <Card className="mb-5 p-6">
        <div className="mb-2">
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              {getContentStatusStyle(details?.dna_status)}
              <p className="text-xl capitalize ">{details?.title}</p>
              <p className="text-black-600">{details?.learning_objectives}</p>
              <WorkflowStatusChecklist dna={details} />
            </div>
            <div className="space-y-2">
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  className="flex items-center gap-3 text-sm hover:text-primary dark:hover:text-primary"
                  onClick={handlePrevious}
                  disabled={!canGoPrevious}
                >
                  <Icon
                    icon={i18n.language === 'en' ? 'line-md:chevron-small-left' : 'line-md:chevron-small-right'}
                    className="mt-1"
                    width="20"
                  />
                  <p>{t('dnaSingle.previousDna')}</p>
                </Button>
                <Button
                  variant="ghost"
                  className="flex items-center gap-3 text-sm hover:text-primary dark:hover:text-primary"
                  onClick={handleNext}
                  disabled={!canGoNext}
                >
                  <p>{t('dnaSingle.nextDna')}</p>
                  <Icon
                    icon={i18n.language === 'en' ? 'line-md:chevron-small-right' : 'line-md:chevron-small-left'}
                    className="mt-1"
                    width="20"
                  />
                </Button>
              </div>
              <ConditionalComponent
                status={details?.dna_status}
                wantedStatus={[StatusClass.DNA.REVIEW.APPROVED]}
                operator="not"
              >
                <div className="flex gap-3 justify-end items-center">
                  {permissionFinder('can_reviewed_dna') && details?.dna_status?.phase?.action != 'Production' && (
                    <Button
                      onClick={() => setOpen(true)}
                      className="min-w-[140px]"
                      loading={isChangingStatus}
                      disabled={isChangingStatus || details?.dna_status.action === 'ready_for_production'}
                    >
                      {t('editDnaModal.reviewed')}
                    </Button>
                  )}

                  {open && (
                    <AddChecklistDialog
                      loading={isPendingChecklist || isChangingStatus}
                      open={open}
                      setOpen={setOpen}
                      callback={handleApprove}
                      type="reviewrDna"
                    />
                  )}
                  <ConditionalComponent
                    status={data?.content?.tool_data?.courseStatus}
                    wantedStatus={[StatusClass.COURSE.REVIEW.PRODUCTION]}
                    operator="not"
                  >
                    {permissionFinder('dna_edit') && details?.dna_status?.phase?.action != 'Production' && (
                      <Button
                        variant={'outline'}
                        onClick={() => setEditDialog(true)}
                        disabled={data?.task_status.action === 'ready_for_production'}
                        className="min-w-[140px]"
                      >
                        {t('editDnaModal.title')}
                      </Button>
                    )}
                  </ConditionalComponent>
                  {permissionFinder('can_add_dna_notes') && details?.dna_status?.phase?.action != 'Production' && (
                    <Button className="min-w-[140px]" variant={'outline'} onClick={() => setNotesDialog(true)}>
                      {t('edit.notes')}
                    </Button>
                  )}
                </div>
              </ConditionalComponent>
            </div>
          </div>
          <Separator className="mt-4" />
          <TranslationContentTabs
            Dna={details}
            setSelectedContent={setSelectedContent}
            setSelectedContentLanguage={setSelectedContentLanguage}
          />
          <div className="py-4">
            <div
              className={`
        overflow-y-hidden
        relative
        transition-all
        duration-500
        ease-in-out
        ${isExpanded ? 'max-h-[15000px]' : ' max-h-[350px]'}
        [&_.ck-toolbar]:hidden
      `}
            >
              {isPending ? (
                <UniversalSkeleton template="information" />
              ) : (
                <Editor
                  height={'auto'}
                  editorContent={selectedContent}
                  setEditorContent={setSelectedContent}
                  readOnly
                  language={selectedContentLanguage}
                />
              )}

              {!isExpanded && (
                <div className="absolute bottom-[0px] h-[50px] left-0 right-0 bg-gradient-to-t from-background">
                  <span className="block">&nbsp;&nbsp;&nbsp;&nbsp;</span>
                </div>
              )}
            </div>
            <div className="flex gap-3 justify-center items-center mt-2 text-primary">
              <Icon className="mt-1" icon={isExpanded ? 'ri:arrow-up-s-line' : 'ri:arrow-down-s-line'} width={22} />
              <p
                onClick={toggleHeight}
                className=" cursor-pointer"
                aria-expanded={isExpanded}
                role="button"
                tabIndex={0}
              >
                {isExpanded ? t('dnaSinglePage.hide') : t('dnaSinglePage.expand')}
              </p>
            </div>
          </div>
        </div>
      </Card>
      <Card>
        <div className="p-3">
          <Tabs tabs={filteredTabs} defaultTab={defaultTab} useUrlParams={true} paramName="tab" />
        </div>
      </Card>

      {editDialog && data && <EditDna onOpen={editDialog} onOpenChange={setEditDialog} data={details} course={data} />}
      {notesDialog && data && (
        <NotesDialog isOpen={notesDialog} data={details} tool={data} setIsOpen={setNotesDialog} />
      )}
    </div>
  );
};

export default SingleDna;
