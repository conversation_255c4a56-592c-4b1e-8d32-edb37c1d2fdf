import { Button } from '@/components/ui/button';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useForm, useValidate } from '@/hooks';
import { useGenerateDnaTranslations, useGetDnaTranslations } from '../../dna/apis/queries';
import { ComboboxInput, Form } from '@/components';
import { generateEnum } from '@/utils/helpers';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';

const EmptyLocalization = ({ contentId, viewOnly = false }: { contentId: number | string; viewOnly?: boolean }) => {
  const { t, i18n } = useTranslation();
  const { taskId } = useParams();
  const { isRequired, validateUrlRegex } = useValidate();

  const { form, setFieldValue } = useForm({
    id: contentId,
    language_id: '',
    taskId: taskId,
  });

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const { data } = useGetDnaTranslations(contentId as number);
  const { mutate: generateTranslation, isPending } = useGenerateDnaTranslations();

  const handleSubmit = () => {
    generateTranslation(form);
  };

  useEffect(() => {
    if (taskId) {
      setFieldValue('taskId')(taskId);
    }
    if (contentId) {
      setFieldValue('id')(contentId);
    }
  }, [taskId, contentId]);

  return (
    <div className="flex flex-col justify-center items-center ms-2 min-h-96">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.localiztion.noLocalization')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.localiztion.noLocalizationDescription')}</p>
      </div>
      {!viewOnly && (
        <ProtectedTaskComponent requiredPermissions={'translation_create'}>
          <Form onSubmit={handleSubmit}>
            <div className="flex flex-col gap-6">
              <div className="w-[400px]">
                <ComboboxInput
                  placeholder={t('dnaCreationPage.form.language')}
                  options={generateEnum(data || [], 'id', labelKey)}
                  value={form.language_id}
                  onChange={setFieldValue('language_id')}
                  validators={[isRequired()]}
                />
              </div>
              <div>
                <div className="flex justify-center">
                  <Button
                    loading={isPending}
                    disabled={isPending}
                    className="min-w-[150px] border-primary border-2 flex gap-1 items-center"
                    type="submit"
                  >
                    {t('dnaCreationPage.translate')}
                  </Button>
                </div>
              </div>
            </div>
          </Form>
        </ProtectedTaskComponent>
      )}
    </div>
  );
};

export default EmptyLocalization;
