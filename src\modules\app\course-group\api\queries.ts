import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createCourseGroup,
  deleteCourseGroup,
  deleteCourseInsideCourseGroup,
  getCoursesSimplified,
  updateCourseGroup,
  updateCourseGroupStatus,
} from './endpoints';
import { useNotify } from '@/hooks';
import { useTranslation } from 'react-i18next';

export const useGetCoursesSimplified = () => {
  return useQuery({
    queryKey: ['course-simplified'],
    queryFn: getCoursesSimplified,
  });
};

export const useCreateCourseGroup = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createCourseGroup,
    onSuccess: () => {
      notify.success(t('courseGroup.createCourseGroupSuccess'));
      queryClient.invalidateQueries({ queryKey: ['groups'] });
    },

    onError: () => {
      notify.error(t('courseGroup.createCourseGroupError'));
    },
  });
};

export const useDeleteCourseGroup = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteCourseGroup,
    onSuccess: () => {
      notify.success(t('courseGroup.deleteCourseGroupSuccess'));
      queryClient.invalidateQueries({ queryKey: ['groups'] });
    },
    onError: () => {
      notify.error('Error deleting course group!');
    },
  });
};
export const useDeleteCourseInsideCourseGroup = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteCourseInsideCourseGroup,
    onSuccess: () => {
      notify.success(t('courseGroup.deleteCourseInCourseGroupSuccess'));
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: () => {
      notify.error('Error deleting course!');
    },
  });
};

export const useUpdateCourseGroup = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCourseGroup,
    onSuccess: () => {
      notify.success(t('courseGroup.updateCourseGroupSuccess'));
      queryClient.invalidateQueries({ queryKey: ['groups'] });
    },
    onError: () => {
      notify.error(t('courseGroup.updateCourseGroupError'));
    },
  });
};
export const useUpdateCourseGroupStatus = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateCourseGroupStatus,
    onSuccess: () => {
      notify.success(t('courseGroup.updateCourseGroupSuccess'));
      queryClient.invalidateQueries({ queryKey: ['groups'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: () => {
      notify.error(t('courseGroup.updateCourseGroupError'));
    },
  });
};
