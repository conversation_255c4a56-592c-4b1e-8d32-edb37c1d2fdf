import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  addContentNeedGoal,
  addContentNeedRole,
  addNew<PERSON>ontentNeedJob,
  analyzeGoals,
  analyzeJobsAndRoles,
  analyzeJobsAndSkills,
  analyzeOrganizationVision,
  deleteContentNeed,
  delete<PERSON>ontentNeedGoal,
  deleteContentNeedJob,
  deleteContentNeedRole,
  deleteContentNeedSkill,
  editContentNeedGoal,
  editContent<PERSON>eedJob,
  editContentNeedRole,
  editContentNeedSkill,
  findContentNeedsById,
} from './endpoints';
import { useNotify } from '@/hooks';
import { useSetAtom } from 'jotai';
import { contentId, contentNeedsAtom } from '@/modules/app/tools/store/contnet-need';
import { useTranslation } from 'react-i18next';
// Hook to Approve dna

export const useGetContentNeedsById = (id: string | undefined) => {
  return useQuery({
    queryKey: ['contentNeeds', id],
    queryFn: () => (id ? findContentNeedsById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null
  });
};
export const useAnalyzeOrganizationVision = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const setData = useSetAtom(contentNeedsAtom);
  const setId = useSetAtom(contentId);
  return useMutation({
    mutationFn: analyzeOrganizationVision,
    onSuccess: (data) => {
      notify.success('Organization Vision Analyzed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
      setData({ ...data, jobs: null });
      setId(data.id);
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAnalyzeGoals = () => {
  const { notify } = useNotify();
  const setData = useSetAtom(contentNeedsAtom);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: analyzeGoals,
    onSuccess: (data) => {
      notify.success('Goals Analyzed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
      setData((prev) => {
        return {
          ...prev,
          jobs: data,
        };
      });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAnalyzeJobsAndRoles = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const setData = useSetAtom(contentNeedsAtom);

  return useMutation({
    mutationFn: analyzeJobsAndRoles,
    onSuccess: (data) => {
      notify.success('Jobs and Roles Analyzed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });

      setData((prev) => {
        return {
          ...prev,
          jobs: data,
        };
      });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAnalyzeJobsAndSkills = () => {
  const { notify } = useNotify();
  const setData = useSetAtom(contentNeedsAtom);
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: analyzeJobsAndSkills,
    onSuccess: (data) => {
      notify.success('Jobs and Skills Analyzed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });

      setData((prev) => {
        return {
          ...prev,
          jobs: data,
        };
      });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteContentNeed = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteContentNeed,
    onSuccess: () => {
      notify.success(t('notify.contentDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteContentNeedGoal = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteContentNeedGoal,
    onSuccess: () => {
      notify.success(t('notify.goalDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditContentNeedGoal = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editContentNeedGoal,
    onSuccess: () => {
      notify.success(t('notify.goalDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAddContentNeedGoal = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addContentNeedGoal,
    onSuccess: () => {
      notify.success(t('notify.goalDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to manage Jobs

export const useAddContentNeedJob = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addNewContentNeedJob,
    onSuccess: () => {
      notify.success(t('notify.jobCreated'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteContentNeedJob = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteContentNeedJob,
    onSuccess: () => {
      notify.success(t('notify.jobDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditContentNeedJob = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editContentNeedJob,
    onSuccess: () => {
      notify.success(t('notify.jobDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to manage Roles
export const useAddContentNeedRole = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addContentNeedRole,
    onSuccess: () => {
      notify.success(t('notify.roleCreated'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteContentNeedRole = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteContentNeedRole,
    onSuccess: () => {
      notify.success(t('notify.roleDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditContentNeedRole = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editContentNeedRole,
    onSuccess: () => {
      notify.success(t('notify.roleDeleted'));
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to manage Skills
export const useDeleteContentNeedSkill = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteContentNeedSkill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useEditContentNeedSkill = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editContentNeedSkill,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['contentNeeds'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
