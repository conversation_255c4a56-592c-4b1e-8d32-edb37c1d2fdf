import { Combobox } from '@/components/combo-box';
import { useTranslation } from 'react-i18next';
import React from 'react';
import { Button } from '@/components/ui/button';

const ActivityFilter = ({
  selectedType,
  setSelectedType,
}: {
  selectedType: 'case_study' | 'mcq' | 'fill_in_the_blank' | 'true_false' | null;
  setSelectedType: React.Dispatch<
    React.SetStateAction<'case_study' | 'mcq' | 'fill_in_the_blank' | 'true_false' | null>
  >;
}) => {
  const { t } = useTranslation();
  const options = [
    { value: 'true_false', label: t('activity.filter.true_false') },
    { value: 'case_study', label: t('activity.filter.case_study') },
    { value: 'mcq', label: t('activity.filter.mcq') },
    { value: 'fill_in_the_blank', label: t('activity.filter.fill_in_the_blank') },
  ];
  const handleSelect = (value: string) => {
    if (selectedType === value) {
      setSelectedType(null);
    } else {
      setSelectedType(value as any);
    }
  };
  return (
    <div className="flex gap-2 items-center">
      <Combobox placeholder="Select Activity" options={options} onChange={handleSelect} value={selectedType || ''} />

      {selectedType && (
        <Button variant={'link'} onClick={() => setSelectedType(null)}>
          {t('activity.filter.reset')}
        </Button>
      )}
    </div>
  );
};

export default ActivityFilter;
