import {
  ClassicEditor,
  Essentials,
  Paragraph,
  Bold,
  Italic,
  Autoformat,
  Code,
  Strikethrough,
  Subscript,
  Superscript,
  Underline,
  RemoveFormat,
  Highlight,
  Alignment,
  Image,
  AutoImage,
  ImageCaption,
  ImageInsert,
  ImageResize,
  ImageStyle,
  ImageToolbar,
  ImageUpload,
  SpecialCharacters,
  SpecialCharactersEssentials,
  Table,
  List,
  CodeBlock,
  GeneralHtmlSupport,
  ButtonView,
  BalloonEditor,
  Plugin,
} from 'ckeditor5';
import { DirectionPlugin } from './dir-plugin';

// import { AIAssistant, OpenAITextAdapter } from 'ckeditor5-premium-features';

import { CKEditor } from '@ckeditor/ckeditor5-react';
import { AiAssistantEnums } from '../services/AiAssistantEnums';
import { WProofreader } from '@webspellchecker/wproofreader-ckeditor5';
import { DnaEnums } from '@/index';
import { Icon } from './icon'; // Direct import to avoid circular dependency
import '@webspellchecker/wproofreader-ckeditor5/index.css';
import { marked } from 'marked';
import 'ckeditor5/ckeditor5.css';
import 'ckeditor5-premium-features/ckeditor5-premium-features.css';
import { debounce } from 'lodash';
import { t } from 'i18next';
import { useEffect, useState, lazy, Suspense } from 'react';
import { cn } from '@/lib/utils';

// Lazy load FileManagerDialog to reduce initial bundle size
const FileManagerDialog = lazy(() => import('./file-manager/file-manager-dialog'));
// import MathType from '@wiris/mathtype-ckeditor5/dist/index.js';

const aiIcon = `
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="2 2 25 25">
    <path
      fill="currentColor"
      d="m3.501 6.683l.003.067V9h5.918c.331 0 .649-.132.883-.366L12.19 6.75l-1.883-1.884a1.25 1.25 0 0 0-.884-.366H5.75a2.25 2.25 0 0 0-2.249 2.183M2.004 9.826L2 9.75v-3A3.75 3.75 0 0 1 5.75 3h3.672c.729 0 1.428.29 1.944.805L13.561 6h8.69A3.75 3.75 0 0 1 26 9.75v4.652a7.5 7.5 0 0 0-1.5-1.247V9.75a2.25 2.25 0 0 0-2.25-2.25h-8.69l-2.195 2.195a2.75 2.75 0 0 1-1.944.805H3.504v9.75a2.25 2.25 0 0 0 2.25 2.25h7.87c.234.536.529 1.038.875 1.5H5.755a3.75 3.75 0 0 1-3.75-3.75zM27 19.5a6.5 6.5 0 1 1-13 0a6.5 6.5 0 0 1 13 0m-6-4a.5.5 0 0 0-1 0V19h-3.5a.5.5 0 0 0 0 1H20v3.5a.5.5 0 0 0 1 0V20h3.5a.5.5 0 0 0 0-1H21z"
    />
  </svg>
  `;

class ReplaceImagePlugin extends Plugin {
  init() {
    const editor = this.editor;
    const openImageModal = this.editor.config.get('replaceImage.openImageModal');

    // Register the button
    editor.ui.componentFactory.add('replaceImage', (locale) => {
      const button = new ButtonView(locale);

      button.set({
        label: t('mediaManager'),
        tooltip: true,
        icon: aiIcon,
      });

      button.on('execute', () => {
        if (typeof openImageModal === 'function') {
          openImageModal();
        }
      });

      return button;
    });
  }
}
export const Editor = ({
  editorContent,
  setEditorContent,
  disabled,
  loading,
  height = '300px',
  language,
  type = 'classic',
  setEditor,
  readOnly,
  defaultPrompt,
  ...props
}: {
  editorContent: any;
  setEditorContent?: any;
  disabled?: boolean;
  loading?: boolean;
  height?: string;
  language?: { name_en: string; name_ar: string } | any;
  type?: 'balloon' | 'classic';
  readOnly?: boolean;
  setEditor?: any;
  defaultPrompt?: string;
}) => {
  // states
  const [editorInstance, setEditorInstance] = useState<any>(null);
  const [isFileManagerOpen, setIsFileManagerOpen] = useState<boolean>(false);
  // Hooks
  // Funcrtions
  const openFileManager = () => {
    setIsFileManagerOpen(true);
  };
  // Callback when a new image is selected from the modal.
  const handleImageSelect = (newImageUrl: string) => {
    setIsFileManagerOpen(false);
    if (!editorInstance) return;
    const selection = editorInstance.model.document.selection;
    const selectedElement = selection.getSelectedElement();

    if (selectedElement && selectedElement.name === 'image') {
      editorInstance.model.change((writer: any) => {
        writer.setAttribute('src', newImageUrl, selectedElement);
      });
    } else {
      editorInstance.execute('insertImage', { source: newImageUrl });
    }
  };

  const generateCommands = () => {
    const commandGroups = Object.entries(AiAssistantEnums).map(([groupKey, groupItems]) => {
      const groupId = groupKey.toLowerCase();
      const groupLabel = groupKey.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase());

      const commands = groupItems.map((item: any) => ({
        id: item.id,
        label: item.label || item.id.replace(/([A-Z])/g, ' $1').replace(/^./, (str: any) => str.toUpperCase()),
        prompt: DnaEnums.audience[0].prompt,
      }));

      return { groupId, groupLabel, commands };
    });

    return commandGroups;
  };

  const isArabic = language?.name_en === 'Arabic' || language === 'Arabic translation from English';
  // ready only mode
  useEffect(() => {
    if (editorInstance) {
      // The correct way to set read-only mode in CKEditor 5
      if (readOnly || disabled) {
        editorInstance.enableReadOnlyMode('read-only-mode');
      } else {
        editorInstance.disableReadOnlyMode('read-only-mode');
      }
    }
  }, [editorInstance, readOnly]);
  const editorConfig = {
    plugins: [
      Essentials,
      Paragraph,
      Autoformat,
      List,
      // AIAssistant,
      // OpenAITextAdapter,
      Bold,
      Code,
      CodeBlock,
      Italic,
      Strikethrough,
      Subscript,
      Superscript,
      Underline,
      SpecialCharacters,
      SpecialCharactersEssentials,
      Highlight,
      RemoveFormat,
      Alignment,
      AutoImage,
      Image,
      ImageCaption,
      ImageInsert,
      ImageResize,
      ImageStyle,
      ImageToolbar,
      ImageUpload,
      Table,
      WProofreader,
      DirectionPlugin,
      GeneralHtmlSupport,
      ReplaceImagePlugin,
      // MathType,
    ],
    toolbar: {
      items: [
        // 'aiCommands',
        // 'aiAssistant',
        'bold',
        'italic',
        'underline',
        'strikethrough',
        'removeFormat',
        'SpecialCharacters',
        'highlight',
        'alignment',
        'bulletedList',
        'numberedList',
        'link',
        'replaceImage',
        'insertTable',
        'code',
        'codeBlock',
        'undo',
        'redo',
        'wproofreader',
        // 'ltr',
        // 'rtl',
        'dir:ltr',
        'dir:rtl',
        // 'MathType',
      ],
    },
    image: {
      resizeOptions: [
        {
          name: 'resizeImage:original',
          label: 'Default image width',
          value: null,
        },
        {
          name: 'resizeImage:50',
          label: '50% page width',
          value: '50',
        },
        {
          name: 'resizeImage:75',
          label: '75% page width',
          value: '75',
        },
      ],
      toolbar: [
        'imageTextAlternative',
        'toggleImageCaption',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
        '|',
        'replaceImage',
      ],
      insert: {
        integrations: ['upload', 'url', 'media'], // Add 'upload' here
      },
    },
    replaceImage: {
      openImageModal: openFileManager, // Pass the function here
    },
    wproofreader: {
      serviceId: 'v5vfOvygupvdIHq',
      srcUrl: 'https://svc.webspellchecker.net/spellcheck31/wscbundle/wscbundle.js',
    },
    codeBlock: {
      indentSequence: '  ',
    },
    language: {
      content: isArabic ? 'ar' : 'en',
    },
    // mathTypeParameters: {
    //   editorParameters: {
    //     language: isArabic ? 'ar' : 'en',
    //   },
    // },
    licenseKey:
      'eyJhbGciOiJFUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************.TVkd9z6Z78OCyKANrbN8e6GETL8HHac46ebWYX_GtYLhhLDyOFemW0KUf5arDNS3Pqp5VWsJ5poA-akC2QhIIw',
    ai: {
      openAI: {
        requestHeaders: {
          Authorization: 'Bearer sk-proj-6seARgSKxGU4CmXvRr7wT3BlbkFJ7jpGbghZZEJNUYtUd2Wd',
        },
      },
      aiAssistant: {
        commands: generateCommands(),
      },
    },
    htmlSupport: {
      allow: [
        {
          name: /.*/,
          attributes: true,
          classes: true,
          styles: true,
        },
      ],
      disallow: [
        {
          attributes: [
            { key: /^on(.*)/i, value: true },
            {
              key: /.*/,
              value: /(\b)(on\S+)(\s*)=|javascript:|(<\s*)(\/*)script/i,
            },
            { key: /.*/, value: /data:(?!image\/(png|jpeg|gif|webp))/i },
          ],
        },
        { name: 'script' },
      ],
    },
  } as any;

  const handleEditorChange = debounce((event: any, editor: any) => {
    const data = editor.getData();
    const cleanedData = data.replace(
      /<pre([^>]*)>([\s\S]*?)<\/pre>/g,
      (match: string, attributes: string, content: string) => {
        const cleanContent = content.replace(/\n\s*\n/g, '\n').replace(/^\s+|\s+$/g, '');
        return `<pre${attributes}>${cleanContent}</pre>`;
      }
    );
    setEditorContent(cleanedData);
  }, 50);

  return (
    <div className={`${readOnly ? '' : 'border-2 border-gray-200 rounded-md shadow-md'}`}>
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
        </div>
      ) : (
        <>
          <div
            className={cn(
              'prose prose-neutral leading-normal  w-full max-w-full',
              readOnly && 'read-only-ckeditor',
              disabled && 'disabled-ckeditor'
            )}
          >
            <CKEditor
              key={`ckeditor-${isArabic}`}
              editor={type === 'classic' ? ClassicEditor : BalloonEditor}
              data={marked.parse(editorContent) as any}
              config={editorConfig}
              disabled={disabled}
              onReady={(editor) => {
                editor.editing.view.change((writer: any) => {
                  writer.setStyle('height', height, editor?.editing?.view?.document?.getRoot() as any);
                });
                setEditorInstance(editor);
                setEditor(editor);
              }}
              onChange={handleEditorChange}
              {...props}
            />
          </div>

          {isFileManagerOpen && (
            <Suspense fallback={<div>Loading...</div>}>
              <FileManagerDialog
                open={isFileManagerOpen}
                onOpenChange={setIsFileManagerOpen}
                handleImageSelect={handleImageSelect}
                defaultPrompt={defaultPrompt}
              />
            </Suspense>
          )}
        </>
      )}
    </div>
  );
};
