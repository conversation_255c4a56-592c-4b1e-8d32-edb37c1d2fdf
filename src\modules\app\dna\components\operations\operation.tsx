import React, { useMemo, useState } from 'react';
import { useGetDnaContributors, useGetDnaLogs } from '../../apis/queries';
import { useParams } from 'react-router-dom';
import { Icon } from '@/components';
import { dateAndTimeFormat, formatUserByNameAndEmail } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { IContributor, ILogs } from '../../types';
import UniversalSkeleton from '@/components/universal-skeleton';
import LogsData from './logs';
import LanguageLink from '@/components/language-link';
import { Badge } from '@/components/ui/badge';
import { TableContentBody, Table, TableContent } from '@/components/theTable';
import { ProtectedComponent } from '@/index';
import { Button } from '@/components/ui/button';

const Operations = () => {
  const { t, i18n } = useTranslation();
  const [logsData, selogsData] = useState([]);
  const [showLogs, setShowLogs] = useState(false);

  const { dnaId } = useParams();
  const { data: logs, isPending: isPendingLogs } = useGetDnaLogs('Dna', Number(dnaId));
  const [contributorsAccordion] = useState<string>('contributors');
  const { data: contributors, isPending: isPendingContributors } = useGetDnaContributors(
    dnaId as string,
    !!contributorsAccordion
  );

  console.log(logs, 'data');

  const contributorsColumns: ITableColumn<IContributor>[] = useMemo(() => {
    return [
      {
        accessorKey: 'name',
        header: t('dnaConentPgae.table.name'),
        cell: ({ row }) => (
          <LanguageLink to={`app/users/${row.id}`} className="flex flex-col gap-1 text-primary hover:underline">
            <p>{row.name}</p>
            <p>{row.email}</p>
          </LanguageLink>
        ),
      },
      {
        accessorKey: 'roles',
        header: t('dnaConentPgae.table.roles'),
        cell: ({ row }) => <div className="flex gap-2">{t('roles.' + row.roles)}</div>,
      },
      {
        accessorKey: 'last_interaction',
        header: t('dnaConentPgae.table.dateTime'),
        cell: ({ row }) => dateAndTimeFormat(row.last_interaction, i18n.language),
      },
      {
        accessorKey: 'contribute_in',
        header: t('dnaConentPgae.table.contribute_in'),
        cell: ({ row }) => (
          <div className="flex gap-2">
            {row.contribute_in.map((contribute, idx) => (
              <Badge key={contribute + idx} className="p-1.5 px-4" variant={'outline'}>
                {contribute}
              </Badge>
            ))}
          </div>
        ),
      },
    ];
  }, [t]);

  const operationsColumns: ITableColumn<ILogs>[] = useMemo(() => {
    return [
      {
        accessorKey: 'user',
        header: t('userPage.table.name'),
        cell: ({ row }) => formatUserByNameAndEmail(row?.user),
      },
      {
        accessorKey: 'created_at',
        header: t('dnaConentPgae.table.dateTime'),
        cell: ({ row }) => dateAndTimeFormat(row.created_at, i18n.language),
      },
      {
        accessorKey: 'action',
        header: t('systemLogs.table.actions'),
        cell: ({ row }) => (
          <Button
            variant="link"
            size="sm"
            className="text-primary hover:underline"
            onClick={() => {
              setShowLogs(true);
              selogsData([row.logs] as any);
            }}
          >
            {row.action}
          </Button>
        ),
      },
    ];
  }, [t]);

  if (isPendingLogs || isPendingContributors) {
    return <UniversalSkeleton preset="articleList" rows={2} />;
  }

  return (
    <div className="space-y-5">
      <ProtectedComponent requiredPermissions={'for_administration'}>
        <div>
          <h1 className="mb-3 text-lg capitalize text-black-600">{t('contributors')}</h1>
          <Table rows={contributors || []} columns={contributorsColumns} loading={isPendingContributors}>
            <TableContent>
              <TableContentBody />
            </TableContent>
          </Table>
        </div>
      </ProtectedComponent>
      <div>
        <h1 className="mb-3 text-lg capitalize text-black-600">{t('dnaSinglePage.tabs.logs.title')}</h1>
        <Table rows={logs || []} columns={operationsColumns} loading={isPendingLogs}>
          <TableContent>
            <TableContentBody />
          </TableContent>
        </Table>
      </div>

      {showLogs && <LogsData isOpen={showLogs} onOpenChange={setShowLogs} data={logsData} />}
    </div>
  );
};

export default Operations;
