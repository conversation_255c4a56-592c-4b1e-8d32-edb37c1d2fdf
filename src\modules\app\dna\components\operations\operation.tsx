import React, { useMemo, useState } from 'react';
import { useGetDnaContributors, useGetDnaLogs } from '../../apis/queries';
import { useParams } from 'react-router-dom';
import { Icon } from '@/components';
import { Separator } from '@/components/ui/separator';
import { dateAndTimeFormat, formatDateByHour, formatDateByYear } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { IContributor, ILogs } from '../../types';
import UniversalSkeleton from '@/components/universal-skeleton';
import LogsData from './logs';
import LanguageLink from '@/components/language-link';
import { Badge } from '@/components/ui/badge';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { TableContentBody, Table, TableContent } from '@/components/theTable';
import { ProtectedComponent } from '@/index';

const Operations = () => {
  const { t, i18n } = useTranslation();
  const [logsData, selogsData] = useState([]);
  const [showLogs, setShowLogs] = useState(false);

  const { dnaId } = useParams();
  const { data, isPending } = useGetDnaLogs('Dna', Number(dnaId));
  const [contributorsAccordion, setContributorsAccordion] = useState<string>('contributors');
  const { data: contributors, isPending: isPendingContributors } = useGetDnaContributors(
    dnaId as string,
    !!contributorsAccordion
  );

  const columns: ITableColumn<IContributor>[] = useMemo(() => {
    return [
      {
        accessorKey: 'name',
        header: t('dnaConentPgae.table.name'),
        cell: ({ row }) => (
          <LanguageLink to={`app/users/${row.id}`} className="flex flex-col gap-1 text-primary hover:underline">
            <p>{row.name}</p>
            <p>{row.email}</p>
          </LanguageLink>
        ),
      },
      {
        accessorKey: 'roles',
        header: t('dnaConentPgae.table.roles'),
        cell: ({ row }) => <div className="flex gap-2">{t('roles.' + row.roles)}</div>,
      },
      {
        accessorKey: 'last_interaction',
        header: t('dnaConentPgae.table.dateTime'),
        cell: ({ row }) => dateAndTimeFormat(row.last_interaction, i18n.language),
      },
      {
        accessorKey: 'contribute_in',
        header: t('dnaConentPgae.table.contribute_in'),
        cell: ({ row }) => (
          <div className="flex gap-2">
            {row.contribute_in.map((contribute, idx) => (
              <Badge key={contribute + idx} className="p-1.5 px-4" variant={'outline'}>
                {contribute}
              </Badge>
            ))}
          </div>
        ),
      },
    ];
  }, [t]);

  if (isPending) {
    return <UniversalSkeleton preset="articleList" rows={2} />;
  }
  const handleLogTitle = (item: ILogs) => {
    const userName = (
      <span className="underline font-medium">
        {item?.user?.name || 'By System'} ({item?.user?.email || 'By System'})
      </span>
    );
    const edit = <span className="font-medium">{t('edit')}: </span>;
    switch (item.action) {
      case 'created':
        return (
          <p className="flex gap-1">
            <span className="font-medium">{t('Created')}</span> {t('By')}
            {userName}
          </p>
        );

      case 'updated':
        if (item.logs['dna status'] && item.logs['dna content']) {
          return (
            <p className="flex gap-1">
              {edit}
              {t('Updated by')}
              {userName}
            </p>
          );
        } else if (item.logs['dna status']) {
          return (
            <p className="flex gap-1">
              {edit}
              {t(item.logs['dna status'].new)} {t('By')}
              {userName}
            </p>
          );
        }
        return (
          <p className="flex gap-1">
            {edit}
            {t('Updated by')}
            {userName}
          </p>
        );
      default:
        return (
          <p className="flex gap-1">
            {edit}
            {t('Updated by')}
            {userName}
          </p>
        );
    }
  };
  return (
    <div className="space-y-5">
      <ProtectedComponent requiredPermissions={'for_administration'}>
        <div className="col-span-3">
          <Accordion
            value={contributorsAccordion}
            collapsible
            onValueChange={setContributorsAccordion}
            type="single"
            className="w-full"
          >
            <AccordionItem value="contributors">
              <AccordionTrigger>
                <h5 className="mb-2 text-sm capitalize text-black-600">{t('contributors')}</h5>
              </AccordionTrigger>
              <AccordionContent>
                <Table rows={contributors || []} columns={columns} loading={isPending}>
                  <TableContent>
                    <TableContentBody />
                  </TableContent>
                </Table>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </ProtectedComponent>
      {data?.map((item, index) => (
        <div className="flex items-center gap-4">
          <div className="flex flex-col items-center">
            <Icon icon="uil:calender" className="bg-[#E1EFFE] h-5 w-5 p-3 rounded-full text-primary" />
            <Separator orientation="vertical" className="h-6" />
          </div>
          <div className="space-y-1">
            {handleLogTitle(item)}
            <div className="flex gap-1 text-gray-400 text-sm">
              <p>{formatDateByYear(item.created_at, i18n.language)} -</p>
              <p>{formatDateByHour(item.created_at, i18n.language)}</p>
            </div>
            <span
              className="text-primary cursor-pointer text-sm hover:underline"
              onClick={() => {
                setShowLogs(true);
                selogsData([item.logs] as any);
              }}
            >
              {t('dnaSinglePage.tabs.logs.seeChanges')}
            </span>
          </div>
          {index === 0 && (
            <div className="ml-5 self-start p-1 bg-[#E1EFFE] rounded-md text-[#1E429F] text-sm">{t('Newest')}</div>
          )}
        </div>
      ))}

      {showLogs && <LogsData isOpen={showLogs} onOpenChange={setShowLogs} data={logsData} />}
    </div>
  );
};

export default Operations;
