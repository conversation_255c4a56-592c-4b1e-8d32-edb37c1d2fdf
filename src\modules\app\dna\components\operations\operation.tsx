import React, { useState } from 'react';
import { useGetDnaLogs } from '../../apis/queries';
import { useParams } from 'react-router-dom';
import { Icon } from '@/components';
import { Separator } from '@/components/ui/separator';
import { formatDateByHour, formatDateByYear } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { ILogs } from '../../types';
import LogsData from '@/modules/app/dashboard/system-logs/components/logs';
import UniversalSkeleton from '@/components/universal-skeleton';

const Operations = () => {
  const { t, i18n } = useTranslation();
  const [logsData, selogsData] = useState([]);
  const [showLogs, setShowLogs] = useState(false);

  const { dnaId } = useParams();
  const { data, isPending } = useGetDnaLogs('Dna', Number(dnaId));

  if (isPending) {
    return <UniversalSkeleton preset="articleList" rows={2} />;
  }

  // if (isLoading) {
  //   return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />;
  // }

  const handleLogTitle = (item: ILogs) => {
    const userName = (
      <span className="underline font-medium">
        {item?.user?.name || 'By System'} ({item?.user?.email || 'By System'})
      </span>
    );
    const edit = <span className="font-medium">{t('edit')}: </span>;
    switch (item.action) {
      case 'created':
        return (
          <p className="flex gap-1">
            <span className="font-medium">{t('Created')}</span> {t('By')}
            {userName}
          </p>
        );

      case 'updated':
        if (item.logs['dna status'] && item.logs['dna content']) {
          return (
            <p className="flex gap-1">
              {edit}
              {t('Updated by')}
              {userName}
            </p>
          );
        } else if (item.logs['dna status']) {
          return (
            <p className="flex gap-1">
              {edit}
              {t(item.logs['dna status'].new)} {t('By')}
              {userName}
            </p>
          );
        }
        return (
          <p className="flex gap-1">
            {edit}
            {t('Updated by')}
            {userName}
          </p>
        );
      default:
        return (
          <p className="flex gap-1">
            {edit}
            {t('Updated by')}
            {userName}
          </p>
        );
    }
  };
  return (
    <div className="space-y-5">
      {data?.map((item, index) => (
        <div className="flex items-center gap-4">
          <div className="flex flex-col items-center">
            <Icon icon="uil:calender" className="bg-[#E1EFFE] h-5 w-5 p-3 rounded-full text-primary" />
            <Separator orientation="vertical" className="h-6" />
          </div>
          <div className="space-y-1">
            {handleLogTitle(item)}
            <div className="flex gap-1 text-gray-400 text-sm">
              <p>{formatDateByYear(item.created_at, i18n.language)} -</p>
              <p>{formatDateByHour(item.created_at, i18n.language)}</p>
            </div>
            <span
              className="text-primary cursor-pointer text-sm hover:underline"
              onClick={() => {
                setShowLogs(true);
                selogsData([item.logs] as any);
              }}
            >
              {t('dnaSinglePage.tabs.logs.seeChanges')}
            </span>
          </div>
          {index === 0 && (
            <div className="ml-5 self-start p-1 bg-[#E1EFFE] rounded-md text-[#1E429F] text-sm">{t('Newest')}</div>
          )}
        </div>
      ))}
      {showLogs && <LogsData isOpen={showLogs} onOpenChange={setShowLogs} logs={logsData} />}
    </div>
  );
};

export default Operations;
