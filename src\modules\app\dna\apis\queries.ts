import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  generateDnaSlideshow,
  updateDnaStatus,
  getDnaById,
  deleteDnas,
  reformatDna,
  getDnaTranslations,
  generateDnaTranslations,
  updateDnaBloomTax,
  deleteTransaltion,
  updateTransaltion,
  generateDnaData,
  uploadDnaFile,
  deleteDna,
  getDnaStatistics,
  generateMultiActivite,
  generateDnaContent,
  changeDnaStatus,
  approveOrFeedbackDna,
  saveDNA,
  updateTransaltionStatus,
  getDnaTitleById,
  reorderDna,
  updateSlideContent,
  regenerateSlide,
  changeSlideTemplate,
  reorderSlide,
  generateVoiceOverSample,
  updateVoiceOverTextAndAudio,
  getDnaLogs,
  getDnaContributors,
  reGenerateDnaSlideshow,
  getSlideshowPlay,
  generateMagicDna,
  confirmDnaGeneratedByAiMagic,
  generateArticulateAnalysis,
  getArticulateAnalysis,
  updateAnalysis,
} from './endpoints';
import { useNotify } from '@/hooks';
import { IDnaStatus } from '../types';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';

// Hook to Approve dna
export const useUpdateDnaStatus = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateDnaStatus,
    onSuccess: () => {
      notify.success('Dna Status Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useChangeDnaStatus = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: changeDnaStatus,
    onSuccess: () => {
      notify.success('Dna Status Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dna-logs'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGenarateDnaData = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: generateDnaData,
    onSuccess: () => {
      notify.success(t('notify.dnaCreated'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGenarateDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: generateDnaContent,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      notify.success(t('notify.dnaCreated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUploadDnaFile = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: uploadDnaFile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      notify.success(t('notify.dnaCreated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: deleteDna,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateDnaData = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateDnaBloomTax,
    onSuccess: () => {
      notify.success(t('dnaChangeStatus.success'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dna-title'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['dna-logs'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to Report dna
export const useReportDna = () => {
  const { notify } = useNotify();

  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { id: string; status: IDnaStatus; report_reason: string }) =>
      updateDnaStatus({ ...payload, status: 'reported' }),
    onSuccess: () => {
      notify.success('Dna Reported Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
    },
    onError: (error: any) => {
      notify.success('Dna Reported Successfully!');
    },
  });
};

export const useDeleteDnas = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteDnas,
    onSuccess: () => {
      notify.success('DNAs Deleted Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to get dna by ID
export const useGetDnaById = (id: number | string | undefined) => {
  return useQuery({
    queryKey: ['dna', id],
    queryFn: () => getDnaById(id),
    enabled: !!id,
  });
};

export const useGetDnaTitleById = (id: number | undefined) => {
  return useQuery({
    queryKey: ['dna-title', id],
    queryFn: () => getDnaTitleById(id),
    enabled: !!id,
  });
};

export const useGetDnaStatistics = () => {
  return useQuery({
    queryKey: ['dna Statistics'],
    queryFn: () => getDnaStatistics(),
  });
};

export const useGetDnaLogs = (model: string, id: number) => {
  return useQuery({
    queryKey: ['dna-logs', id],
    queryFn: () => getDnaLogs(model, id),
  });
};

export const useMultiActivite = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateMultiActivite,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['dna'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useReformatDna = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: reformatDna,
    onSuccess: () => {
      // notify.success('Dna Reformatted Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGetDnaTranslations = (id: number) => {
  return useQuery({
    queryKey: ['dna-translations', id],
    queryFn: () => getDnaTranslations(id),
    enabled: !!id,
  });
};

export const useGenerateDnaTranslations = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateDnaTranslations,
    onSuccess: () => {
      notify.success(t('notifications.dnaTranslationsGenerated'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

export const useSaveDNA = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: saveDNA,

    onSuccess: () => {
      notify.success('Dna Saved Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['topic'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteTransaltion = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteTransaltion,
    onSuccess: () => {
      notify.success('Transaltion Deleted Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateTransaltion = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateTransaltion,
    onSuccess: () => {
      notify.success('Transaltion updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateTransaltionStatus = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateTransaltionStatus,
    onSuccess: () => {
      notify.success('Transaltion updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useGenerateMagicDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateMagicDna,
    onSuccess: () => {
      notify.success(t('notify.magicDnaGenerated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useConfirmDnaGeneratedByAiMagic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: confirmDnaGeneratedByAiMagic,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('notify.magicDnaConfirmed'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useApproveOrFeedbackDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveOrFeedbackDna,
    onSuccess: () => {
      notify.success('Dna Approved Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useReorderDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: reorderDna,
    onSuccess: () => {
      notify.success(t('notifications.dnaReordered'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGetDnaContributors = (id: string, shouldRun: boolean) => {
  return useQuery({
    queryKey: ['dna-contributors', id],
    queryFn: () => getDnaContributors(id),
    enabled: !!id && shouldRun,
  });
};

export const useGenerateArticulateAnalysis = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateArticulateAnalysis,
    onSuccess: () => {
      notify.success(t('notify.articulateAnalysisGenerated'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['dna-articulate-analysis'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGetDnaArticulateAnalysis = (id: string | number | undefined) => {
  return useQuery({
    queryKey: ['dna-articulate-analysis', id],
    queryFn: () => getArticulateAnalysis(id),
    enabled: !!id,
  });
};

// slideshow  ----- slideshow ------------ slideshow -------- slideshow ---------- slideshow ---------- slideshow

export const useGetSlideshowPlay = (id: string | number) => {
  return useQuery({
    queryKey: ['slideshow-play', id],
    queryFn: () => getSlideshowPlay(id),
    enabled: !!id,
  });
};

export const useReorderSlide = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reorderSlide,
    onSuccess: (_, { dnaId, slideshow_id, slide_id, next_slide_id }) => {
      notify.success('Slide Reordered Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGenerateVoiceOverSample = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateVoiceOverSample,
    onSuccess: (_) => {
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      notify.success('Voice Over Sample Generated Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateVoiceOverTextAndAudio = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateVoiceOverTextAndAudio,
    onSuccess: (_, { dnaId }) => {
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['slideshow-play'] });
      notify.success('Voice Over Generated Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Hook to Generate Slideshow
export const useGenerateSlideshow = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateDnaSlideshow,
    onSuccess: () => {
      notify.success('Slideshow Generated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useReGenerateDnaSlideshow = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reGenerateDnaSlideshow,
    onSuccess: () => {
      notify.success('Slideshow reGenerated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// hook to update single slide content
export const useUpdateSlideContent = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateSlideContent,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Content Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useRegenerateSlide = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: regenerateSlide,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Regenerated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useChangeSlideTemplate = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: changeSlideTemplate,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Template Changed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateAnalysis = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateAnalysis,
    onSuccess: () => {
      notify.success('Analysis Changed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna-articulate-analysis'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
