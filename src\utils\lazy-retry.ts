/**
 * Utility for creating lazy-loaded components with retry functionality
 * Helps handle "Failed to fetch dynamically imported module" errors
 */

interface RetryOptions {
  maxRetries?: number;
  delay?: number;
  exponentialBackoff?: boolean;
}

/**
 * Creates a retry wrapper for dynamic imports
 * @param importFn - The dynamic import function
 * @param options - Retry configuration options
 * @returns Promise that resolves to the imported module
 */
export const retryImport = async <T>(
  importFn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> => {
  const { maxRetries = 3, delay = 1000, exponentialBackoff = true } = options;
  
  let lastError: Error;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await importFn();
    } catch (error) {
      lastError = error as Error;
      
      // Check if this is a chunk loading error
      const isChunkError = 
        error instanceof Error && (
          error.name === 'ChunkLoadError' ||
          error.message.includes('Failed to fetch dynamically imported module') ||
          error.message.includes('Loading chunk') ||
          error.message.includes('ChunkLoadError')
        );
      
      // If it's not a chunk error, don't retry
      if (!isChunkError) {
        throw error;
      }
      
      // If this was the last attempt, throw the error
      if (attempt === maxRetries) {
        console.error(`Failed to load module after ${maxRetries + 1} attempts:`, error);
        throw error;
      }
      
      // Calculate delay for next attempt
      const currentDelay = exponentialBackoff 
        ? delay * Math.pow(2, attempt)
        : delay;
      
      console.warn(`Import attempt ${attempt + 1} failed, retrying in ${currentDelay}ms...`, error);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, currentDelay));
    }
  }
  
  throw lastError;
};

/**
 * Creates a lazy component with retry functionality
 * @param importFn - The dynamic import function
 * @param options - Retry configuration options
 * @returns React lazy component
 */
export const lazyWithRetry = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options?: RetryOptions
) => {
  return React.lazy(() => retryImport(importFn, options));
};

/**
 * Creates a lazy component with retry and custom module extraction
 * @param importFn - The dynamic import function
 * @param extractComponent - Function to extract the component from the module
 * @param options - Retry configuration options
 * @returns React lazy component
 */
export const lazyWithRetryAndExtract = <T extends React.ComponentType<any>, M = any>(
  importFn: () => Promise<M>,
  extractComponent: (module: M) => T,
  options?: RetryOptions
) => {
  return React.lazy(async () => {
    const module = await retryImport(importFn, options);
    return { default: extractComponent(module) };
  });
};

/**
 * Preloads a module to warm up the cache
 * @param importFn - The dynamic import function
 * @param options - Retry configuration options
 */
export const preloadModule = async <T>(
  importFn: () => Promise<T>,
  options?: RetryOptions
): Promise<void> => {
  try {
    await retryImport(importFn, options);
  } catch (error) {
    console.warn('Failed to preload module:', error);
  }
};

// Re-export React for convenience
import React from 'react';
