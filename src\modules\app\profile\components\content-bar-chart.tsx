'use client';
import { TrendingUp } from 'lucide-react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ist, <PERSON><PERSON><PERSON><PERSON> } from 'recharts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { IDashboardData } from '@/modules/auth/types';
import { useTranslation } from 'react-i18next';

export const ContentBarChart = ({ data }: { data: IDashboardData }) => {
  const { t } = useTranslation();
  const chartData = [
    { type: t('barChart.Open'), count: data.open_tasks_count },
    { type: t('barChart.in_Progress'), count: data.in_progress_tasks_count },
    { type: t('barChart.Completed'), count: data.completed_tasks_count },
    { type: t('barChart.Hold'), count: data.hold_tasks_count },
    { type: t('barChart.Declined'), count: data.declined_tasks_count },
    { type: t('barChart.Overdue'), count: data.overdue_tasks_count },
    { type: t('barChart.Overdue_No_Response'), count: data.overdue_no_response_tasks_count },
  ];

  const chartConfig = {
    count: {
      label: t('breadcrumb.myContentPage.my-tasks'),
      color: 'hsl(280, 55%, 48%)',
    },
  } satisfies ChartConfig;
  return (
    <Card>
      <CardHeader className="items-center">
        <CardTitle className="font-medium">{t('barChart.title')}</CardTitle>
        <CardDescription>
          {t('barChart.total')} - {data.tasks_count} {t('barChart.tasks.singular')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer config={chartConfig} className="xl:h-[220px] xl:w-[700px]">
          <BarChart
            accessibilityLayer
            data={chartData}
            margin={{
              top: 20,
            }}
          >
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey="type"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => value.slice(0, 15)}
            />
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
            <Bar dataKey="count" fill="var(--color-count)" radius={8} maxBarSize={40}>
              <LabelList position="top" offset={12} className="fill-foreground" fontSize={12} />
            </Bar>
          </BarChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col t gap-2 text-sm items-center">
        <div className="flex gap-2 font-medium leading-none">
          {t('barChart.footer.title')} <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">{t('barChart.footer.description')}</div>
      </CardFooter>
    </Card>
  );
};
