import { lazy } from 'react';

const StatisiticsDashboardMainLayout = lazy(() =>
  import('./layout').then((module) => ({ default: module.StatisiticsDashboardMainLayout }))
);
const Index = lazy(() => import('./pages'));

export default [
  {
    path: 'statistics-dashboard',
    element: <StatisiticsDashboardMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.termsPage.statistics-dashboard',
        title: 'breadcrumb.termsPage.statistics-dashboard',
      };
    },
    children: [
      // Default
      {
        path: '',
        element: <Index />,
      },
    ],
  },
];
