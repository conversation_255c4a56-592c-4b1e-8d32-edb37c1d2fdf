import { IStatus } from '../dashboard/modules-status/types';
import { ISystemStatus } from '@/apis/random/types';
import { IGenerateActivityPayload, ITask } from '../tasks/types';

// Interface for DNA within a topic
export interface ICourseTopicDNA {
  id: number;
  audience: string;
  audio_length: string;
  bloom_tax: IStatus;
  content_style: string | null;
  file_path: string;
  intro_type: string;
  language: IMetadata;
  long_text: string;
  model: string;
  dna: string;
  status: string;
  old_dna: string;
  other_subject: string;
  subject: string;
  title: string;
  txt: string;
  url: string;
  version: string;
  is_approved: number;
  is_reported: number;
  learning_objectives: string;
  created_at: string;
  updated_at: string;
  word_count: number | null;
  audio_file_path: string | null;
  topic_id: number;
  is_deleted: number;
  dna_id: number | null;
  user_id: number;
  description: string | null;
  resources: string | null;
  reporting_reason: string | null;
  approved_by: string | null;
  audio_generated_at: string | null;
  audio_generated_by: string | null;
  approved_at: string | null;
  deleted_at: string | null;
  user: User;
  dna_content: string;
  dna_status: IStatus;
  course_status: IStatus;
  bloom_tax_id: number;
  reviewer_notes: string;
  content_translations: any;
  topic: ICourseTopic;
  activities: IGenerateActivityPayload;
}
interface BloomTax {
  id: number;
  type: string;
  name_en: string;
  name_ar: string;
  description: string;
}

// Interface for Topic within a course
export interface ICourseTopic {
  id: number;
  created_at: string;
  updated_at: string;
  title: string;
  course_id: number;
  multi_dnas_generation_status: string;
  user_id: number;
  status: string;
  subject: string;
  audience: string;
  model: string;
  other_subject: string;
  language: string;
  bloom_tax: BloomTax;
  long_text: string;
  file_path: string;
  url: string;
  txt: string;
  version: string;
  deleted_at: string | null;
  learning_objectives: string;
  dnas: ICourseTopicDNA[];
  user: User;
  topic_status: IStatus;
  activities: IGenerateActivityPayload;
}

// Interface for User
export interface User {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  account_status: string;
  created_at: string;
  updated_at: string;
}

// Interface for the main Course data
export interface ICourse {
  id: string;
  title: string;
  info: string;
  has_tasks: boolean;
  subject_id: string;
  difficulty_level_id: number;
  learning_strategy_id: number;
  learning_type_id: number;
  learning_type: any;
  learning_strategy: any;
  audience_id: string;
  language_id: string;
  subject: IMetadata;
  audience: IMetadata;
  language: IMetadata;
  difficultyLevel: IMetadata;
  level: string;
  status: string;
  user_id: number;
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  topics: ICourseTopic[];
  user: User;
  dna_status: IStatus;
  courseStatus: IStatus;
  creditHours: IMetadata;
  learningStrategy: IMetadata;
  all_dnas_ids: number[];
  prompt: string;
  model: string;
  notes_count: number | string;
  tasks: {
    can_create_new_task: boolean;
    can_assign_child_task_to_parent: boolean;
    data: ITask[];
  };
  can_reopen_task: boolean;
  can_update_task: boolean;
}

// Interface for the entire API response

export interface ICreateCourseAI {
  title: string;
  info: string;
  subject: string;
  audience: string;
  language: 'english' | 'arabic';
  level: 'beginner' | 'intermediate' | 'advanced';
}

export interface ICreateCourseFile {
  version: string;
  file: string;
  subject_id: number;
  audience_id: number;
  language_id: number;
  bloom_tax_id: number;
  duration: number;
}
