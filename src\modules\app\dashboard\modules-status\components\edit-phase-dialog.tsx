import { Form, TextInput, useForm, useValidate, Modal } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useUpdateModulePhase } from '../apis/queries';
import { useEffect } from 'react';
import { IPhase } from '../types';
interface IProps {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  selectedPhase: IPhase | null;
}

const EditDialog = ({ isOpen, setIsOpen, selectedPhase }: IProps) => {
  // Hooks
  const { isRequired } = useValidate();
  const { t } = useTranslation();
  const { mutate, isPending } = useUpdateModulePhase();
  //Form
  const { form, setFieldValue } = useForm({
    name_ar: '',
    name_en: '',
    phase_id: 0,
  });
  useEffect(() => {
    if (selectedPhase) {
      setFieldValue('phase_id')(selectedPhase?.id);
      setFieldValue('name_ar')(selectedPhase?.name_ar);
      setFieldValue('name_en')(selectedPhase?.name_en);
    }
  }, [selectedPhase]);
  // Functions
  const handleSubmit = async () => {
    mutate(form, {
      onSuccess: () => {
        setIsOpen(false);
      },
    });
  };
  return (
    <Modal
      className="overflow-visible"
      width={800}
      open={isOpen}
      onOpenChange={setIsOpen}
      modalHeader={t('modulesStatus.popup.editPhase.title')}
    >
      <Form onSubmit={handleSubmit}>
        <div className="justify-between">
          <div className="lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name_ar"
              label={t('name_ar')}
              placeholder={t('name_ar')}
              value={form.name_ar}
              onChange={setFieldValue('name_ar')}
              isRequired
              validators={[isRequired()]}
            />
            <TextInput
              name="name_en"
              label={t('name_en')}
              placeholder={t('name_en')}
              value={form.name_en}
              onChange={setFieldValue('name_en')}
              isRequired
              validators={[isRequired()]}
            />
          </div>
          <div className="flex items-start gap-2 mt-[16px] justify-end">
            <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
              {t('save')}
            </Button>

            <Button disabled={isPending} onClick={() => setIsOpen(false)} className=" mt-4" variant={'outline'}>
              {t('cancel')}
            </Button>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default EditDialog;
