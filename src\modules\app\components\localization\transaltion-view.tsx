import { IDNA } from '@/modules/app/dna/types';
import { ProtectedComponent } from '@/index';
import EmptyLocalization from './empty-state';
import TranslationTable from './transaltion-table';
const TranslationTabView = ({ details }: { details: IDNA | any }) => {
  if (!details?.content_translations?.length) {
    return <EmptyLocalization contentId={details?.id} viewOnly={true} />;
  }

  return (
    <div className="space-y-4">
      {details?.content_translations?.length > 0 && (
        <ProtectedComponent requiredPermissions="translation_show">
          <TranslationTable viewOnly={true} details={details} />
        </ProtectedComponent>
      )}
    </div>
  );
};

export default TranslationTabView;
