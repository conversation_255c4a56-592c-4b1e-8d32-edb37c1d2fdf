import { Api } from '@/services';
import {
  IGenerateActivityPayload,
  INotes,
  ITask,
  ITaskChangeDnaContentPayload,
  ITaskChangeDnaStatusPayload,
} from '../types';
import _ from 'lodash';
import { ISoundType } from '../../dna/types';
import { ILanguage } from '../../dashboard/transaltion/types';
import { AddSlideParams, GenerateSlideshowParams, ISingleSlideShow, ISlideShow } from '../../dna/apis/slideshow/types';

export const getAllUsers = async () => {
  const { data } = await Api.get(`/reviewers?page_size=1000`);
  return data.data;
};
export const assignTask = async (payload: any) => {
  const { data } = await Api.post('/tasks', payload);
  return data.data;
};
export const getTaskByCode = async (code: string): Promise<ITask> => {
  const { data } = await Api.get(`/reviewer/tasks/${code}`);
  return data.data;
};
export const getTaskByCodeAdmin = async (code: string): Promise<ITask> => {
  const { data } = await Api.get(`/tasks/${code}`);
  return data.data;
};
export const editDnaInsideTask = async ({ code, payload }: { code: string; payload: ITaskChangeDnaContentPayload }) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/edit-dna`, payload);
  return data.data;
};
export const chnageDnaStatusInsideTask = async ({
  code,
  payload,
}: {
  code: string;
  payload: ITaskChangeDnaStatusPayload;
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/change-dna-status`, payload);
  return data.data;
};
export const addNotes = async ({ code, payload }: { code: string; payload: INotes }) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/add-notes`, { ...payload, _method: 'put' });
  return data.data;
};
export const updateCourseTitle = async ({
  code,
  payload,
}: {
  code: string;
  payload: { course_id: number; course_title: string };
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/update-course-title`, { ...payload, _method: 'put' });
  return data.data;
};
export const updateDnaData = async ({
  code,
  payload,
}: {
  code: number | string;
  payload: { course_id: number; dna_id: number; bloom_tax_id?: number; title?: string; learning_objectives?: string };
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/update-dna-data`, { ...payload, _method: 'put' });
  return data;
};
export const getTasks = async () => {
  const { data } = await Api.get(`/reviewer/tasks?page_num=1&page_size=1&search=`);
  return data.data.items;
};
export const changeTaskStatus = async (payload: { task_code: string; status: 'Accepted' | 'Declined' }) => {
  const { data } = await Api.post(`/tasks-invitation/change-status`, payload);
  return data.data;
};
export const markAsComplete = async ({ code }: { code: string | any }) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/mark-as-complete`, { _method: 'put' });
  return data.data;
};
export const reAssignedTask = async ({ code, payload }: { code: string; payload: any }) => {
  const { data } = await Api.post(`/tasks/${code}`, { ...payload, _method: 'put' });
  return data.data;
};
export const removeTask = async ({ code }: { code: string }) => {
  const { data } = await Api.post(`/tasks/${code}/make-task-not-available`, { _method: 'put' });
  return data.data;
};
export const approveOrFeedbackTaskDna = async ({
  code,
  dna_id,
  status,
}: {
  code: string | undefined;
  dna_id: number;
  status: string;
}) => {
  const { data } = await Api.post(`/tasks/${code}/approve-or-feedback`, { dna_id, status, _method: 'put' });
  return data.data;
};
export const reOpenTask = async (code: string) => {
  const { data } = await Api.put(`/tasks/${code}/re-open`, {
    description: 're-open',
  });
  return data.data;
};
export const generateActivity = async ({ code, payload }: { code: string; payload: IGenerateActivityPayload }) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/activities/generate`, payload);
  return data.data;
};
export const regenerateActivity = async ({ code, payload }: { code: string; payload: IGenerateActivityPayload }) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/activities/generate?regenerate=true`, payload);
  return data.data;
};
export const approveMultipleActivity = async ({ code, activities }: { code: string; activities: number[] }) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/activities/bulk-change-status`, {
    activities,
    status: 'reviewed',
  });
  return data.data;
};
export const approveActivity = async ({ code, activity_id }: { code: string; activity_id: number }) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/activities/change-status/${activity_id}`, {
    status: 'reviewed',
  });
  return data.data;
};
export const deleteActivity = async ({ code, activity_id }: { code: string; activity_id: number }) => {
  const { data } = await Api.delete(`/reviewer/tasks/${code}/activities/delete/${activity_id}`);
  return data.data;
};
export const regenerateActivityById = async ({ code, activity_id }: { code: string; activity_id: number }) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/activities/re-generate/${activity_id}`);
  return data.data;
};
export const generateActivityXml = async ({ code, activity_id }: { code: string; activity_id: number }) => {
  const { data } = await Api.get(`/reviewer/tasks/${code}/activities/generate-xml/${activity_id}`);
  return data.data;
};
export const updateActivity = async ({
  code,
  activity_id,
  payload,
}: {
  code: string;
  activity_id: number;
  payload: {
    question: string;
    answer: string;
    feedback: string;
    options?: Record<string, string>;
    scenario?: string;
  };
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/activities/update/${activity_id}`, payload);
  return data.data;
};

// slideshow ----  slideshow ----   slideshow ----  slideshow ----  slideshow ----  slideshow

export const getSlideshowPlay = async ({
  taskCode,
  dnaId,
  slideshowId,
}: {
  taskCode: string;
  dnaId: number;
  slideshowId: number;
}): Promise<{ items: any; additional_data: ILanguage[] }> => {
  const { data } = await Api.get(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/${slideshowId}/play`);
  return data.data;
};

export const generateDnaSlideshow = async ({
  dnaId,
  slideshow_sound_type,
  slides_count,
  language_id,
  title,
  model,
  taskCode,
}: {
  dnaId: number | string;
  taskCode: string | number;
  slideshow_sound_type: ISoundType;
  slides_count?: number;
  language_id?: number;
  title?: string;
  model?: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/store`, {
    title,
    slideshow_sound_type,
    slides_count,
    language_id,
    model,
  });
  return data.data;
};
export const reGenerateDnaSlideshow = async ({
  taskCode,
  dnaId,
  slideshow_sound_type,
  slideshow_id,
}: {
  taskCode: string | number;
  dnaId: number | string;
  slideshow_sound_type?: ISoundType;
  slideshow_id: number;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/regenerate`, {
    slideshow_sound_type,
    slideshow_id,
  });
  return data.data;
};

export const updateSlideContent = async ({
  dnaId,
  taskCode,
  payload,
}: {
  dnaId: number | string;
  taskCode: string | number;
  payload: { slideshow_id: number; slide_id: number; content: string };
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/update`, {
    ...payload,
  });
  return data.data;
};
export const regenerateSlide = async ({
  taskCode,
  dnaId,
  slideshow_id,
  slide_id,
  prompt = '',
}: {
  taskCode: string | number;
  dnaId: string | undefined;
  slideshow_id: number;
  slide_id: number;
  prompt?: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/regenerate`, {
    slideshow_id,
    slide_id,
    prompt,
  });
  return data.data;
};
export const changeSlideTemplate = async ({
  taskCode,
  dnaId,
  slideshow_id,
  slide_id,
  template,
}: {
  taskCode: string | number;
  dnaId: string | undefined;
  slideshow_id: number;
  slide_id: number;
  template: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/change-template`, {
    slideshow_id,
    slide_id,
    template,
  });
  return data.data;
};
export const reorderSlide = async ({
  taskCode,
  dnaId,
  slideshow_id,
  slide_id,
  next_slide_id,
}: {
  taskCode: string | number;
  dnaId: number;
  slideshow_id: number;
  slide_id: number;
  next_slide_id: number;
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/reorder`, {
    slideshow_id,
    slide_id,
    next_slide_id,
  });
  return data.data;
};

export const generateVoiceOverSample = async ({
  taskCode,
  dnaId,
  voice_over,
  voice_over_sound,
}: {
  taskCode: string | number;
  dnaId: string | number;
  voice_over: string;
  voice_over_sound: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/get-audio`, {
    voice_over,
    voice_over_sound,
  });
  const base64Audio = data.data.audio;
  const url = `data:audio/mpeg;base64,${base64Audio}`;
  return url;
};
export const updateVoiceOverTextAndAudio = async ({
  taskCode,
  dnaId,
  slideshow_id,
  slide_id,
  voice_over,
  voice_over_sound,
  audio,
}: {
  taskCode: string | number;
  slideshow_id: number;
  slide_id: number;
  dnaId: string | number | undefined;
  voice_over: string;
  voice_over_sound: string;
  audio: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/save-voice-over`, {
    voice_over,
    voice_over_sound,
    audio,
    slide_id,
    slideshow_id,
  });
  return data.data;
};

export const generateSlideshow = async ({ id, slideshow_sound, language_id }: GenerateSlideshowParams) => {
  const { data } = await Api.post(`/dna/slideshow/${id}`, { slideshow_sound, language_id });
  return data.data;
};
export const addSlide = async ({
  taskCode,
  dnaId,
  prompt,
  template,
  voice_over_sound,
  next_slide_id,
  slideshow_id,
}: AddSlideParams) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/add-new`, {
    template,
    prompt,
    next_slide_id,
    slideshow_id,
    voice_over_sound,
  });
  return data.data;
};
export const listSlideshow = async ({
  taskCode,
  dnaId,
}: {
  taskCode: string | number;
  dnaId: number | string;
}): Promise<ISlideShow[] | []> => {
  const { data } = await Api.get(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/list`);
  return data?.data || [];
};
export const singleSlideShow = async ({
  taskCode,
  dnaId,
  slideshowId,
}: {
  taskCode: string | number;
  dnaId: number | string;
  slideshowId: number | string;
}): Promise<ISingleSlideShow> => {
  const { data } = await Api.get(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/${slideshowId}`);
  return data?.data || {};
};
export const deleteSlide = async ({
  taskCode,
  dnaId,
  payload,
}: {
  taskCode: string | number;
  dnaId: number | string;
  payload: { slideshow_id: Number; slide_id: Number };
}) => {
  const { data } = await Api.delete(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/slides/slide/delete`, {
    ...payload,
  });
  return data?.data || [];
};
export const getAllowedLanguages = async ({
  taskCode,
  dnaId,
}: {
  taskCode: string | number;
  dnaId: number | string;
}): Promise<IMetadata[] | []> => {
  const { data } = await Api.get(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/allowed-languages`);
  return data?.data || [];
};
export const getTranslatedSlideshowAllowedLanguages = async ({
  taskCode,
  dnaId,
  slideshowId,
}: {
  taskCode: string | number;
  dnaId: number | string;
  slideshowId: number | string;
}): Promise<IMetadata[] | []> => {
  const { data } = await Api.get(
    `/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/${slideshowId}/translate/allowed-languages`
  );
  return data?.data || [];
};
export const deleteSlideshow = async ({
  dnaId,
  slideshow_id,
  taskCode,
}: {
  taskCode: string | number;
  dnaId: number;
  slideshow_id: number;
}) => {
  const { data } = await Api.delete(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/delete`, {
    slideshow_id,
  });
  return data.data;
};
export const updateSlideshow = async ({
  taskCode,
  dnaId,
  title,
  status,
  slideshow_id,
}: {
  taskCode: string | number;
  dnaId: number | string;
  title?: string;
  status?: string;
  slideshow_id: number;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/update`, {
    slideshow_id,
    title,
    status,
    _method: 'put',
  });
  return data.data;
};
export const addTransaltionSlideshow = async ({
  taskCode,
  dnaId,
  slideshow_id,
  language_id,
  model,
}: {
  dnaId: number | string;
  taskCode: string | number;
  slideshow_id?: string;
  language_id: number;
  model: string;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskCode}/dna/${dnaId}/slideshow/translate`, {
    slideshow_id,
    language_id,
    model,
  });
  return data.data;
};
