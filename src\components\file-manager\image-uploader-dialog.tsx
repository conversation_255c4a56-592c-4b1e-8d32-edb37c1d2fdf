import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRef, useState } from 'react';
import { ImageIcon } from 'lucide-react';
import { Form, Modal, TextInput, ComboboxInput } from '@/components';
import { t } from 'i18next';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useUploadImage } from '../../modules/app/dna/apis/media/queries';
import { useForm, useNotify } from '@/hooks';
import { useParams } from 'react-router-dom';

const ImageUploaderDialog = ({ open, onOpenChange }: { open: boolean; onOpenChange: any }) => {
  // States
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);
  const { data: allmetaData } = useGetSingleMetadata('');
  const subjectOptions = allmetaData?.filter((item) => item.type === 'subject') || [];
  const audienceOptions = allmetaData?.filter((item) => item.type === 'audience') || [];
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];
  // Ref
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  //Hooks
  const { dnaId } = useParams();
  const { notify } = useNotify();
  const { mutate: uploadImage, isPending: isUploading } = useUploadImage();
  const { form, setFieldValue } = useForm({
    file: null,
    dna_id: dnaId,
    media_name: null,
    audience_id: null,
    language_id: null,
    subject_id: null,
  });

  // Functions
  const handleFileUpload = (file: File) => {
    setFieldValue('file')(file);
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setUploadPreview(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadImage = () => {
    if (!form.file) {
      notify.error(t('media.addImage'));
      return;
    }

    uploadImage(
      {
        dna_id: dnaId || '',
        file: form.file,
        media_name: form.media_name,
        audience_id: form.audience_id,
        language_id: form.language_id,
        subject_id: form.subject_id,
      },
      {
        onSuccess: () => {
          onOpenChange(false);
        },
      }
    );
  };
  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Form onSubmit={handleUploadImage}>
        <div className="grid grid-rows-4 h-[400px] pt-3 ">
          <div className="border-2 mb-3 row-span-2 border-dashed border-muted-foreground/20 rounded-lg w-full flex flex-col items-center justify-center">
            {/* hidden input */}
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                if (e.target.files?.[0]) {
                  handleFileUpload(e.target.files[0]);
                }
              }}
            />
            {uploadPreview ? (
              <div className="relative w-full h-full overflow-hidden group">
                <img src={uploadPreview} alt="Upload preview" className=" w-full h-full rounded-lg object-contain" />
                <div className="absolute opacity-0 transition-all inset-0 z-10 bg-black/50 flex items-center justify-center rounded-lg group-hover:opacity-100">
                  <Button
                    variant="secondary"
                    className="mx-auto mt-2"
                    size="sm"
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    {t('media.replace')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <ImageIcon className="!h-8 !w-8 mx-auto text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">Drop your image here or click to browse</p>
                  <p className="text-xs text-muted-foreground">Supports JPG, PNG, GIF up to 10MB</p>
                  <Button
                    variant="secondary"
                    className="mx-auto mt-2"
                    size="sm"
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Browse Files
                  </Button>
                </div>
              </div>
            )}
          </div>
          <div className="row-span-2">
            <div className="grid gap-2">
              <div className="grid grid-cols-2 gap-2">
                <TextInput
                  placeholder={t('media.name')}
                  name="media_name"
                  label={t('media.name')}
                  value={form.media_name}
                  onChange={setFieldValue('media_name')}
                />
                <ComboboxInput
                  placeholder={t('media.language')}
                  name="language_id"
                  label={t('media.language')}
                  options={generateEnum(languageOptions, 'id', 'name_en')}
                  value={form.language_id}
                  onChange={setFieldValue('language_id')}
                />
              </div>
              <div className="grid grid-cols-2 gap-2 items-center">
                <ComboboxInput
                  placeholder={t('media.subject')}
                  name="subject_id"
                  label={t('media.subject')}
                  options={generateEnum(subjectOptions, 'id', 'name_en')}
                  value={form.subject_id}
                  onChange={setFieldValue('subject_id')}
                />
                <ComboboxInput
                  placeholder={t('media.audience')}
                  name="audience_id"
                  label={t('media.audience')}
                  options={generateEnum(audienceOptions, 'id', 'name_en')}
                  value={form.audience_id}
                  onChange={setFieldValue('audience_id')}
                />
              </div>
            </div>
          </div>
        </div>

        <div className="pt-4 mr-auto border-t border-border flex justify-end items-center">
          <Button type="submit" size={'sm'} className="min-w-32" loading={isUploading}>
            {t('media.upload')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ImageUploaderDialog;
