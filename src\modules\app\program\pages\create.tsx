import React, { useState, useEffect } from 'react';

import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { programPlanAtom, currentStepAtom } from '../store';
import { useSearchParams } from 'react-router-dom';
import ProgramInfoStep from '../components/program-info';
import CoursesInformation from '../components/courses-info/courses-information';
import CouresePlanStep from '../components/courses-plan/course-plan-step';
import ProgramPlanStepPage from '../components/program-plan/program-plan';
import { useGetProgramPLanById } from '../apis/queries';

type Step = {
  id: number;
  label: string;
};

const ProgramPlanPage: React.FC = () => {
  const [searchParams] = useSearchParams();

  const { data } = useGetProgramPLanById(searchParams.get('programId') || '');
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [currentStep, setCurrentStep] = useAtom<number>(currentStepAtom);
  const [programPlan, setProgramPlan] = useAtom(programPlanAtom);
  const { t } = useTranslation();

  // effects
  useEffect(() => {
    if (data) {
      setProgramPlan(data);
      setCurrentStep(currentStep !== 1 ? currentStep : data.step);
    }
  }, [data, setCurrentStep]);

  const steps: Step[] = [
    { id: 1, label: t('Program Information') },
    { id: 2, label: t('Program Plan') },
    { id: 3, label: t('Courses Information') },
    { id: 4, label: t('Courses Plan') },
  ];
  // State for storing API data for each step
  const handleNextStep = (step: number) => {
    setCurrentStep(step);
    setCompletedSteps((prev) => [...prev, step, step - 1]);
  };

  const components: { [key: number]: JSX.Element } = {
    1: <ProgramInfoStep moveToNextStep={() => handleNextStep(2)} />,
    2: <ProgramPlanStepPage moveToNextStep={() => handleNextStep(3)} />,
    3: <CoursesInformation moveToNextStep={() => handleNextStep(4)} />,
    4: <CouresePlanStep onFinish={() => {}} />,
  };

  return (
    <div>
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-12 items-center my-12 cursor-pointer">
        {steps.map((step) => (
          <div
            key={step.id}
            className={`flex gap-4 transition-all items-start ${
              currentStep === step.id ? 'text-blue-500' : step.id < currentStep ? 'text-green-500' : 'text-gray-500'
            }`}
            onClick={() => {
              if (step.id === currentStep || step.id <= (data?.step || currentStep)) {
                setCurrentStep(step.id);
              }
            }}
          >
            {step.id < (data?.step || 1) ? (
              <Icon className="self-start" icon={`icon-park-solid:check-one`} width={25} />
            ) : (
              <Icon className="self-start" icon={`fluent:number-circle-${step.id}-20-regular`} width={25} />
            )}
            <div className="space-y-2">
              <p className="text-lg -mt-1">{step.label}</p>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-10 transition-all">{components[currentStep]}</div>
    </div>
  );
};

export default ProgramPlanPage;
