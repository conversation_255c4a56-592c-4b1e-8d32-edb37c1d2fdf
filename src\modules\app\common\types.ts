export interface IMedia {
  mime_type: string;
  model_id: number;
  model_type: string;
  name: string;
  size: string;
  url: string;
  uuid: string;
  meta_data?: {
    user_id: 9;
    prompt: string;
    drawing_style_id: number;
    dimensions: number;
    quality: number;
    model: string;
    user: {
      id: number;
      name: string;
      email: string;
    };
    audience: {
      id: number;

      name_en: string;
      name_ar: string;
    };
    language: {
      id: number;

      name_en: string;
      name_ar: string;
    };
    subject: {
      id: number;
      name_en: string;
      name_ar: string;
    };
  };
}

export interface IGetMediaResponse {
  dna_id: number;
  topic_id: null;
  dna_title: string;
  media: IMedia[];
}
