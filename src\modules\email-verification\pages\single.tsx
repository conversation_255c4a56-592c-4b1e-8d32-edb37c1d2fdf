import LanguageLink from '@/components/language-link';
import { Button } from '@/components/ui/button';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useParams, useSearchParams } from 'react-router-dom';
import { useVerifyEmail } from '../apis/queries';
import { Icon } from '@/components';

const EmailVerification = ({ status = 'info', message = 'Verifying your email...' }) => {
  const getStatusColor = (status = 'info') => {
    switch (status) {
      case 'success':
        return 'text-green-500';
      case 'error':
        return 'text-red-500';
      default:
        return 'text-orange-500';
    }
  };
  const [search] = useSearchParams();
  const id = search.get('id') || '';
  const hash = search.get('hash') || '';
  const { data, error, isError, isPending, isSuccess } = useVerifyEmail({ id, hash });
  return (
    <div className="flex justify-center items-center min-h-screen  text-gray-700">
      <div className="px-10 py-8 rounded-2xl shadow-sm border border-border transform transition-all duration-300  max-w-md w-full text-center">
        <div className="text-6xl text-purple-700 mb-5">✉️</div>
        <h1 className="text-2xl mb-4 text-gray-700 font-medium">Email Verification</h1>
        {isError && <p className="text-lg mb-5 font-bold text-red-500">{error?.message}</p>}
        {/* <p className={`text-lg mb-5 font-bold ${getStatusColor(status)}`}>{message}</p> */}
        {isPending && <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary my-3" width={30} />}

        {isSuccess && <p className="text-lg mb-5 font-bold text-green-500">{data?.data?.message}</p>}
        <LanguageLink to="/app">
          <Button className="px-10 w-full rounded-lg">Home</Button>
        </LanguageLink>
      </div>
    </div>
  );
};

export default EmailVerification;
