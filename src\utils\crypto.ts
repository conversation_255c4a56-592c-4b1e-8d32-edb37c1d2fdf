import CryptoJS from 'crypto-js';

const secretKey = 'yourverylongsecretgirheremustbe32bytes'; // Ensure 32 characters

// Encrypt function
const encrypt = (obj: any) => {
  try {
    return CryptoJS.AES.encrypt(JSON.stringify(obj), secretKey).toString() as any;
  } catch (error) {
    return null;
  }
};

// Decrypt function
const decrypt = (encryptedData: any) => {
  try {
    const bytes = CryptoJS.AES.decrypt(encryptedData, secretKey);
    const decryptedOutput = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedOutput);
  } catch (error) {
    return null;
  }
};

export { encrypt, decrypt };
