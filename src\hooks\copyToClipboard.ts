export const copyToClipboard = async (textToCopy: any, notify: any) => {
  if (navigator.clipboard && window.isSecureContext) {
    await navigator.clipboard.writeText(textToCopy);
    notify.success('text copied to clipboard!');
  } else {
    const textArea = document.createElement('textarea');
    textArea.value = textToCopy;
    textArea.style.position = 'absolute';
    textArea.style.left = '-999999px';
    document.body.prepend(textArea);
    textArea.select();

    try {
      document.execCommand('copy');
      notify.success('text copied to clipboard!');
    } catch (error) {
      notify.error('Failed to copy text. Please try again.');
    } finally {
      textArea.remove();
    }
  }
};
