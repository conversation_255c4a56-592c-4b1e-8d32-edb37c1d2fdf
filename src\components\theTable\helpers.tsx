import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { ReactElement } from 'react';
import { TheRow } from './table-content-row';

function createRowPathLocal(row: any, parentPath: string, rowKey: string): string {
  const currentId = row?.[rowKey]?.toString() || '';
  return parentPath ? `${parentPath}->${currentId}` : currentId;
}

export function renderRows(
  rows: any[],
  level: number,
  parentPath: string,
  rowKey: string,
  columns: any[],
  isMultipleSelect: boolean,
  isDraggable: boolean,
  isRowSelected: (id: string) => boolean,
  onSelectRow: (row: any) => void,
  expandedRows: Set<string>,
  toggleRow: (path: string) => void,
  getChildRows: (row: any, level: number) => any[] | null,
  t: any,
  parentRow: any[] = []
): ReactElement[] {
  if (!rows) return [];

  return rows.flatMap((row, index) => {
    const currentPath = createRowPathLocal(row, parentPath, rowKey);
    const rowEl = (
      <TheRow
        key={currentPath}
        row={row}
        parentRow={parentRow}
        level={level}
        rowPath={currentPath}
        columns={columns}
        isMultipleSelect={isMultipleSelect}
        isDraggable={isDraggable}
        rowKey={rowKey}
        isRowSelected={isRowSelected}
        onSelectRow={onSelectRow}
        expandedRows={expandedRows}
        toggleRow={toggleRow}
        getChildRows={getChildRows}
        t={t}
        index={index + 1} // This is the correct index for the current level
      />
    );

    const childEls: ReactElement[] = [];
    if (expandedRows.has(currentPath)) {
      const children = getChildRows(row, level);
      if (children && children.length > 0) {
        const childIds = children.map((c: any) => c.id);
        childEls.push(
          <SortableContext
            key={`SC-${currentPath}`}
            id={currentPath}
            items={childIds}
            strategy={verticalListSortingStrategy}
          >
            {renderRows(
              children,
              level + 1,
              currentPath,
              rowKey,
              columns,
              isMultipleSelect,
              isDraggable,
              isRowSelected,
              onSelectRow,
              expandedRows,
              toggleRow,
              getChildRows,
              t,
              row // Pass the current row as parentRow for children
            )}
          </SortableContext>
        );
      }
    }

    return [rowEl, ...childEls];
  });
}
