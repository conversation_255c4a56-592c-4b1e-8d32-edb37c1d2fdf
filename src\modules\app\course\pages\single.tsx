import { Button } from '@/components/ui/button';
import { CoursePlanTable } from '@/modules/app/course/components/course-plan-table';
import Tasks from './tasks';
import { t } from 'i18next';
import { useParams } from 'react-router-dom';
import { useGetCourseById } from '../apis/queries';
import { useSearchParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { PageLoader } from '@/components';

interface ITab {
  name: 'courseDetails' | 'taskDetails';
  disabled?: boolean;
}

const SingleCoursePage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { id } = useParams();
  const { data: course } = useGetCourseById(searchParams.get('courseId') || id);

  const [currentTab, setCurrentTab] = useState<'courseDetails' | 'taskDetails'>(
    (searchParams.get('tab') as 'courseDetails' | 'taskDetails') || 'courseDetails'
  );

  const hasTasks = course?.tasks?.data && course.tasks.data.length > 0;
  const tabs: ITab[] = [{ name: 'courseDetails' }, { name: 'taskDetails', disabled: !hasTasks }];

  useEffect(() => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('tab', currentTab);
    setSearchParams(newSearchParams, { replace: true });
  }, [currentTab]);

  const handleTabChange = (tabName: 'courseDetails' | 'taskDetails') => {
    const selectedTab = tabs.find((tab) => tab.name === tabName);
    if (selectedTab && !selectedTab.disabled) {
      setCurrentTab(tabName);
    }
  };

  if (!course) return <PageLoader />;

  return (
    <div className="mt-12">
      <div className="bg-background border-b border-border p-4 ps-0">
        <div className="flex gap-3">
          {tabs.map((tab) => (
            <Button
              variant={'outline'}
              className={`min-w-[120px] hover:text-primary hover:bg-semi-primary ${
                currentTab === tab.name ? 'text-primary bg-semi-primary' : ''
              }`}
              key={tab.name}
              onClick={() => handleTabChange(tab.name)}
              disabled={tab.disabled}
            >
              {t(tab.name)}
            </Button>
          ))}
        </div>
      </div>
      {currentTab === 'courseDetails' ? (
        <CoursePlanTable />
      ) : (
        <Tasks
          canCreateNewTask={course.tasks.can_create_new_task}
          canCreateChildTask={course.tasks.can_assign_child_task_to_parent}
          course={course}
        />
      )}
    </div>
  );
};

export default SingleCoursePage;
