import { useNavigate, useParams, useLocation } from 'react-router-dom';
import i18n from '../utils/i18n';

const useLanguageNavigate = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();

  const lng = params.lng || i18n.language;
  const supportedLanguages = ['en', 'ar'];

  const addLanguageToPath = (path: any) => {
    if (!path.startsWith('/')) {
      return path;
    }

    const pathSegments = path.split('/').filter(Boolean);

    if (supportedLanguages.includes(pathSegments[0])) {
      return path;
    } else {
      return `/${lng}${path}`;
    }
  };

  const languageNavigate = (to: any, options = {}) => {
    if (!lng) {
      return;
    }

    let newTo;

    if (typeof to === 'string') {
      newTo = addLanguageToPath(to);
    } else if (typeof to === 'object' && to !== null) {
      const pathname = to.pathname || location.pathname;
      const newPathname = addLanguageToPath(pathname);
      newTo = {
        ...to,
        pathname: newPathname,
      };
    } else {
      return;
    }

    navigate(newTo, options);
  };

  return languageNavigate;
};

export default useLanguageNavigate;
