import React from 'react';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { useTranslation } from 'react-i18next';
import { Button } from '../ui/button';
interface ITab {
  tab: string;
  route: string;
}
const TableTabs: React.FC<{ tabs: ITab[] }> = ({ tabs }) => {
  const navegate = useLanguageNavigate();
  const { t } = useTranslation();
  return (
    <div className="bg-background border-b border-border p-4">
      <div className="flex gap-3">
        {tabs.map((tab: any, index: any) => (
          <Button
            variant={'outline'}
            className={`min-w-[120px] hover:text-primary hover:bg-semi-primary ${
              location.pathname.substring(3) === tab.route ? 'text-primary bg-semi-primary' : ''
            }`}
            key={tab.route}
            onClick={() => navegate(tab.route)}
          >
            {t(tab.tab)}
          </Button>
        ))}
      </div>
    </div>
  );
};

export default TableTabs;
