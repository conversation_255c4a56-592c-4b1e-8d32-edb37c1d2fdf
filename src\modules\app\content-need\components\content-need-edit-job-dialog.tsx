import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useEditContentNeedJob } from '@/modules/app/content-need/apis/queries';

const EditJobTitleDialog = ({
  isOpen,
  setIsOpen,
  jobBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  jobBeingEdited: { id: number; content_id: number; title: string };
}) => {
  const { t } = useTranslation();
  const { form, setFieldValue } = useForm({
    title: jobBeingEdited?.title || '',
    content_id: jobBeingEdited?.content_id || 0,
  });
  const { mutate: editJob, isPending: isEditing, variables: editJobVariables } = useEditContentNeedJob();

  const handleSubmit = () => {
    editJob(
      { id: jobBeingEdited.id, content_id: jobBeingEdited.content_id, title: form.title },
      {
        onSuccess: () => {
          setIsOpen(false);
        },
      }
    );
  };

  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('editJobTitle.title')}>
      <Form onSubmit={handleSubmit} className="flex flex-col">
        <TextInput name="title" label={t('editJobTitle.title')} value={form.title} onChange={setFieldValue('title')} />
        <Button loading={isEditing} disabled={isEditing} type="submit" className="mt-4 ml-auto w-fit">
          {t('save')}
        </Button>
      </Form>
    </Modal>
  );
};

export default EditJobTitleDialog;
