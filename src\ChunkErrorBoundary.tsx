import React, { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components/icon';

interface ChunkErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  maxRetries?: number;
  onError?: (error: Error) => void;
}

function ChunkErrorBoundary({ children, fallback, maxRetries = 3, onError }: ChunkErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const { t } = useTranslation();

  const isChunkError = useCallback((error: Error) => {
    return (
      error.name === 'ChunkLoadError' ||
      error.message?.includes('dynamically imported module') ||
      error.message?.includes('Failed to fetch dynamically imported module') ||
      error.message?.includes('Loading chunk') ||
      error.message?.includes('ChunkLoadError') ||
      error.message?.includes('Loading CSS chunk')
    );
  }, []);

  const handleRetry = useCallback(() => {
    if (retryCount < maxRetries) {
      setHasError(false);
      setError(null);
      setRetryCount((prev) => prev + 1);
      // Force a re-render by updating a key or state
      setTimeout(() => {
        window.location.reload();
      }, 100);
    } else {
      window.location.reload();
    }
  }, [retryCount, maxRetries]);

  const handleRefresh = useCallback(() => {
    window.location.reload();
  }, []);

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      const error = event.error;
      if (error && isChunkError(error)) {
        console.error('Chunk loading error caught:', error);
        onError?.(error);
        setHasError(true);
        setError(error);
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      if (error instanceof Error && isChunkError(error)) {
        console.error('Chunk loading error caught via unhandled rejection:', error);
        onError?.(error);
        setHasError(true);
        setError(error);
        event.preventDefault(); // Prevent the error from being logged to console
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [isChunkError, onError]);

  if (hasError) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-8 text-center bg-background w-full">
        <div className="max-w-lg mx-auto">
          <Icon
            icon="material-symbols:error-outline"
            className="text-red-500 mx-auto animate-pulse mb-4"
            width={100}
            height={100}
          />

          <h3 className="text-xl font-medium mb-3 text-foreground">
            {t('chunkError.title', 'Failed to load component')}
          </h3>

          <p className="text-muted-foreground mb-6 leading-relaxed">
            {t(
              'chunkError.description',
              "We couldn't load part of the application. This might be due to a network connection issue or a recent update. Please check your internet connection and try again."
            )}
          </p>

          {retryCount > 0 && retryCount < maxRetries && (
            <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                {t('chunkError.retryAttempt', `Retry attempt ${retryCount} of ${maxRetries}`)}
              </p>
            </div>
          )}

          {/* Technical Details (Development Only) */}
          {error && (
            <div className="mb-6 text-left">
              <div className="cursor-pointer text-sm text-muted-foreground hover:text-foreground transition-colors mb-2">
                Technical Details
              </div>
              <div className="mt-2 p-3 bg-muted rounded-lg border">
                <pre className="text-xs text-muted-foreground overflow-auto whitespace-pre-wrap break-words">
                  {error.message}
                  {error.stack && (
                    <>
                      {'\n\nStack trace:\n'}
                      {error.stack}
                    </>
                  )}
                </pre>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button
              onClick={handleRetry}
              variant="outline"
              className="min-w-[120px]"
              disabled={retryCount >= maxRetries}
            >
              <Icon icon="solar:refresh-line-duotone" className="mr-2 mt-1" width={16} />
              {retryCount >= maxRetries
                ? t('chunkError.maxRetriesReached', 'Max retries reached')
                : t('chunkError.retry', 'Try Again')}
            </Button>

            <Button onClick={handleRefresh} className="min-w-[120px]">
              <Icon icon="solar:refresh-line-duotone" className="mr-2 mt-1" width={16} />
              {t('chunkError.refresh', 'Refresh Page')}
            </Button>
          </div>

          <p className="mt-6 text-xs text-muted-foreground">
            {t('chunkError.helpText', 'If this problem persists, try clearing your browser cache or contact support.')}
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

export default ChunkErrorBoundary;
