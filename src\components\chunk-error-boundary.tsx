import React, { Component, ReactNode } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Icon } from '@/components/icon';
import { useTranslation } from 'react-i18next';

interface ChunkErrorBoundaryState {
  hasError: boolean;
  error?: Error;
  retryCount: number;
}

interface ChunkErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  maxRetries?: number;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

// Error display component that uses hooks
const ChunkErrorDisplay: React.FC<{
  onRetry: () => void;
  onRefresh: () => void;
  error?: Error;
}> = ({ onRetry, onRefresh, error }) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col items-center justify-center min-h-[400px] p-6 text-center">
      <Icon 
        icon="material-symbols:error-circle-rounded-outline" 
        className="text-red-500 mb-4" 
        width={64} 
      />
      <h3 className="text-lg font-semibold mb-2 text-foreground">
        {t('chunkError.title', 'Failed to load component')}
      </h3>
      <p className="text-muted-foreground mb-6 max-w-md">
        {t('chunkError.description', 'There was an issue loading part of the application. This might be due to a network issue or a recent update.')}
      </p>
      
      {error && process.env.NODE_ENV === 'development' && (
        <details className="mb-4 text-left">
          <summary className="cursor-pointer text-sm text-muted-foreground">
            Technical Details
          </summary>
          <pre className="mt-2 text-xs bg-muted p-2 rounded overflow-auto max-w-md">
            {error.message}
          </pre>
        </details>
      )}

      <div className="flex gap-3">
        <Button onClick={onRetry} variant="outline">
          <Icon icon="material-symbols:refresh" className="mr-2" width={16} />
          {t('chunkError.retry', 'Try Again')}
        </Button>
        <Button onClick={onRefresh}>
          <Icon icon="material-symbols:refresh" className="mr-2" width={16} />
          {t('chunkError.refresh', 'Refresh Page')}
        </Button>
      </div>
    </div>
  );
};

class ChunkErrorBoundary extends Component<ChunkErrorBoundaryProps, ChunkErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ChunkErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ChunkErrorBoundaryState> {
    // Check if this is a chunk loading error
    const isChunkError = 
      error.name === 'ChunkLoadError' ||
      error.message.includes('Failed to fetch dynamically imported module') ||
      error.message.includes('Loading chunk') ||
      error.message.includes('ChunkLoadError') ||
      error.message.includes('Loading CSS chunk');

    if (isChunkError) {
      return {
        hasError: true,
        error,
      };
    }

    // For non-chunk errors, don't handle them here
    return { hasError: false };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Only handle chunk loading errors
    const isChunkError = 
      error.name === 'ChunkLoadError' ||
      error.message.includes('Failed to fetch dynamically imported module') ||
      error.message.includes('Loading chunk') ||
      error.message.includes('ChunkLoadError');

    if (isChunkError) {
      console.error('Chunk loading error caught by boundary:', error, errorInfo);
      this.props.onError?.(error, errorInfo);
    } else {
      // Re-throw non-chunk errors so they can be handled by other error boundaries
      throw error;
    }
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    
    if (this.state.retryCount < maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        retryCount: prevState.retryCount + 1,
      }));
    } else {
      // If max retries reached, force a page refresh
      this.handleRefresh();
    }
  };

  handleRefresh = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <ChunkErrorDisplay
          onRetry={this.handleRetry}
          onRefresh={this.handleRefresh}
          error={this.state.error}
        />
      );
    }

    return this.props.children;
  }
}

export { ChunkErrorBoundary };
