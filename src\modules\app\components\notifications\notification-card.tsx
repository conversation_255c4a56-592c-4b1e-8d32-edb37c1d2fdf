import { Icon } from '@/components';
import { useChangeNotificationReadStatus, useDeleteNotification } from '../../notification/apis/queries';
import { t } from 'i18next';
import { dateAndTimeFormat, formatNotificationDate } from '@/utils/helpers';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { INotification, NotificationURL } from '@/modules/app/notification/types';
import { useMemo, useState } from 'react';
import LanguageLink from '@/components/language-link';
import { useTranslation } from 'react-i18next';
import { NotificationsEnums } from '@/index';

const NotificationCard = ({
  notification,
  setIsNotificationOpen,
}: {
  notification: INotification;
  setIsNotificationOpen?: any;
}) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { t, i18n } = useTranslation();
  const { mutate: changeStatus, isPending: isChangingStatus } = useChangeNotificationReadStatus();
  const { mutate: initialStatusChange } = useChangeNotificationReadStatus();
  const { mutate: deleteNotification, isPending: isDeleting } = useDeleteNotification();

  const notificationRoute = useMemo(() => {
    const notificationType = NotificationsEnums.urls.find(
      (urlType: NotificationURL) => urlType.value === notification.type
    );
    if (notificationType) {
      return notificationType.url(notification);
    }
    return `/app/my-content`;
  }, [notification]);
  const handleNotificationClick = () => {
    setIsNotificationOpen(false);
    if (!notification.read_at) {
      initialStatusChange(notification.id);
    }
  };
  const handleChangeStatus = (e: Event | any) => {
    e.stopPropagation();
    changeStatus(notification.id);
  };
  const handleDeleteNotification = (e: Event | any) => {
    e.stopPropagation();
    deleteNotification(notification.id);
  };
  console.log('notification', notification);

  return (
    <LanguageLink
      to={notificationRoute}
      onClick={handleNotificationClick}
      className={`flex relative gap-3 p-4 text-gray-500 cursor-pointer rounded-md hover:bg-gray-50 ${
        notification.read_at ? 'read' : '!bg-primary/10'
      }`}
    >
      {(isDeleting || isChangingStatus) && (
        <div className="absolute top-0 left-0 flex items-center justify-center w-full h-full backdrop-blur-sm">
          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
        </div>
      )}
      <div className="size-11 min-w-11 min-h-11">
        <img
          src={notification?.data?.data?.created_by?.avatar || '/placeholder.png'}
          className="object-cover rounded-full size-full"
          alt=""
        />
      </div>
      <div className="w-48">
        <p className="text-sm line-clamp-3">{notification?.data?.data?.created_by?.name} assigned Course:</p>
        <p className="text-sm">{notification?.data?.data?.content?.title}</p>
        <div className="text-xs mt-1.5 flex gap-1">
          <Icon icon="solar:clock-circle-bold" width="16" height="16" />
          {dateAndTimeFormat(
            notification?.data?.data?.created_at || notification.created_at,
            i18n.language,
            'flex gap-1'
          )}
        </div>
      </div>
      <DropdownMenu open={isMenuOpen} onOpenChange={setIsMenuOpen}>
        <DropdownMenuTrigger
          className="flex items-start h-fit"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <div className="flex justify-center w-7">
            <Icon icon="solar:menu-dots-bold" width="24" height="24" />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem className="flex items-center justify-start w-full gap-1" onClick={handleDeleteNotification}>
            <Icon icon="gg:trash" width="18" className="mt-px -ms-px" />
            <span className="text-sm"> {t('notification.delete')}</span>
          </DropdownMenuItem>
          {notification.read_at ? (
            <DropdownMenuItem className="flex items-center justify-start w-full gap-1" onClick={handleChangeStatus}>
              <Icon icon="f7:envelope-badge" width="16" />
              <span className="text-sm"> {t('notification.markAsUnread')}</span>
            </DropdownMenuItem>
          ) : (
            <DropdownMenuItem className="flex items-center justify-start w-full gap-1" onClick={handleChangeStatus}>
              <Icon icon="gravity-ui:envelope-open" width="16" />
              <span className="text-sm"> {t('notification.markAsRead')}</span>
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </LanguageLink>
  );
};

export default NotificationCard;
