import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  addMetadata,
  deleteMetadata,
  exportTypes,
  getSingleMetadata,
  getTypes,
  importTypes,
  restoreMetadata,
  updateMetadata,
} from './endpoints';
import { useNotify } from '@/index';
import { MetadataKeys } from '../types';
import { useTranslation } from 'react-i18next';
// Simulate creating metadata through an API

// React Query hook for creating metadata
export const useCreateMetadata = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addMetadata,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['types-names'] });
      queryClient.invalidateQueries({ queryKey: ['types'] });
      notify.success('Metadata Added Successfully!');
    },
    onError: (error: any) => {
      if (error.response_code === 422) {
        notify.error('Duplicated data detected!');
      } else {
        notify.error(error.message);
      }
    },
  });
};

export const useGetTypes = () => {
  return useQuery({
    queryKey: ['types-names'],
    queryFn: getTypes,
  });
};

export const useDeleteMetadata = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteMetadata,
    onSuccess: () => {
      notify.success('Metadata Deleted Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useRestoreMetadata = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: restoreMetadata,
    onSuccess: () => {
      notify.success('Metadata Restored Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateMetadata = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: updateMetadata,
    onSuccess: () => {
      notify.success('Metadata Updated Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGetSingleMetadata = (type: MetadataKeys | '') => {
  return useQuery({
    queryKey: ['types', type],
    queryFn: () => getSingleMetadata(type),
  });
};

export const useImportTypes = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  return useMutation({
    mutationKey: ['importTypes'],
    mutationFn: importTypes,

    onSuccess: () => {
      notify.success(t('notify.imported'));
    },
    onError: (error) => {
      notify.error(error.message || t('notify.somethingWentWrong'));
    },
  });
};
export const useExportTypes = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();

  return useMutation({
    mutationKey: ['exportTypes'],
    mutationFn: exportTypes,
    onSuccess: () => {
      notify.success(t('notify.exported'));
    },
    onError: (error) => {
      notify.error(error.message || t('notify.somethingWentWrong'));
    },
  });
};
