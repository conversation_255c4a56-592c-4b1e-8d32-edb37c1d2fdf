import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import { useDeleteRole, useGetPermissions, useGetRoles, useUpdateRole } from '../apis/queries';
import { useTranslation } from 'react-i18next';
import { Checkbox } from '@/components/ui/checkbox';
import { IPermission, IRole } from '../types';
import { Icon, useConfirmDialog } from '@/index';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import LanguageLink from '@/components/language-link';
import { useUsersTablesTabs } from '../../content-tabs';

const List = () => {
  const { t } = useTranslation();
  const navigate = useLanguageNavigate();
  const { data: roles } = useGetRoles();
  const { data: permissions } = useGetPermissions();
  const { mutate: updateRole } = useUpdateRole();
  const { mutate: deleteRole } = useDeleteRole();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const [pendingChanges, setPendingChanges] = useState<Record<string, { add: Set<string>; remove: Set<string> }>>({});
  const [isSaving, setIsSaving] = useState(false);

  const navegate = useLanguageNavigate();

  const tabs = useUsersTablesTabs();

  const isChecked = (role: IRole, permission: IPermission) => {
    const changes = pendingChanges[role.id];
    if (!changes) {
      return role.permissions.some((perm) => perm.id === permission.id);
    }

    const isOriginallyChecked = role.permissions.some((perm) => perm.id === permission.id);
    if (changes.add.has(permission.id as any)) return true;
    if (changes.remove.has(permission.id as any)) return false;
    return isOriginallyChecked;
  };

  const handleDeleteRole = async (id: string) => {
    showConfirm(ConfirmApproveText(), {
      async onConfirm() {
        deleteRole(id, {
          onSuccess: () => {
            hideConfirm();
          },
        });
      },
    });
  };

  const ConfirmApproveText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="solar:trash-bin-minimalistic-broken" width={55} className="text-red-500" />
        </div>
        <p>Are you sure you want to proceed?</p>
      </div>
    );
  };

  const ConfirmEditText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-20-regular" width={55} />
        </div>
        <p>Do you want to save the permission changes?</p>
      </div>
    );
  };

  // Handle permission toggle
  const handleTogglePermission = (checked: boolean, role: IRole, permission: IPermission) => {
    setPendingChanges((prev) => {
      const current = prev[role.id] || { add: new Set(), remove: new Set() };
      const isOriginallyChecked = role.permissions.some((perm) => perm.id === permission.id);
      const newAdd = new Set(current.add);
      const newRemove = new Set(current.remove);

      if (checked) {
        if (isOriginallyChecked) {
          newRemove.delete(permission?.id as any);
        } else {
          newAdd.add(permission?.id as any);
        }
      } else {
        if (isOriginallyChecked) {
          newRemove.add(permission?.id as any);
        } else {
          newAdd.delete(permission?.id as any);
        }
      }

      const newChanges = { ...prev, [role.id]: { add: newAdd, remove: newRemove } };

      // Clean up empty change sets
      if (newAdd.size === 0 && newRemove.size === 0) {
        delete newChanges[role.id];
      }

      return newChanges;
    });
  };

  const handleSavePermissions = (role: IRole) => {
    showConfirm(ConfirmEditText(), {
      async onConfirm() {
        setIsSaving(true);
        const changes = pendingChanges[role.id];

        const currentPermIds = new Set(role.permissions.map((p) => p.id));

        // Apply changes
        changes.add.forEach((id) => currentPermIds.add(id as any));
        changes.remove.forEach((id) => currentPermIds.delete(id as any));

        // Convert to permission names for the API
        const newPermissions = permissions?.filter((p) => currentPermIds.has(p.id)).map((p) => p.name) || [];

        updateRole(
          {
            id: role.id,
            payload: {
              name: role.name,
              permissions: newPermissions,
            },
          },
          {
            onSuccess: () => {
              setPendingChanges((prev) => {
                const newState = { ...prev };
                delete newState[role.id];
                return newState;
              });
              setIsSaving(false);
              hideConfirm();
            },
            onError: () => {
              setIsSaving(false);
              hideConfirm();
            },
          }
        );
      },
    });
  };

  // Check if there are pending changes for a role
  const hasPendingChanges = (roleId: string) => {
    const changes = pendingChanges[roleId];
    return changes && (changes.add.size > 0 || changes.remove.size > 0);
  };

  return (
    <div className="relative mb-10">
      <div className="border border-border">
        <div className="p-4 flex items-center justify-between">
          {' '}
          <div className="bg-background">
            <div className="flex gap-3">
              {tabs?.map((tab: any, index: any) => (
                <Button
                  variant={'outline'}
                  className={`min-w-[120px] hover:text-primary hover:bg-semi-primary ${
                    location.pathname.substring(3) === tab.route ? 'text-primary bg-semi-primary' : ''
                  }`}
                  key={tab.route}
                  onClick={() => navegate(tab.route)}
                >
                  {t(tab.tab)}
                </Button>
              ))}
            </div>
          </div>
          <LanguageLink to="/app/roles/create">
            <Button>{t('roles.addRole')} </Button>
          </LanguageLink>
        </div>
        <div className="min-h-[200px] rounded-lg border border-border md:min-w-[450px] grid bg-background">
          <div className="h-full relative">
            <ResizablePanelGroup direction="horizontal" className="h-full">
              <ResizablePanel defaultSize={50}>
                <div className="flex items-center justify-center h-16 px-3 border-border border-b">
                  <span className="font-semibold">{t('roles.permissionName')}</span>
                </div>
                {permissions?.map((permission) => (
                  <div key={permission.id} className="h-11 px-3 border-border border-b flex items-center">
                    {permission.name}
                  </div>
                ))}
              </ResizablePanel>
              {roles?.map((role) => (
                <>
                  <ResizableHandle />
                  <ResizablePanel defaultSize={50}>
                    <div className="flex items-center justify-between h-16 px-3 py-3 border-border border-b">
                      <div>
                        <div className="text-sm">{t('roles.role')}</div>
                        <div className="font-semibold">{t(`roles.${role.name}`)}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        {hasPendingChanges(role.id) && (
                          <Button size="sm" onClick={() => handleSavePermissions(role)} disabled={isSaving}>
                            {isSaving ? (
                              <Icon className="h-4 w-4 animate-spin" icon="mingcute:loading-fill" />
                            ) : (
                              t('save')
                            )}
                          </Button>
                        )}
                        <DropdownMenu>
                          {role.name !== 'super_admin' && (
                            <DropdownMenuTrigger className="self-start">
                              <Icon icon="bi:three-dots-vertical" />
                            </DropdownMenuTrigger>
                          )}
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="flex gap-2"
                              onClick={() => navigate(`/app/roles/edit/${role.id}`)}
                            >
                              <Icon icon="solar:pen-new-square-linear" width={16} className="text-primary" />
                              {t('edit')}
                            </DropdownMenuItem>

                            <DropdownMenuItem className="flex gap-2" onClick={() => handleDeleteRole(role.id)}>
                              <Icon icon="solar:trash-bin-minimalistic-broken" width={18} className="text-primary" />
                              {t('delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                    {permissions?.map((permission) => (
                      <div
                        key={permission.id}
                        className="h-11 px-3 text-center border-border border-b flex items-center justify-center"
                      >
                        <Checkbox
                          disabled={role.name === 'super_admin' || isSaving}
                          className="mt-0.5"
                          checked={isChecked(role, permission)}
                          onCheckedChange={(checked) => handleTogglePermission(checked as boolean, role, permission)}
                        />
                      </div>
                    ))}
                  </ResizablePanel>
                </>
              ))}
            </ResizablePanelGroup>
          </div>
        </div>
      </div>
    </div>
  );
};

export default List;
