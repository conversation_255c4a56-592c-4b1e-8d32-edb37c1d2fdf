import { useState } from 'react';
import { Modal, useConfirmDialog, Icon, Form, TextInput, useForm, Card, useValidate } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useConfirmDnaGeneratedByAiMagic, useGenerateMagicDna } from '../apis/queries';
import { IDNA } from '../types';
import { Badge } from '@/components/ui/badge';
import { Editor } from 'src/components/CKEditor';
interface Props {
  onOpen: boolean;
  onClose: () => void;
  data: IDNA;
}

const EditDna = ({ onOpen, onClose, data }: Props) => {
  const { showConfirm, hideConfirm } = useConfirmDialog();
  //  State
  const [dnaText, setDnaText] = useState(data.dna_content);
  const [fullAiMagicResponse, setFullAiMagicResponse] = useState(null);
  const [aiGenerateDna, setAIGenerateDna] = useState(null);

  // Hooks
  const { t } = useTranslation();
  const commentsExamples = [
    { ar: '', en: 'Please include more recent advancements in ...' },
    { ar: '', en: 'Please include more examples ...' },
    { ar: '', en: 'Please provide more information ...' },
  ];

  const { mutate: aiGenerate, isPending: aiPending } = useGenerateMagicDna();
  const { mutate: confirmDna, isPending: isConfirming } = useConfirmDnaGeneratedByAiMagic();

  const { form, setFieldValue } = useForm({
    comments: '',
  });
  const { isRequired } = useValidate();
  // Edit Dna
  const handleEdit = async () => {
    aiGenerate(
      { id: data.id, comment: form.comments },
      {
        onSuccess: (res) => {
          setDnaText(res?.sentence);
          setFullAiMagicResponse(res);
          setAIGenerateDna(res);
        },
      }
    );
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationEdit')}</p>
      </div>
    );
  };

  const ConfirmSaveText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>Changing the DNA will update the current status to 'Draft.' Are you sure you want to proceed?</p>
      </div>
    );
  };

  const handleClose = async () => {
    showConfirm(data?.dna_status?.phase_with_status === 'Edit: Ready for Review' ? ConfirmSaveText() : ConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        onClose();
      },
    });
  };

  const handleConfirmDna = async () => {
    confirmDna(
      { id: data.id, payload: fullAiMagicResponse },
      {
        onSuccess: () => {
          onClose();
        },
      }
    );
  };

  return (
    <Modal
      width={1500}
      open={onOpen}
      onOpenChange={dnaText === data.dna_content ? onClose : handleClose}
      modalHeader={t('dnaModal.aiMagic')}
    >
      <div>
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-5">
            <div>
              <h5 className="mb-2 font-medium">{t('oldDnaContent')}</h5>
              <Card>
                <Editor editorContent={data.dna_content} language={data.language} readOnly />
              </Card>
            </div>
            <div>
              <h5 className="mb-2 font-medium">{t('preview')}</h5>

              {fullAiMagicResponse ? (
                <Card className="">
                  <Editor editorContent={dnaText} language={data.language} readOnly />{' '}
                </Card>
              ) : (
                <div className="h-full flex flex-col justify-center items-center">
                  <img src="/empty-activity.svg" className="" />

                  <div className="flex flex-col gap-2 my-5 ml-6">
                    <h1 className="self-center text-2xl">{t('empty.aiMagicTitle')}</h1>
                    <p className="self-center text-slate-500">{t('empty.aiMagicDescription')}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
          {
            <Form onSubmit={handleEdit}>
              <Card className="p-3 space-y-3 mb-3">
                <TextInput
                  name="comments"
                  label={t('dnaCreationPage.form.comments')}
                  placeholder={t('dnaCreationPage.form.comments')}
                  value={form.comments}
                  onChange={setFieldValue('comments')}
                  validators={[isRequired()]}
                />
                <div className="flex gap-2">
                  {commentsExamples.map((comment, index) => (
                    <Badge
                      key={index}
                      onClick={(e) => setFieldValue('comments')(comment.en)}
                      variant={'outline'}
                      className="px-4 py-1.5 text-nowrap cursor-pointer hover:text-primary hover:bg-semi-primary font-medium"
                    >
                      {data?.language.name_en === 'English' ? comment.en : comment.ar}
                    </Badge>
                  ))}
                </div>
              </Card>
              <div className="flex justify-end gap-2">
                <Button
                  variant={'outline'}
                  loading={aiPending}
                  disabled={aiPending}
                  className="min-w-[150px] rtl:order-last flex items-center gap-1"
                  type="submit"
                >
                  <Icon icon="bi:stars" width="18" className="text-primary" />
                  {t('generate.aiMagic')}
                </Button>
                <Button
                  onClick={handleConfirmDna}
                  loading={isConfirming}
                  disabled={isConfirming || !fullAiMagicResponse}
                  className="min-w-[150px] rtl:order-last"
                  type="button"
                >
                  {t('confirm')}
                </Button>
              </div>
            </Form>
          }
        </div>
      </div>
    </Modal>
  );
};

export default EditDna;
