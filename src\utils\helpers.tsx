import * as XLSX from 'xlsx';
import { marked } from 'marked';
import { IStatus, ISystemModuleStatus } from '@/modules/app/dashboard/modules-status/types';
import StatusClass from './SystemStatusEnums';
import i18n from './i18n';
import { cn } from '@/lib/utils';
import moment from 'moment';
import momentTz from 'moment-timezone';

import { INotification, NotificationURL } from '@/modules/app/notification/types';
import {
  Paragraph,
  Table as DocxTable,
  TableCell,
  TableRow,
  TextRun,
  Packer,
  Document,
  AlignmentType,
  WidthType,
  ImageRun,
} from 'docx';
import { saveAs } from 'file-saver';
import { IUser } from '@/modules/auth/types';
import { NotificationsEnums } from '@/index';

export const formatDateByYear = (data: Date | string | null, lang: string) => {
  if (!data) return 'N/A';
  const dateStr = data;
  const date = new Date(dateStr);
  const year = date.getFullYear();
  const month = date.toLocaleString(lang === 'ar' ? 'ar-EG' : 'en-US', { month: 'short' });
  const day = date.getDate().toString().padStart(2, '0');
  const formattedDate = `${day} ${month} ${year} `;
  return formattedDate;
};
//time
export const formatDateByHour = (data: Date | string | null, lag: string): string => {
  if (!data) return 'N/A';
  // Ensure the input data is converted to a Date object
  const date = new Date(data);
  // Define formatting options with type safety
  const options: Intl.DateTimeFormatOptions = {
    timeZone: 'Asia/Riyadh', // Time zone for Saudi Arabia
    hour: '2-digit', // Correct type
    minute: '2-digit', // Correct type
    hour12: false, // Correct type
  };

  // Format the time using Intl.DateTimeFormat
  const formattedTime = new Intl.DateTimeFormat('en-US', options).format(date);

  return formattedTime;
};

export const dateAndTimeFormat = (data: string, language: string, className?: string) => {
  return (
    <div className={`${className}`}>
      <p>{formatDateByYear(data, language)}</p>
      <p>{formatDateByHour(data, language)}</p>
    </div>
  );
};

export const handelExportToExcel = (selectedRows: string[], list: any[]) => {
  // Add HTML to text converter
  const convertHtmlToText = (html: string) => {
    if (!html) return '';
    const tempElement = document.createElement('div');
    tempElement.innerHTML = html;
    return tempElement.textContent || tempElement.innerText || '';
  };
  // Filter list to only get selected rows
  const filteredList = list.filter((item) => selectedRows.includes(item.id));

  const dataToExport = filteredList.map((row: any) => {
    const filteredRow: any = {};

    const columns = [
      {
        key: 'title',
        label: 'DNA Title',
      },
      {
        key: 'learning_objectives',
        label: 'learning_objectives',
      },
      {
        key: 'audio_length',
        label: 'Length',
      },
      {
        key: 'created_at',
        label: 'Date',
      },
    ];

    columns.forEach((column: any) => {
      const value = row[column.key];
      filteredRow[column.label] = value;
    });
    filteredRow['DNA'] = convertHtmlToText(row['dna_content']);
    filteredRow['user'] = row['user']['name'];
    filteredRow['status'] = row['dna_status']['phase_with_status'];
    filteredRow['subject'] = row['subject']['name_en'];
    filteredRow['bloom_tax'] = row['bloom_tax']['name_en'];

    return filteredRow;
  });

  const worksheet = XLSX.utils.json_to_sheet(dataToExport);
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
  XLSX.writeFile(workbook, 'exported_data.xlsx');
};

const getBase64ImageFromURL = async (url: string): Promise<string> => {
  const response = await fetch(url);
  const blob = await response.blob();

  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      const result = reader.result?.toString().split(',')[1];
      if (result) {
        resolve(result);
      } else {
        reject('Failed to load image');
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

export const handelExportToWord = async (editorContent: string, filename: string, language: string) => {
  const isRTL = ['ar', 'he', 'fa', 'ur'].includes(language);
  const alignment = isRTL ? AlignmentType.RIGHT : AlignmentType.LEFT;

  const parser = new DOMParser();
  const doc = parser.parseFromString(editorContent, 'text/html');

  const elements: any[] = [];

  const convertTable = (table: HTMLTableElement) => {
    const rows: TableRow[] = [];

    for (const row of Array.from(table.rows)) {
      const cells: TableCell[] = [];

      for (const cell of Array.from(row.cells)) {
        const text = cell.textContent?.replace(/\u00a0/g, '').trim() || '';
        cells.push(
          new TableCell({
            children: [
              new Paragraph({
                children: [
                  new TextRun({
                    text,
                    size: 28,
                  }),
                ],
                alignment,
              }),
            ],
            width: {
              size: 100 / row.cells.length,
              type: WidthType.PERCENTAGE,
            },
          })
        );
      }

      rows.push(new TableRow({ children: cells }));
    }

    return new DocxTable({ rows });
  };

  for (const node of Array.from(doc.body.childNodes)) {
    if (node.nodeName === 'P') {
      const text = node.textContent?.trim();
      if (text) {
        elements.push(
          new Paragraph({
            children: [
              new TextRun({
                text,
                size: 28,
              }),
            ],
            alignment,
          })
        );
        elements.push(new Paragraph({}));
      }
    } else if (node.nodeName === 'FIGURE') {
      const figure = node as HTMLElement;

      const table = figure.querySelector('table');
      if (table) {
        elements.push(convertTable(table));
        elements.push(new Paragraph({}));
      }

      const img = figure.querySelector('img');
      if (img && img.src) {
        const width = parseInt(img.getAttribute('width') || '600');
        const height = parseInt(img.getAttribute('height') || '400');

        try {
          const base64 = await getBase64ImageFromURL(img.src);
          elements.push(
            new Paragraph({
              children: [
                new ImageRun({
                  data: base64, // Directly using the base64 string here
                  transformation: {
                    width: Math.min(width, 600), // Set a maximum width of 600px (adjust as needed)
                    height: Math.min(height, 400), // Set a maximum height of 400px (adjust as needed)
                  },
                }),
              ],
              alignment,
              spacing: { after: 200 }, // Adjust spacing after the image, value is in twips (1/1440 inch)
            })
          );
          elements.push(new Paragraph({}));
        } catch (err) {
          console.error('Image fetch error:', err);
        }
      }
    }
  }

  const wordDoc = new Document({
    sections: [
      {
        properties: {},
        children: elements,
      },
    ],
  });

  const blob = await Packer.toBlob(wordDoc);
  saveAs(blob, `${filename}.docx`);
};

export const generateEnum = (data: any[], valueKey = 'value', labelKey = 'label') => {
  return data?.map((item: any) => ({ value: item[valueKey], label: item[labelKey] })) || [];
};

const statusStyles: Record<string, string> = {
  [StatusClass.DNA.EDIT.NO_CONTENT]: 'bg-gray-100 text-gray-900',
  [StatusClass.DNA.EDIT.DRAFT]: 'bg-gray-100 text-gray-900',
  [StatusClass.DNA.EDIT.RFR]: 'bg-[#DEF7EC] text-[#046C4E]',
  [StatusClass.DNA.EDIT.APPROVED]: 'bg-[#84E1BC] text-[#03543F]',
  [StatusClass.DNA.EDIT.FEEDBACK]: 'bg-yellow-100 text-yellow-800',
  [StatusClass.DNA.REVIEW.DRAFT]: 'bg-gray-100 text-gray-900',
  [StatusClass.DNA.REVIEW.PRODUCTION]: 'bg-[#DEF7EC] text-[#046C4E]',
  [StatusClass.DNA.REVIEW.APPROVED]: 'bg-[#84E1BC] text-[#03543F]',
  [StatusClass.DNA.REVIEW.FEEDBACK]: 'bg-yellow-100 text-yellow-800',
};

export const getContentStatusStyle = (status: IStatus | null | undefined) => {
  if (!status) return '__';
  if (status?.background_color && status?.text_color) {
    return (
      <div
        style={{ backgroundColor: status?.background_color, color: status?.text_color }}
        className="px-4 text-center py-1.5 rounded-md whitespace-nowrap w-fit"
      >
        {handleStatusConversion(status, i18n.language)}
      </div>
    );
  }

  return (
    <div
      className={cn(
        'px-4 text-center py-1.5 rounded-md whitespace-nowrap w-fit',
        statusStyles[status.phase_with_status] || 'bg-gray-500/10 text-gray-500'
      )}
    >
      {handleStatusConversion(status, i18n.language)}
    </div>
  );
};

const courseGroupStatusStyles: Record<string, string> = {
  [StatusClass.COURSE_GROUP.NOT_STARTED]: 'bg-gray-100 text-gray-900',
  [StatusClass.COURSE_GROUP.IN_PROGRESS]: 'bg-indigo-100 text-indigo-800',
  [StatusClass.COURSE_GROUP.COMPLETED]: 'bg-green-300 text-green-800',
};

export const getCourseGroupStatusStyle = (status: IStatus | undefined) => {
  if (!status) return '__';

  return (
    <div
      className={cn(
        'px-4 text-center py-1.5 rounded-md whitespace-nowrap w-fit',
        courseGroupStatusStyles[status.phase_with_status] || 'bg-gray-500/10 text-gray-500'
      )}
    >
      {handleStatusConversion(status, i18n.language)}
    </div>
  );
};

const taskStatusStyles: Record<string, string> = {
  [StatusClass.TASK.REVIEW.OPEN]: 'bg-gray-100 text-gray-900',
  [StatusClass.TASK.REVIEW.REOPEN]: 'bg-gray-100 text-gray-900',
  [StatusClass.TASK.REVIEW.IN_PROGRESS]: 'bg-indigo-100 text-indigo-800',
  [StatusClass.TASK.REVIEW.NOT_AVAILABLE]: 'bg-gray-100 text-gray-900',
  [StatusClass.TASK.REVIEW.COMPLETED]: 'bg-green-300 text-green-800',
  [StatusClass.TASK.REVIEW.DECLINED]: 'bg-red-100 text-red-800',
  [StatusClass.TASK.REVIEW.FEEDBACK]: 'bg-yellow-100 text-yellow-800',
  [StatusClass.TASK.REVIEW.OVERDUE]: 'bg-red-100 text-red-800',
  [StatusClass.TASK.REVIEW.OVERDUE_NO_RESPONSE]: 'bg-red-100 text-red-800',
};

export const getTaskStatusStyle = (status: IStatus) => {
  if (status?.background_color && status?.text_color) {
    return (
      <div
        style={{ backgroundColor: status?.background_color, color: status?.text_color }}
        className="px-4 text-center py-1.5 rounded-md whitespace-nowrap w-fit"
      >
        {handleStatusConversion(status, i18n.language)}
      </div>
    );
  }
  return (
    <div
      className={cn(
        'px-4 text-center py-1.5 rounded-md whitespace-nowrap w-fit',
        taskStatusStyles[status?.phase_with_status] || 'bg-gray-500/10 text-gray-500'
      )}
    >
      {handleStatusConversion(status, i18n.language)}
    </div>
  );
};

export const getLogStatus = (status: string) => {
  switch (status) {
    case 'updated':
      return 'bg-green-700/10 text-teal-700'; // Dark teal for updated status
    case 'deleted':
      return 'bg-red-500/10 text-red-700'; // Dark red for deleted status
    case 'created':
      return 'bg-blue-700/10 text-blue-700'; // Dark blue for created status
    default:
      return 'bg-gray-500/10 text-gray-500'; // Default gray for unspecified statuses
  }
};

export const getTransalationStatus = (status: string) => {
  switch (status) {
    case 'reviewed':
      return 'bg-green-700/10 text-teal-700'; // Dark teal for updated status
    default:
      return 'bg-gray-500/10 text-gray-500'; // Default gray for unspecified statuses
  }
};

export const getSlideImage = (images: { uuid: string; url: string }[], id: string) => {
  return images.find((image: { uuid: string }) => image.uuid === id)?.url;
};

// Type guard to check if a key is a name field
export const isNameField = (key: string): boolean => {
  return key.startsWith('name_');
};

// Get the language code from the name field
export const getLanguageFromField = (field: string): string => {
  return field.replace('name_', '');
};

export const getAllNames = (meta: IMetadata, language: string) => {
  if (!meta) return '_';

  // const names = Object.entries(meta)
  //   .filter(([key, value]) => {
  //     return isNameField(key) && value !== undefined;
  //   })
  //   .map(([key, value]) => {
  //     const language = getLanguageFromField(key);
  //     return {
  //       language,
  //       name: value as string,
  //     };
  //   });
  const name = language === 'en' ? meta?.name_en : meta?.name_ar;
  return <p className="rtl:text-right">{name}</p>;
};

export const handleStatusConversion = (statue: any, language: string) => {
  const module = language === 'en' ? statue?.phase?.name_en : statue?.phase?.name_ar;
  const ModuleStatue = language === 'en' ? statue?.name_en : statue?.name_ar;
  return module + ': ' + ModuleStatue;
};

export const convertStatusToFilterEnumByTitle = (status: ISystemModuleStatus[], title: string, language: string) => {
  if (!status || !title || !language) return [];

  const selectedModule = status.find((item) => item.title === title);
  if (!selectedModule) return [];

  const enums = selectedModule.phases.flatMap((phase) =>
    phase.status.map((smallStatus) => {
      const selectedName = language.startsWith('en') ? smallStatus.name_en : smallStatus.name_ar;
      const selectedPhase = language.startsWith('en') ? phase.name_en : phase.name_ar;
      return {
        label: selectedPhase + ' : ' + selectedName,
        value: smallStatus.id,
      };
    })
  );
  return enums || [];
};

// Download Dna
export const parseMarkdownToDocx = (markdownContent: string) => {
  const docElements: any = [];
  const lines = markdownContent.split('\n');
  lines.forEach((line: string) => {
    const tokens = marked.lexer(line);
    tokens.forEach((token: any) => {
      switch (token.type) {
        case 'heading':
          docElements.push(
            new Paragraph({
              text: token.text,
              heading: `heading${token.depth}` as any,
            })
          );
          break;
        case 'paragraph':
          docElements.push(
            new Paragraph({
              children: [
                new TextRun({
                  text: token.text,
                  size: 30,
                }),
              ],
            })
          );
          docElements.push(new Paragraph({}));
          break;
        case 'list':
          token.items.forEach((item: any) => {
            docElements.push(
              new Paragraph({
                text: item,
                bullet: {
                  level: 0,
                },
              })
            );
          });
          break;
      }
    });
  });

  return docElements;
};

export const localizePresenter = (localizedObject: { name_ar: string; name_en: string }, language: string) => {
  return language.includes('ar') ? (
    <div className="text-start">{localizedObject?.name_ar}</div>
  ) : (
    <div>{localizedObject?.name_en}</div>
  );
};

export const formatNotificationDate = (timestamp: string) => {
  const notificationDate = moment(timestamp);

  if (moment().diff(notificationDate, 'hours') >= 12) {
    return notificationDate.format('DD/MM/YYYY, h:mm:ss A'); // Example: 3/5/2025, 1:46:21 PM
  } else {
    return notificationDate.fromNow(); // Example: "3 hours ago", "moments ago"
  }
};

export const handleNotificationRouting = (notification: INotification) => {
  const notificationType = NotificationsEnums.urls.find(
    (urlType: NotificationURL) => urlType.value === notification.type
  );
  if (notificationType) {
    return notificationType.url(notification);
  }
  return `/app/my-content`;
};

export const formatDateByYearAndHour = (data: string, language: string) => {
  if (!data) return '';

  const date = momentTz(data).tz('Asia/Riyadh');
  if (!date.isValid()) return 'N/A';

  const locale = language === 'ar' ? 'ar' : 'en';
  date.locale(locale);

  const formattedDate = date.format('DD MMM YYYY');
  const formattedTime = date.format('HH:mm');

  return `${formattedDate}\n${formattedTime}`;
};

export const formatUserByNameAndEmail = (user: Pick<IUser, 'name' | 'email'>) => {
  return (
    <div>
      <p>{user?.name}</p>
      <p>{user?.email}</p>
    </div>
  );
};

export const faultyValue: {
  (value: unknown): boolean;
} = (value) => {
  return (
    value === "" ||
    value === null ||
    value === undefined ||
    String(value).toLocaleLowerCase() === "undefined" ||
    String(value).toLocaleLowerCase() === "null" ||
    JSON.stringify(value) === "{}" ||
    JSON.stringify(value) === "[]"
  );
};
