import { Icon } from '@/components';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';
import { IMedia } from '@/modules/app/common/types';
import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from './empty-state';
import SkeletonGrid from './skeleton-grid';

const MediaGallery = ({
  media,
  isLoading,
  selectedImage,
  onCheckboxChange,
}: {
  media: any;
  isLoading: boolean;
  selectedImage: IMedia | null;
  onCheckboxChange: (item: IMedia) => void;
}) => {
  // Hooks
  const { t } = useTranslation();

  if (isLoading) return <SkeletonGrid />;
  if (media?.length === 0) return <EmptyState />;
  return (
    <ScrollArea className="h-[500px] pe-3">
      <div className="grid grid-cols-[repeat(auto-fill,minmax(180px,1fr))] gap-3 overflow-y-auto">
        {media?.map((item: IMedia) => (
          <Label className="relative group" key={item.uuid}>
            <div
              className={cn(
                'w-full border overflow-hidden rounded-md cursor-pointer',
                selectedImage?.uuid === item.uuid && 'border-primary'
              )}
            >
              {/* Removed query parameter from src to prevent unnecessary reloads */}
              <img src={`${item.url}?${item.uuid}`} className="h-28 w-full object-cover" alt="Image" />
              <div className="pb-3 px-2 pt-2 border-t border-border text-sm">{item.name || t('media.noName')}</div>
            </div>
            <Checkbox
              className={cn(
                'absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer size-5 rounded-full',
                selectedImage?.uuid === item.uuid && 'opacity-100'
              )}
              checked={selectedImage?.uuid === item.uuid}
              onCheckedChange={() => onCheckboxChange(item)}
              // Prevent focus to avoid scroll jump
              tabIndex={-1}
            />
          </Label>
        ))}
      </div>
    </ScrollArea>
  );
};

export default memo(MediaGallery);
