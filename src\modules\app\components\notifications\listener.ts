import { useEffect } from 'react';
import pusher from './pusher-config';

interface IListenerProps {
  channelName: string;
  eventName: string;
  evenCallback: (data: any) => void;
}

const useListener = ({ channelName, eventName, evenCallback }: IListenerProps) => {
  useEffect(() => {
    if (!channelName) return;

    const channel = pusher.subscribe(channelName);

    pusher.connection.bind('error', (err: any) => {
      console.error('Pusher connection error:', err);
    });
    channel.bind(eventName, evenCallback);

    return () => {
      channel.unbind(eventName);
      pusher.unsubscribe(channelName);
    };
  }, [channelName]);

  return null;
};

export { useListener };
