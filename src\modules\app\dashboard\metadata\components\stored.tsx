import { useMemo } from 'react';
import { Modal } from '@/index';
import { useRestoreMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { useFetchList, Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { Table, TableContentBody, TableContent, TablePagination } from '@/components/theTable';
import { Button } from '@/components/ui/button';

const StoredMetaDataDialog = ({ isOpen, onOpenChange, refreshTable }: any) => {
  const { t } = useTranslation();
  const { loading, list, count, refresh, pagination } = useFetchList('/types/only-trashed', 'trashed-keys', {
    filters: {
      type: {
        enum: 'metadata',
        placeholder: 'metadata.filters.selectType',
      },
    },
  });
  const { mutate: restoreMetadata, isPending, variables } = useRestoreMetadata();

  const columns: ITableColumn<IMetadata>[] = useMemo(() => {
    return [
      {
        accessorKey: 'id',
        header: 'Id',
        width: '50px',
      },
      {
        accessorKey: 'type',
        header: 'Type',
        width: '150px',
      },
      {
        accessorKey: 'name_ar',
        header: 'Name_ar',
        width: '130px',
      },
      {
        accessorKey: 'name_en',
        header: 'Name_en',
        width: '130px',
      },
      {
        accessorKey: 'description',
        header: 'Description',
        width: '400px',
        cell: ({ row }) => {
          return <div className="text-start truncate">{row.description}</div>;
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '80px',
        cell: ({ row }) => {
          return (
            <div>
              <Button
                loading={isPending && row.id === variables}
                disabled={isPending}
                variant="ghost"
                size={'icon'}
                className="bg-white dark:bg-[#09090B]"
              >
                <Icon
                  onClick={() =>
                    restoreMetadata(row.id.toString(), {
                      onSuccess: () => {
                        refreshTable(), refresh();
                      },
                    })
                  }
                  icon="material-symbols-light:restore-from-trash-outline-rounded"
                  width={30}
                  className="text-primary"
                />
              </Button>
            </div>
          );
        },
      },
    ];
  }, []);

  return (
    <Modal width={1500} open={isOpen} onOpenChange={onOpenChange} modalHeader={t('Hidden Data')}>
      <div className="h-[800px] overflow-y-auto">
        <Table rows={list} columns={columns} loading={loading}>
          <TableContent>
            <TableContentBody />
            <TablePagination count={count} pagination={pagination} />
          </TableContent>
        </Table>
      </div>
    </Modal>
  );
};

export default StoredMetaDataDialog;
