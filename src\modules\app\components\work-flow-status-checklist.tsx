import { Icon } from '@/components';
import { ProtectedTaskComponent } from '../tasks/components/protected-task-component';
import { IDNA } from '../dna/types';
import { useTranslation } from 'react-i18next';
import { IActivityQuestion } from '../tasks/types';
import StatusClass from '@/utils/SystemStatusEnums';

interface StatusItemProps {
  condition: boolean;
  shouldShow: boolean;
  label: string;
  permission?: string;
}

interface ChecklistItemProps {
  item: StatusItemProps;
  isApproved: boolean;
  isFeedBack: boolean;
}

const ChecklistItem = ({ item, isApproved, isFeedBack }: ChecklistItemProps) => {
  const getItemStyles = () => {
    if (isApproved) return 'text-green-500';
    if (isFeedBack) return 'text-yellow-500';
    return 'opacity-30';
  };

  return (
    <div className={`${getItemStyles()} flex gap-2 mt-3 items-center`}>
      <Icon width="22" className="mt-1" icon="material-symbols:check-circle-outline-rounded" />
      <span>{item.label}</span>
    </div>
  );
};

const WorkflowStatusChecklist = ({ dna, needsPermissions = true }: { dna: IDNA; needsPermissions?: boolean }) => {
  const { t } = useTranslation();

  const checkApprovalStatus = {
    translations: () => {
      return dna?.production_indicator?.translation === true;
    },

    activities: () => {
      return dna?.production_indicator?.activity === true;
    },

    slideshow: () => {
      return dna?.production_indicator?.slideshow === true;
    },
  };

  const statusItems: StatusItemProps[] = [
    {
      condition:
        dna?.dna_status?.phase_with_status === StatusClass.DNA.REVIEW.APPROVED ||
        dna?.dna_status?.phase_with_status === StatusClass.DNA.REVIEW.PRODUCTION,
      label: t('breadcrumb.myContentPage.my-content'),
      shouldShow: dna?.dna_status?.phase?.action !== 'Production',
    },
    {
      condition: checkApprovalStatus.activities(),
      label: t('dnaSinglePage.tabs.activity'),
      permission: 'dna_activity_show',
      shouldShow: true,
    },
    {
      condition: checkApprovalStatus.translations(),
      label: t('dashboardPage.keys.newKeyDialog.translation'),
      permission: 'translation_show',
      shouldShow: true,
    },
    {
      condition: checkApprovalStatus.slideshow(),
      label: t('dnaSinglePage.tabs.slideShow'),
      permission: 'dna_slideshow_show',
      shouldShow: true,
    },
  ];

  const renderItem = (item: StatusItemProps, index: number) => {
    const isFeedBack = dna?.dna_status?.phase_with_status === StatusClass.DNA.REVIEW.FEEDBACK;
    const itemElement = <ChecklistItem key={index} item={item} isApproved={item.condition} isFeedBack={isFeedBack} />;

    return needsPermissions ? (
      <ProtectedTaskComponent key={index} requiredPermissions={item.permission || 'public'}>
        {itemElement}
      </ProtectedTaskComponent>
    ) : (
      itemElement
    );
  };

  return (
    <div className="flex gap-3">
      {statusItems.filter((item) => item.shouldShow).map((item, index) => renderItem(item, index))}
    </div>
  );
};

export default WorkflowStatusChecklist;
