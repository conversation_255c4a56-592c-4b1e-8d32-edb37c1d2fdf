import { Icon } from '@/components';
import { ProtectedTaskComponent } from '../tasks/components/protected-task-component';
import { IDNA } from '../dna/types';
import { useTranslation } from 'react-i18next';
import { IActivityQuestion } from '../tasks/types';
import StatusClass from '@/utils/SystemStatusEnums';
import { useParams } from 'react-router-dom';
import { useListSlideshow } from '../tasks/apis/queries';

interface StatusItemProps {
  condition: boolean;
  shouldShow: boolean;
  label: string;
  permission?: string;
}

interface ChecklistItemProps {
  item: StatusItemProps;
  isApproved: boolean;
  isFeedback: boolean;
}

const ChecklistItem = ({ item, isApproved, isFeedback }: ChecklistItemProps) => {
  const getItemClasses = () => {
    if (isFeedback) {
      return 'text-yellow-500';
    }
    return `${isApproved ? 'text-green-500' : 'opacity-30'}`;
  };

  return (
    <div className={`flex items-center gap-2 mt-3 ${getItemClasses()}`}>
      <Icon width="22" className="mt-1" icon="material-symbols:check-circle-outline-rounded" />
      <span>{item.label}</span>
    </div>
  );
};

const WorkflowStatusChecklist = ({ dna, needsPermissions = true }: { dna: IDNA; needsPermissions?: boolean }) => {
  const { t } = useTranslation();

  const isFeedbackMode = dna?.dna_status?.action === 'feedback';

  const checkApprovalStatus = {
    translations: () => {
      const translations = dna?.content_translations;
      return translations?.length ? translations.every((translation) => translation.status === 'reviewed') : false;
    },
    activities: () => {
      const activities = dna?.activities;
      return activities && Object.keys(activities).length
        ? Object.values(activities).every((array) =>
            array?.every((item: IActivityQuestion) => item.status === 'reviewed')
          )
        : false;
    },
  };

  const statusItems: StatusItemProps[] = [
    {
      condition:
        dna?.dna_status?.phase_with_status === StatusClass.DNA.REVIEW.APPROVED ||
        dna?.dna_status?.phase_with_status === StatusClass.DNA.REVIEW.PRODUCTION,
      label: t('breadcrumb.myContentPage.my-content'),
      shouldShow: dna?.dna_status?.phase?.action !== 'Production',
    },
    {
      condition: checkApprovalStatus.activities(),
      label: t('dnaSinglePage.tabs.activity'),
      permission: 'dna_activity_show',
      shouldShow: true,
    },
    {
      condition: checkApprovalStatus.translations(),
      label: t('dashboardPage.keys.newKeyDialog.translation'),
      permission: 'translation_show',
      shouldShow: true,
    },
  ];

  const renderItem = (item: StatusItemProps, index: number) => {
    let firstUnapprovedFound = false;

    const isFirstFeedbackItem = isFeedbackMode && !item.condition && !firstUnapprovedFound;

    if (isFirstFeedbackItem) {
      firstUnapprovedFound = true;
    }

    const itemElement = (
      <ChecklistItem key={index} item={item} isApproved={item.condition} isFeedback={isFirstFeedbackItem} />
    );

    return needsPermissions ? (
      <ProtectedTaskComponent key={index} requiredPermissions={item.permission || 'public'}>
        {itemElement}
      </ProtectedTaskComponent>
    ) : (
      itemElement
    );
  };

  return (
    <div className="flex gap-3">
      {statusItems.filter((item) => item.shouldShow).map((item, index) => renderItem(item, index))}
    </div>
  );
};

export default WorkflowStatusChecklist;
