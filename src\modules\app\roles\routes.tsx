import { lazy } from 'react';
import { ProtectedRoute } from '@/components';

const Rolescreate = lazy(() => import('./pages/create'));
const RolesEdit = lazy(() => import('./pages/edit'));
const RolesNewPage = lazy(() => import('./pages/list'));
export default [
  {
    path: 'roles-new',
    element: <RolesNewPage />,
    loader() {
      return {
        label: 'breadcrumb.userPage.roles',
        title: 'breadcrumb.userPage.roles',
      };
    },
  },
  {
    path: 'roles',
    element: (
      <ProtectedRoute requiredPermissions={'role_show'}>
        <RolesNewPage />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.roles',
        title: 'breadcrumb.userPage.roles',
      };
    },
  },
  {
    path: 'roles/create',
    element: (
      <ProtectedRoute requiredPermissions={'role_create'}>
        <Rolescreate />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.role',
        title: 'breadcrumb.userPage.role',
      };
    },
  },
  {
    path: 'roles/edit/:id',
    element: (
      <ProtectedRoute requiredPermissions={'role_edit'}>
        <RolesEdit />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.role',
        title: 'breadcrumb.userPage.role',
      };
    },
  },
];
