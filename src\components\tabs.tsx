import { t } from 'i18next';
import { Button } from './ui/button';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Separator } from './ui/separator';

export interface ITab {
  label: string;
  value: string;
  permission?: string | string[];
  subText?: boolean;
  blocked?: boolean;
  component?: JSX.Element;
}

interface TabsProps {
  tabs: ITab[];
  defaultTab?: string;
  useUrlParams?: boolean;
  paramName?: string;
}

const Tabs = ({ tabs, defaultTab, useUrlParams = false, paramName = 'tab' }: TabsProps) => {
  const [searchParams, setSearchParams] = useSearchParams();

  const tabComponents: Record<string, JSX.Element | undefined> = {};
  tabs.forEach((tab) => {
    if (tab.component) {
      tabComponents[tab.value] = tab.component;
    }
  });

  const initialTab = () => {
    if (useUrlParams && searchParams.get(paramName) && tabs.some((tab) => tab.value === searchParams.get(paramName))) {
      return searchParams.get(paramName) as string;
    }
    return defaultTab || (tabs.length > 0 ? tabs[0].value : '');
  };

  const [activeTabName, setActiveTabName] = useState<string>(initialTab());

  const handleTabClick = (value: string) => {
    setActiveTabName(value);
    if (useUrlParams) {
      setSearchParams({ [paramName]: value }, { replace: true });
    }
  };

  useEffect(() => {
    if (useUrlParams && searchParams.get(paramName) && tabs.some((tab) => tab.value === searchParams.get(paramName))) {
      setActiveTabName(searchParams.get(paramName) as string);
    }
  }, [searchParams, paramName, tabs, useUrlParams]);

  const Tab = ({ label, value, isActive, onClick, subText }: any) => (
    <div className="space-y-3 cursor-pointer p-1">
      {subText ? (
        <Button
          variant={'outline'}
          className={`${isActive ? 'text-primary' : 'text-gray-500'} font-medium cursor-not-allowed`}
          disabled
        >
          {label}
          <sup> {t('dnaSinglePage.tabs.soon')}</sup>
        </Button>
      ) : (
        <Button
          onClick={onClick}
          variant={'outline'}
          className={`px-6 ${
            isActive ? 'text-primary bg-semi-primary' : 'text-gray-500'
          } font-medium hover:text-primary dark:hover:text-primary`}
        >
          {label}
        </Button>
      )}
    </div>
  );

  return (
    <div className="w-full">
      <div className="flex flex-wrap gap-2">
        {tabs.map((tab, index) => (
          <Tab
            key={index}
            label={tab.label}
            value={tab.value}
            isActive={activeTabName === tab.value}
            onClick={tab.value ? () => handleTabClick(tab.value) : undefined}
            subText={tab.subText}
          />
        ))}
      </div>
      <Separator className="mt-3" />
      {tabComponents[activeTabName] && <div className="mt-4">{tabComponents[activeTabName]}</div>}
    </div>
  );
};

export default Tabs;
