import { ReactNode } from 'react';
import { IStatus } from '@/modules/app/dashboard/modules-status/types';

export interface IMultiCondition {
  status?: IStatus; // allow for optional status if desired
  wantedStatus: string | string[];
  operator?: 'not' | 'is';
}

export interface ConditionalComponentProps {
  status?: IStatus;
  wantedStatus?: string | string[];
  children: ReactNode;
  operator?: 'not' | 'is';
  fallbackComponent?: ReactNode;
  shouldPass?: boolean;
  multiConditions?: IMultiCondition[];
  multiConditionOperator?: 'some' | 'every' | 'allTogetherShouldNotPass';
}

/**
 * Validates the current status against the desired status (or set of statuses)
 * based on the operator provided.
 */
const validateStatus = (
  status: IStatus | undefined,
  wantedStatus: string | string[],
  operator: 'not' | 'is' = 'is'
): boolean => {
  if (!status) return false;

  const currentStatus = status.phase_with_status;

  if (Array.isArray(wantedStatus)) {
    const matchFound = wantedStatus.includes(currentStatus);
    return operator === 'not' ? !matchFound : matchFound;
  } else {
    const isEqual = currentStatus === wantedStatus;
    return operator === 'not' ? !isEqual : isEqual;
  }
};

/**
 * Conditionally renders children based on one or multiple status conditions.
 * It falls back to the provided fallback component when conditions aren't met.
 */
export const ConditionalComponent = ({
  status,
  wantedStatus,
  children,
  operator = 'is',
  fallbackComponent = null,
  shouldPass = false,
  multiConditions = [],
  multiConditionOperator = 'some',
}: ConditionalComponentProps): JSX.Element | null => {
  // Handle multiple conditions if provided
  if (multiConditions.length > 0) {
    switch (multiConditionOperator) {
      case 'some':
        return multiConditions.some(({ status: conditionStatus, wantedStatus, operator: conditionOperator = 'is' }) =>
          validateStatus(conditionStatus, wantedStatus, conditionOperator)
        ) ? (
          <>{children}</>
        ) : (
          <>{fallbackComponent}</>
        );
      case 'every':
        return multiConditions.every(({ status: conditionStatus, wantedStatus, operator: conditionOperator = 'is' }) =>
          validateStatus(conditionStatus, wantedStatus, conditionOperator)
        ) ? (
          <>{children}</>
        ) : (
          <>{fallbackComponent}</>
        );
      case 'allTogetherShouldNotPass':
        return multiConditions.every(
          ({ status: conditionStatus, wantedStatus, operator: conditionOperator = 'is' }) => {
            return validateStatus(conditionStatus, wantedStatus, conditionOperator);
          }
        ) ? (
          <>{fallbackComponent}</>
        ) : (
          <>{children}</>
        );
    }
  }

  // if status was null or undefined, render children
  if (!status) {
    return <>{children}</>;
  }

  // Handle single condition if wantedStatus is provided
  if (wantedStatus !== undefined) {
    return validateStatus(status, wantedStatus, operator) ? <>{children}</> : <>{fallbackComponent}</>;
  }

  // If unconditional pass flag is set, render children
  if (shouldPass) {
    return <>{children}</>;
  }

  // Otherwise, render the fallback component
  return <>{fallbackComponent}</>;
};
