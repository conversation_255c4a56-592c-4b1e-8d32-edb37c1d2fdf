import { IAuthResponse, IDashboardData } from '@/modules/auth/types';
import { Api } from '@/services';

// Get role by ID

// Create a new role
export const login = async (payload: { email: string; password: string }): Promise<IAuthResponse> => {
  const { data } = await Api.post('/auth/login', payload);
  return data.data;
};

// Delete a role by ID
export const getUser = async (): Promise<any> => {
  const { data } = await Api.get('/auth/get-auth-user');
  return data.data;
};

export const getUserStatistics = async (): Promise<IDashboardData> => {
  const { data } = await Api.get('/auth/user/statistics');
  return data.data;
};

export const signUp = async (form: { email: string; name: string; password: string; confirmPassword: string }) => {
  const { data } = await Api.post('/auth/signup', form);
  return data.data;
};

// // Update a role by ID
// export const updateRole = async (id: string, payload: { name: string; permissions: IPermission }): Promise<IRole> => {
//   const { data } = await Api.put(`${ROLES}/${id}`, payload);
//   return data;
// };

// // Get all permissions
// export const getPermissions = async (): Promise<IPermission[]> => {
//   const { data } = await Api.get(GET_PERMISSIONS);
//   return data.data;
// };

export const logout = async () => {
  const { data } = await Api.post('/auth/signout');
  return data.data;
};

export const reSendEmail = async () => {
  const { data } = await Api.post('/email/verify/resend');
  return data.data;
};

export const forgetPassword = async (payload: { email: string }) => {
  const { data } = await Api.post('/password/forgot', payload);
  return data.data;
};
export const resetPassword = async (payload: {
  password: string;
  password_confirmation: string;
  token: string;
  email: string;
}) => {
  const { data } = await Api.post('/password/reset', payload);
  return data.data;
};
