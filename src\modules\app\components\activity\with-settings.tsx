import { cn } from '@/lib/utils';
import React from 'react';
import { useTranslation } from 'react-i18next';
import QuestionFloatingMenu from './question-floating-menu';
import { useApproveActivity, useDeleteActivity, useGenerateActivityXml } from '@/modules/app/tasks/apis/queries';
import { useLocation, useParams } from 'react-router-dom';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { useRegenerateActivityById } from '@/modules/app/tasks/apis/queries';
import EditQuestionDialog from './question-type-mcq-edit';
// import QuestionXmlDialog from './question-xml-dialog';
import { copyToClipboard, useNotify } from '@/hooks';
import EditCaseQuestionDialog from './question-type-case-edit';
import EditBlankQuestionDialog from './question-type-blank-edit';
import { Button } from '@/components/ui/button';

function withSettings<T extends object>(WrappedComponent: React.FC<T>) {
  return function SettingsWrapper(props: any) {
    // const [open, setOpen] = React.useState(false);
    const { notify } = useNotify();
    const { mutate: deleteActivity, isPending: isDeleting } = useDeleteActivity();
    const { mutate: regenerateActivity, isPending: isRegenerating } = useRegenerateActivityById();
    const { mutate: approveActivity, isPending: isApproving } = useApproveActivity();
    const { mutate: generateActivityXml, isPending: isGeneratingXml } = useGenerateActivityXml();
    const [selectedQuestion, setSelectedQuestion] = React.useState<any>(null);
    const { t } = useTranslation();
    const { taskId: taskCode } = useParams();
    const { confirm } = useConfirmation();
    const { pathname } = useLocation();
    // Functions with confirmation
    const handleDeleteActivity = () => {
      confirm({
        variant: 'destructive',
        title: t('activity.delete.confirmation.title'),
        description: t('activity.delete.confirmation.description'),
        onConfirm: () => deleteActivity({ code: taskCode || '', activity_id: props?.question?.id }),
      });
    };

    const handleRegenerateActivity = () => {
      confirm({
        variant: 'info',
        title: t('activity.regenerate.confirmation.title'),
        description: t('activity.regenerate.confirmation.description'),
        onConfirm: () => regenerateActivity({ code: taskCode || '', activity_id: props?.question?.id }),
      });
    };
    const handleApproveActivity = () => {
      confirm({
        variant: 'default',
        title: t('activity.approve.confirmation.title'),
        description: t('activity.approve.confirmation.description'),
        onConfirm: () => approveActivity({ code: taskCode || '', activity_id: props?.question?.id }),
      });
    };

    const handleGenerateXml = () => {
      confirm({
        variant: 'default',
        title: t('activity.xml.confirmation.title'),
        description: t('activity.xml.confirmation.description'),
        onConfirm: () => generateActivityXml({ code: taskCode || '', activity_id: props?.question?.id }),
      });
    };
    const handleCopyXml = () => {
      copyToClipboard(props?.question?.xml);
      notify.success(t('activity.xml.copied'));
    };

    const handleEditQuestion = () => {
      setSelectedQuestion(props?.question);
    };
    return (
      <div className="relative w-full flex justify-between">
        <div
          className={cn(
            'border-s-8 w-[90%] rounded-md p-3',
            props?.question?.status !== 'draft' && 'border-primary/40'
          )}
        >
          <div
            className={cn(
              'bg-secondary py-1 px-2 rounded-md w-fit text-sm font-medium',
              props?.question?.status !== 'draft' && 'text-primary bg-primary/10'
            )}
          >
            {t(`activity.status.${props?.question?.status}`)}
          </div>
          <WrappedComponent {...props} />
        </div>
        {pathname?.includes('my-tasks') ? (
          <QuestionFloatingMenu
            onDelete={handleDeleteActivity}
            onApprove={handleApproveActivity}
            onEdit={handleEditQuestion}
            onGenerateXml={!!props?.question?.xml ? handleCopyXml : handleGenerateXml}
            hasXml={!!props?.question?.xml}
            onRegenerate={handleRegenerateActivity}
            isLoading={isDeleting || isRegenerating || isApproving || isGeneratingXml}
          />
        ) : (
          <Button onClick={handleCopyXml}>{t('activity.actions.copyXml')}</Button>
        )}
        {selectedQuestion && (
          <div>
            {(props.question?.type === 'mcq' || props.question?.type === 'true_false') && (
              <EditQuestionDialog
                question={selectedQuestion}
                isOpen={!!selectedQuestion}
                onOpenChange={setSelectedQuestion}
              />
            )}

            {props.question?.type === 'case_study' && (
              <EditCaseQuestionDialog
                question={selectedQuestion}
                isOpen={!!selectedQuestion}
                onOpenChange={setSelectedQuestion}
              />
            )}

            {props.question?.type === 'fill_in_the_blank' && (
              <EditBlankQuestionDialog
                question={selectedQuestion}
                isOpen={!!selectedQuestion}
                onOpenChange={setSelectedQuestion}
              />
            )}
          </div>
        )}
        {/* {open && <QuestionXmlDialog isOpen={open} onOpenChange={setOpen} xml={props?.question?.xml} />} */}
      </div>
    );
  };
}

export default withSettings;
