import { CSSProperties, useEffect, useState } from 'react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { Icon } from '@/components';
import { ISlide } from '../../apis/slideshow/types';
import { CSS } from '@dnd-kit/utilities';
import { DndContext, PointerSensor, useSensor, useSensors, DragEndEvent, closestCenter } from '@dnd-kit/core';
import { restrictToFirstScrollableAncestor, restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useReorderSlide } from '../../apis/queries';
import { useTranslation } from 'react-i18next';
const Slide = ({ slide, isReordering }: { slide: ISlide; isReordering: boolean }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: slide.id,
    disabled: isReordering,
  });
  // Draggable styling
  const style: CSSProperties = {
    transform: CSS.Translate.toString(transform),
    transition,
    opacity: isDragging || isReordering ? 0.6 : 1,
  };
  return (
    <div
      className="flex gap-2 bg-[#f9fafb] dark:bg-background justify-between items-center border border-input shadow-sm rounded-lg overflow-hidden ps-3"
      ref={setNodeRef}
      style={style}
    >
      <div className="truncate text-sm" key={slide.id}>
        {slide.title || slide.voice_over}
      </div>
      <div {...attributes} {...listeners} className="bg-[#e5e7eb] dark:bg-primary px-6 py-3 cursor-grab">
        <Icon className="text-[#6b7280] dark:text-foreground" icon="gravity-ui:bars" width={22} />
      </div>
    </div>
  );
};

const SlidesReorderSheet = ({
  slides = [],
  slideshowId,
  dnaId,
}: {
  slides: ISlide[];
  slideshowId: number;
  dnaId: number;
}) => {
  // state
  const [localSlides, setLocalSlides] = useState(slides);
  const ids = localSlides.map((slide) => slide.id);

  // hooks
  const { i18n } = useTranslation();
  const { mutate: reorderSlide, isPending: isReordering } = useReorderSlide();
  const sensors = useSensors(useSensor(PointerSensor));

  // functions
  const handleReorder = (slideshowId: number, dnaId: number, slideId: any, nextSlideId: any) => {
    reorderSlide({ dnaId, slideshow_id: slideshowId, slide_id: slideId, next_slide_id: nextSlideId });
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    if (active.data.current?.sortable?.index === over?.data?.current?.sortable?.index) return;
    const newArray = arrayMove(
      localSlides,
      active.data.current?.sortable?.index,
      over?.data?.current?.sortable?.index || 0
    );
    const nextSlideId = newArray[over?.data?.current?.sortable?.index + 1]?.id || null;
    setLocalSlides(newArray);
    handleReorder(slideshowId, dnaId, active?.id, nextSlideId);
  };

  // effects
  useEffect(() => {
    setLocalSlides(slides);
  }, [slides]);

  return (
    <Sheet>
      <SheetTrigger>
        <div className=" absolute top-96 z-10 left-0 rtl:right-0 -translate-x-1/2 rtl:translate-x-1/2 bg-muted px-5 py-2 rounded-lg w-32 flex justify-end">
          <Icon icon="mingcute:list-check-line" className="text-slate-500 cursor-pointer" width={28} />
        </div>
      </SheetTrigger>
      <SheetContent side={i18n.dir() === 'ltr' ? 'left' : 'right'}>
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
          modifiers={[restrictToFirstScrollableAncestor, restrictToVerticalAxis]}
        >
          <SortableContext items={ids} id="top-level" strategy={verticalListSortingStrategy}>
            <div className=" flex flex-col gap-6 pt-10">
              {localSlides?.length > 0 &&
                localSlides.map((slide) => <Slide isReordering={isReordering} slide={slide} key={slide.id} />)}
            </div>
          </SortableContext>
        </DndContext>
      </SheetContent>
    </Sheet>
  );
};

export default SlidesReorderSheet;
