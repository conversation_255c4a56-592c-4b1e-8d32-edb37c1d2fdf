import { useLocation, Navigate } from 'react-router-dom';
import NotFound from './NotFoundPage';
const RedirectToLanguage = () => {
  const location = useLocation();
  const lng = localStorage.getItem('i18nextLng') || 'en';
  const currentLanguage = lng;
  if (location.pathname.startsWith('/en') || location.pathname.startsWith('/ar')) {
    return <NotFound />;
  }
  const newPathname = `/${currentLanguage}${location.pathname}`;
  const search = location.search;
  const hash = location.hash;

  return <Navigate to={`${newPathname}${search}${hash}`} replace />;
};

export default RedirectToLanguage;
