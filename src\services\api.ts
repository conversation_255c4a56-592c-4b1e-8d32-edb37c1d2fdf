import { api } from './axios';

export const Api: any = {
  get: (endpoint: any, params: any) => api.get(endpoint, { params, headers: getHeaders() }),
  post: (endpoint: any, data: any, headers: any = {}) =>
    api.post(endpoint, data, { headers: { ...getHeaders(), ...headers } }),
  put: (endpoint: any, data: any) => api.put(endpoint, data, { headers: getHeaders() }),
  delete: (endpoint: any, params: any) => api.delete(endpoint, { params, headers: getHeaders() }),
  call: (method: any, endpoint: any, data: any, params: any) =>
    api({
      method,
      url: endpoint,
      data,
      params,
      headers: getHeaders(),
    }),
};

const getHeaders = () => {
  const token = localStorage.getItem('token');
  return token ? { Authorization: `Bearer ${token}` } : {};
};
