// src/types/api.ts
import { AxiosRequestConfig, AxiosResponse } from 'axios';

export type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

export interface ApiResponse<T = any> extends AxiosResponse<T> {}

export interface ApiParams {
  [key: string]: any;
}

export interface ApiData {
  [key: string]: any;
}

export interface ApiLookupParams {
  name: string;
  params?: ApiParams;
}

export interface ApiCallParams {
  method: HttpMethod;
  endpoint: string;
  data?: ApiData;
  params?: ApiParams;
  config?: AxiosRequestConfig;
}
