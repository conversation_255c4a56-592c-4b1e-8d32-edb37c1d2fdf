import React, { createContext, useEffect, useState } from 'react';
import { useDarkMode } from '/src/hooks/dark-mode';
import Cookies from 'js-cookie';

// Deps
import 'iconify-icon';

// Components
import { ConfirmDialog } from '/src/components/confirm-dialog';
import { ConfirmationDialog } from '@/components/confirmation-popup/confirmation-dialog';
import i18n from '@/utils/i18n';
import { Toaster } from '@/components/ui/sonner';

export const AppContext = createContext(null);

export const AppProvider = ({ children }: any) => {
  //   State
  const [toastOptions, notify] = useState(null);
  const [confirmDialogOptions, showConfirm] = useState(null);
  const { isDark, switchDarkMode } = useDarkMode();
  const [lang, setLang] = useState(Cookies.get('i18next')); // Keep track of the language

  //   App Context
  const appContext: any = {
    notify,
    showConfirm,
    isDark,
    switchDarkMode,
    // updateUser,
    // hideDialogConfirm,
  };

  useEffect(() => {
    // Update the lang state when the cookie changes
    const handleLanguageChange = () => {
      const newLang = Cookies.get('i18next');
      setLang(newLang);
    };

    // Set initial values
    handleLanguageChange();

    // Set full height and direction
    // document.body.classList.add('h-full', 'overflow-x-hidden');
    // document.querySelector('html')?.classList.add('h-full', 'overflow-x-hidden');
    // document.getElementById('root')?.classList.add('h-full');
    document.dir = i18n.dir();

    // Listen for changes in the language cookie
    const cookieChangeListener = setInterval(() => {
      const currentLang = Cookies.get('i18next');
      if (currentLang !== lang) {
        handleLanguageChange();
      }
    }, 100); // Check every 100ms (adjust as needed)

    return () => clearInterval(cookieChangeListener);
  }, [lang]);

  return (
    <AppContext.Provider value={appContext}>
      {/* Handle Notifications */}
      <Toaster richColors theme="dark" />
      {confirmDialogOptions && <ConfirmDialog options={confirmDialogOptions} onClose={() => showConfirm(null)} />}
      <ConfirmationDialog />
      {children}
    </AppContext.Provider>
  );
};
