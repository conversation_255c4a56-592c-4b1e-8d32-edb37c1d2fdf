import { lazy } from 'react';

const SingleCoursePage = lazy(() => import('../course/pages/single'));
const SingleDna = lazy(() => import('../dna/pages/single'));
const ContnetLibrary = lazy(() => import('./pages/list'));

export default [
  {
    path: 'content-library',
    element: <ContnetLibrary />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.course',
        title: 'breadcrumb.myContentPage.course',
      };
    },
  },
  {
    path: 'courses/:id',
    element: <SingleCoursePage />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.course',
        title: 'breadcrumb.myContentPage.course',
      };
    },
  },
  {
    path: 'courses/:id/:dnaId',
    element: <SingleDna />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.dna',
      };
    },
  },
];
