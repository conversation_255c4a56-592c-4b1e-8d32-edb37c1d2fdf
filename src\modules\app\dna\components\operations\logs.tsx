import { Modal } from '@/index';
import { diffWords } from 'diff';
import { useTranslation } from 'react-i18next';
import { ILogsData } from '../../types';

// Helper function to strip HTML tags from text
const stripHtmlTags = (html: string) => {
  if (!html) return '';
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = html;
  return tempDiv.textContent || tempDiv.innerText || '';
};

const TextDiffHighlighter = ({ originalText, modifiedText }: { originalText: string; modifiedText: string }) => {
  // Strip HTML tags from both texts before comparing to avoid HTML tag noise
  const cleanOriginalText = stripHtmlTags(originalText || '');
  const cleanModifiedText = stripHtmlTags(modifiedText || '');

  const differences = diffWords(cleanOriginalText, cleanModifiedText);

  return (
    <div className="border p-4 rounded-md bg-gray-50">
      {differences.map((part, index) => {
        const className = part.added
          ? 'bg-green-200 text-green-800 px-1 rounded'
          : part.removed
          ? 'bg-red-200 text-red-800 px-1 rounded'
          : '';

        return <span dangerouslySetInnerHTML={{ __html: part?.value || '' }} key={index} className={className}></span>;
      })}
    </div>
  );
};

interface IProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  data: ILogsData[];
}

const LogsData = ({ isOpen, onOpenChange, data }: IProps) => {
  const { t } = useTranslation();

  const displayKeys = [
    'title',
    'dna status',
    'description',
    'dna content',
    'bloom taxonomy',
    'learning objectives',
    'dna phase',
  ];

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} width={1000} modalHeader={t('metadataPage.logs')}>
      <div className="space-y-6">
        {data.map((logEntry, logIndex) => {
          // Handle the new format where each log entry has an 'attribute' field
          if (logEntry.attribute) {
            const keyLower = logEntry.attribute.toLowerCase();
            const keyWithCapitalizedDna = keyLower
              .split(' ')
              .map((word) => (word === 'dna' ? 'DNA' : word))
              .join(' ');

            if (displayKeys.includes(keyLower)) {
              const containerClass =
                keyLower === 'dna content'
                  ? 'grid grid-cols-2 gap-2 max-h-[400px] overflow-scroll'
                  : 'grid grid-cols-2 gap-2';

              return (
                <div key={logIndex} className="space-y-2">
                  <p className="font-medium capitalize">{t(`systemlogs.popup.type.${keyWithCapitalizedDna}`)}</p>
                  <div className={containerClass}>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.oldValue')}</p>
                      <div
                        className="border p-4 rounded-md "
                        dangerouslySetInnerHTML={{
                          __html: logEntry.old || '<p>No previous content</p>',
                        }}
                      />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.chanages')}</p>
                      <TextDiffHighlighter originalText={logEntry.old || ''} modifiedText={logEntry.new || ''} />
                    </div>
                  </div>
                </div>
              );
            }
          } else {
            return (
              <div className="space-y-4 overflow-hidden" key={logIndex}>
                {/* Handel old format */}
                {Object.entries(logEntry).map(([key, values]: [string, any], entryIndex) => {
                  const keyLower = key.toLowerCase();
                  const keyWithCapitalizedDna = keyLower
                    .split(' ')
                    .map((word) => (word === 'dna' ? 'DNA' : word))
                    .join(' ');

                  if (displayKeys.includes(keyLower)) {
                    const containerClass =
                      keyLower === 'dna content'
                        ? 'grid grid-cols-2 gap-2 max-h-[400px] overflow-scroll'
                        : 'grid grid-cols-2 gap-2';

                    return (
                      <div key={entryIndex} className="space-y-2">
                        <p className="font-medium capitalize">{t(`systemlogs.popup.type.${keyWithCapitalizedDna}`)}</p>
                        <div className={containerClass}>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.oldValue')}</p>
                            <div
                              className="border p-4 rounded-md bg-gray-50"
                              dangerouslySetInnerHTML={{ __html: values?.old || '' }}
                            />
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm font-medium text-gray-600">{t('systemlogs.popup.chanages')}</p>
                            <TextDiffHighlighter originalText={values?.old || ''} modifiedText={values?.new || ''} />
                          </div>
                        </div>
                      </div>
                    );
                  }
                  return null;
                })}
              </div>
            );
          }
        })}
      </div>
    </Modal>
  );
};

export default LogsData;
