import { use<PERSON>tom } from 'jotai';
import { dialogConfigAtom, isDialogOpenAtom, DEFAULT_CONFIG } from './store';
import { ConfirmationOptions } from './types';
import { useTranslation } from 'react-i18next';

export const useConfirmation = () => {
  const [isOpen, setIsOpen] = useAtom(isDialogOpenAtom);
  const [config, setConfig] = useAtom(dialogConfigAtom);
  const { t } = useTranslation();

  const confirm = async (options?: ConfirmationOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      const finalConfig = {
        ...DEFAULT_CONFIG,
        title: t('confirmDialog.title'),
        description: t('confirmDialog.description'),
        confirmLabel: t('confirmDialog.confirmLabel'),
        cancelLabel: t('confirmDialog.cancelLabel'),
        ...options,
        onConfirm: async () => {
          if (options?.onConfirm) {
            await options.onConfirm();
          }
          setIsOpen(false);
          resolve(true);
        },
        onCancel: () => {
          setIsOpen(false);
          options?.onCancel?.();
          resolve(false);
        },
        onClose: () => {
          options?.onClose?.();
          resolve(false);
        },
      };

      setConfig(finalConfig);
      setIsOpen(true);
    });
  };

  return { confirm, isOpen, setIsOpen, config };
};
