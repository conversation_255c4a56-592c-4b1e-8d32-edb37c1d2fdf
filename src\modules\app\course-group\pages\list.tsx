import { useState, useMemo } from 'react';
import { useFetchList, Icon, ProtectedComponent } from '@/index';
import {
  dateAndTimeFormat,
  getContentStatusStyle,
  formatUserByNameAndEmail,
  generateEnum,
  getCourseGroupStatusStyle,
  convertStatusToFilterEnumByTitle,
} from '@/utils/helpers';
import { ICourse } from '@/modules/app/course/types';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CourseGroupDialog } from '../../course/components/createCourseGroupDialog';
import { useContentTablesTabs } from '../../content-tabs';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  useDeleteCourseGroup,
  useDeleteCourseInsideCourseGroup,
  useUpdateCourseGroupStatus,
} from '@/modules/app/course-group/api/queries';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import LanguageLink from '@/components/language-link';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TableSearch,
} from '@/components/theTable';
import { ICourseGroupList } from '../types';
import TablePagination from '@/components/theTable/table-pagination';
import { useGetSingleMetadata } from '../../dashboard/metadata/apis/queries';
import TableRowsExpander from '@/components/theTable/table-rows-expander';
import ListExpander from '../../components/list-expander';
import { useGetModulesStatus } from '../../dashboard/modules-status/apis/queries';
import { useConfirmation } from '@/components/confirmation-popup/hooks';

const CourseGroupList = () => {
  // State
  const [courseGroupDialog, setCourseGroupDialog] = useState(false);
  const [editData, setEditData] = useState<any>(null);
  const { t, i18n } = useTranslation();
  const { confirm } = useConfirmation();

  // Hooks
  const contentTablesTabs = useContentTablesTabs();
  const {
    mutate: deleteCourseGroup,
    isPending: isDeleteCourseGroupPending,
    variables: deleteCourseGroupVariables,
  } = useDeleteCourseGroup();

  const {
    mutate: deleteCourseInsideCourseGroup,
    isPending: isDeleteCourseInsideCourseGroupPending,
    variables,
  } = useDeleteCourseInsideCourseGroup();

  const {
    mutate: updateCourseGroupStatus,
    isPending: isUpdateCourseGroupStatusPending,
    variables: updateCourseGroupStatusVariables,
  } = useUpdateCourseGroupStatus();

  const { data: allmetaData } = useGetSingleMetadata('');
  const tagsOptions = allmetaData?.filter((item) => item.type === 'tags') || [];
  const { data: modulesStatus } = useGetModulesStatus();
  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'Course Group', i18n.language);

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { ready, loading, list, count, refresh, search, filters, pagination } = useFetchList(
    '/courses/groups/all',
    'groups',
    {
      search: '',
      pagination: {
        page_num: 1,
        page_size: 30,
      },
      filters: {
        ...(useHasPermission('user_index')
          ? {
              user_id: {
                api: 'user/lookup',
                placeholder: 'dnaConentPgae.userFIlter',
              },
            }
          : {}),
        tag_id: {
          dynamicEnum: generateEnum(tagsOptions, 'id', labelKey),
          placeholder: 'courseGroup.tagsFliter',
          type: 'multi',
        },
      },
    }
  );

  const handelDeleteCourse = (groupId: string, courseId: string) => {
    confirm({
      variant: 'destructive',
      title: t('courseGroup.deleteCourse.confirmation.title'),
      description: t('courseGroup.deleteCourse.confirmation.description'),
      onConfirm: () => {
        deleteCourseInsideCourseGroup({
          id: groupId,
          course_id: courseId,
        });
      },
    });
  };
  const handelDeleteCourseGroup = (groupId: string) => {
    confirm({
      variant: 'destructive',
      title: t('courseGroup.deletegroup.confirmation.title'),
      description: t('courseGroup.deletegroup.confirmation.description'),
      onConfirm: () => {
        deleteCourseGroup(groupId);
      },
    });
  };

  const handelChangeCourseStatus = (id: number, status_id: number) => {
    updateCourseGroupStatus({ id, status_id });
  };

  const columns = useMemo((): ITableColumn<ICourseGroupList & ICourse>[] => {
    return [
      {
        accessorKey: 'title',
        header: t('cousrePlanContentPage.table.status'),
        width: '250px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="w-full gap-6 flex rtl:text-right rtl:ms-5 group transition-opacity">
                  {getCourseGroupStatusStyle(row?.status)}{' '}
                </div>
              );
            case 1:
              return (
                <div className="w-full gap-6 flex rtl:text-right rtl:ms-5 group transition-opacity">
                  {getContentStatusStyle(row?.courseStatus)}
                </div>
              );
            default:
              return '_';
          }
        },
      },
      {
        accessorKey: 'title',
        header: t('cousrePlanContentPage.table.title'),
        width: '240px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex gap-2 items-center">
                  <p className="font-medium">{row?.title}</p>
                </div>
              );
            case 1:
              return (
                <LanguageLink to={`/app/my-content/courses/${row.id}`} className="font-medium underline text-primary">
                  {row.title}
                </LanguageLink>
              );
            default:
              return '_';
          }
        },
      },
      {
        accessorKey: 'length',
        header: t('breadcrumb.myContentPage.course'),
        width: '70px',
        cell: ({ row, level }) => {
          if (level === 0) {
            return <p className="text-center">{row?.courses?.length}</p>;
          }
        },
      },

      {
        accessorKey: 'subject',
        header: t('cousrePlanContentPage.table.subject'),
        width: '100px',
        cell: ({ row, level }) => {
          return (
            <p className="rtl:text-right">
              {row.subject ? (labelKey ? row.subject?.name_en : row.subject?.name_ar) : row.subject}
            </p>
          );
        },
      },
      {
        accessorKey: 'audience',
        header: t('cousrePlanContentPage.table.audience'),
        width: '100px',
        cell: ({ row, level }) => {
          return (
            <p className="rtl:text-right">
              {row.audience ? (labelKey ? row.audience?.name_en : row.audience?.name_ar) : row.audience}
            </p>
          );
        },
      },
      {
        accessorKey: 'language',
        header: t('cousrePlanContentPage.table.language'),
        width: '100px',
        cell: ({ row, level }) => {
          return (
            <p className="rtl:text-right">
              {row.language ? (labelKey ? row.language?.name_en : row.language?.name_ar) : row.language}
            </p>
          );
        },
      },
      {
        accessorKey: 'notes',
        header: t('cousrePlanContentPage.table.notes'),
        width: '80px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return row.total_notes === 0 ? '_' : row.total_notes;
            case 1:
              return row.notes_count === '0' ? '_' : row.notes_count;
            default:
              return '_';
          }
        },
      },
      {
        accessorKey: 'tags',
        header: t('cousrePlanContentPage.table.tags'),
        width: '180px',
        cell: ({ row }) => <ListExpander initialVisiableItems={1} list={row.tags || []} />,
      },

      {
        accessorKey: 'date',
        header: t('cousrePlanContentPage.table.date'),
        width: '100px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'author',
        header: t('cousrePlanContentPage.table.author'),
        width: '130px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.user);
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '130px',
        cell: ({ row, level, parentRow }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex gap-1 rtl:text-right">
                  <Popover>
                    <PopoverTrigger>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              size="icon"
                              variant="ghost"
                              loading={
                                isUpdateCourseGroupStatusPending && updateCourseGroupStatusVariables.id === row.id
                              }
                            >
                              <Icon icon="solar:list-bold-duotone" width={25} className="text-primary" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>{t('courseGroup.Table.changeStatus')}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </PopoverTrigger>
                    <PopoverContent className="w-56 p-2">
                      <div className="border border-border rounded-md divide-y divide-border">
                        {statusFilter.map((item, index) => (
                          <div
                            key={index}
                            className="group relative flex items-center gap-3 cursor-pointer p-3 text-sm font-medium text-gray-700 hover:text-primary hover:bg-primary/5 transition-all duration-200 ease-in-out"
                            role="button"
                            tabIndex={0}
                            onClick={() => handelChangeCourseStatus(row.id, item.value)}
                          >
                            <span className="flex-1 flex items-center gap-1 truncate  transition-colors duration-200">
                              {item.label}
                            </span>
                            {item.value === row.status.id && (
                              <div className="w-1 h-4 bg-primary rounded-full opacity-100 transition-opacity duration-200 absolute right-2" />
                            )}{' '}
                          </div>
                        ))}
                      </div>
                    </PopoverContent>
                  </Popover>

                  <ProtectedComponent requiredPermissions="course_group_edit">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            onClick={() => {
                              setEditData(row);
                              setCourseGroupDialog(true);
                            }}
                          >
                            <Icon icon="solar:pen-new-square-linear" width={25} className="text-primary" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('edit')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ProtectedComponent>

                  <ProtectedComponent requiredPermissions="course_group_delete">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="ghost"
                            loading={isDeleteCourseGroupPending && row.id === deleteCourseGroupVariables}
                            onClick={() => handelDeleteCourseGroup(row.id)}
                          >
                            <Icon icon="gg:trash" width={25} className="text-red-500" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('courseGroup.group.table.delete')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </ProtectedComponent>
                </div>
              );

            case 1:
              return (
                <div className="flex justify-end">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          loading={isDeleteCourseInsideCourseGroupPending && row?.id === variables.course_id}
                          onClick={() => handelDeleteCourse(parentRow?.id ?? '', row.id)}
                        >
                          <Icon icon="gg:trash" width={25} className="text-red-500" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('courseGroup.course.table.delete')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              );
          }
        },
      },
    ];
  }, [
    statusFilter,
    t,
    labelKey,
    isDeleteCourseGroupPending,
    isDeleteCourseInsideCourseGroupPending,
    isUpdateCourseGroupStatusPending,
    updateCourseGroupStatusVariables,
    deleteCourseGroupVariables,
    variables,
  ]);

  return (
    <>
      <Table
        rows={list || []}
        columns={columns}
        loading={loading}
        nestedConfig={{
          enabled: true,
          childProperties: ['courses'],
        }}
      >
        <TableContent>
          <TableContentHeader tabs={contentTablesTabs} className="mt-0 flex justify-between">
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableRowsExpander />

              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="flex ms-auto gap-3">
              <div className="flex gap-4">
                {useHasPermission('course_group_create') && (
                  <Button onClick={() => setCourseGroupDialog(true)} className="flex gap-2 items-center">
                    {t('cousrePlanContentPage.createNewCourseGroup')}
                  </Button>
                )}
              </div>
            </div>
          </TableContentHeader>

          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>

      {courseGroupDialog && (
        <CourseGroupDialog
          onOpen={courseGroupDialog}
          opOpenChange={() => {
            setCourseGroupDialog(false);
            setEditData(null);
          }}
          editData={editData}
          isCourseList={false}
        />
      )}
    </>
  );
};

export default CourseGroupList;
