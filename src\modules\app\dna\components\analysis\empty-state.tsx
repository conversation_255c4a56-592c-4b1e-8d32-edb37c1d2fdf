import { ProtectedComponent } from '@/components/protected-component';
import { Form, ComboboxInput } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { DnaEnums } from '@/services';
import { useForm, useValidate } from '@/index';
import { useGenerateArticulateAnalysis } from '../../apis/queries';
import { generateEnum } from '@/utils/helpers';
import { IDNA } from '../../types';
import { useEffect, useMemo } from 'react';

const EmptyAnalysis = ({ dna }: { dna: IDNA | undefined }) => {
  const { t, i18n } = useTranslation();
  const { mutate, isPending } = useGenerateArticulateAnalysis();
  const currentLanguageLabel = i18n.language === 'en' ? 'name_en' : 'name_ar';
  const allLanguages = useMemo(() => {
    if (!dna) return [];
    let languages = [];
    languages.push(dna.language);
    for (let i = 0; i < dna.content_translations.length; i++) {
      languages.push(dna.content_translations[i]?.language);
    }

    return languages;
  }, [dna]);
  useEffect(() => {
    if (dna?.id) {
      setFieldValue('id')(dna?.id);
    }
  }, [dna]);
  //hooks
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    id: dna?.id,
    language_id: dna?.language.id,
    model: 'gpt-4.1',
  });
  const handleSubmit = () => {
    mutate(form);
  };

  return (
    <div className="flex flex-col justify-center items-center ms-2 min-h-96 my-10">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.analysis.noAnalysisTitle')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.analysis.noAnalysisDescription')}</p>
      </div>
      <Form onSubmit={handleSubmit}>
        <div className="mb-5 w-80">
          <ComboboxInput
            name="model"
            placeholder={t('analysis.model')}
            label={t('analysis.model')}
            options={DnaEnums.ai_base_model}
            value={form.model}
            onChange={setFieldValue('model')}
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="language"
            placeholder={t('analysis.language')}
            label={t('analysis.language')}
            options={generateEnum(allLanguages, 'id', currentLanguageLabel)}
            value={form.language_id}
            onChange={setFieldValue('language_id')}
            validators={[isRequired()]}
          />
        </div>

        <ProtectedComponent requiredPermissions={'dna_analysis_create'}>
          <Button loading={isPending} className="flex gap-2 px-5 items-center mx-auto">
            {t('generate')}
          </Button>
        </ProtectedComponent>
      </Form>
    </div>
  );
};

export default EmptyAnalysis;
