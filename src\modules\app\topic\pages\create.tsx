import { useEffect, useState } from 'react';
import { Form, TextInput, useConfirmDialog, useForm, useValidate, Icon } from '@/index';
import { Button } from '@/components/ui/button';
import TopicTable from '../components/topic-table';
import { Checkbox } from '@/components/ui/checkbox';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useCreateTopic, useDeleteTopic, useGetTopicById } from '../apis/queries';
import { cn } from '@/lib/utils';
const TopicCreatePage = () => {
  // State
  const [topicId, setTopicId] = useState<string | any>(undefined);
  const [tableData, setTableData] = useState<any>(null);
  // Hooks
  const { isRequired } = useValidate();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { mutate: deleteTopic } = useDeleteTopic();
  const { mutate: createTopic, isPending } = useCreateTopic();
  const { data: subjectOptions } = useGetSingleMetadata('subject');
  const { data: audienceOptions } = useGetSingleMetadata('audience');
  const { data: languageOptions } = useGetSingleMetadata('language');
  const { data: blooms_taxonomyOptions } = useGetSingleMetadata('bloom"s_taxonomy');

  const [searchParams, setSearchParams] = useSearchParams();
  const { data: topicFromFind } = useGetTopicById(searchParams.get('topicId') || topicId);
  useEffect(() => {
    if (topicFromFind) {
      setFormValue({
        version: 'v.1.02',
        topic: topicFromFind.title,
        subject_id: topicFromFind.subject.id,
        other_subject: topicFromFind.other_subject,
        audience_id: topicFromFind.audience.id,
        long_text: topicFromFind.long_text,
        language_id: topicFromFind.language.id,
        file_path: topicFromFind.file_path,
        url: topicFromFind.url,
        txt: topicFromFind.txt,
        remembering: 0,
        understanding: 0,
        applying: 0,
        analyzing: 0,
        at_topic: 1,
      });
      setTableData(topicFromFind.dnas);
      setTopicId(topicFromFind.id);
    }
  }, [topicFromFind]);

  const { t, i18n } = useTranslation();
  const initialFormState = {
    version: 'v.1.02',
    topic: '',
    subject_id: '',
    other_subject: '',
    audience_id: '',
    long_text: '',
    language_id: '',
    file_path: '',
    url: '',
    txt: '',
    remembering: 0,
    understanding: 0,
    applying: 0,
    analyzing: 0,
    at_topic: 1,
  };
  const { form, setFieldValue, setFormValue } = useForm(initialFormState);

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  // Functions
  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('topicCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handelHardReset = () => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        deleteTopic(topicId);
        setSearchParams({});
        setTopicId(undefined);
        setTableData(null);
        hideConfirm();
        handleClear();
      },
    });
  };

  const handleClear = () => {
    setFormValue(initialFormState);
    setTableData(null);
  };
  const handleSubmit = async () => {
    createTopic(form, {
      onSuccess: (data) => {
        setTopicId(data.id);
        setSearchParams({ topicId: data.id }, { replace: true });
      },
    });
  };

  return (
    <div>
      <div className="flex flex-col justify-between lg:grid lg:grid-cols-9 gap-6">
        <Form onSubmit={handleSubmit} className="grid grid-cols-4 lg:col-span-3 gap-5">
          <div
            className={cn(
              'grid grid-cols-1 sm:grid-cols-2 gap-5 lg:block lg:space-y-2',
              topicFromFind ? 'col-span-4' : 'col-span-3'
            )}
          >
            <TextInput
              name="title"
              label={t('topicCreationPage.form.title')}
              placeholder={t('topicCreationPage.form.title')}
              value={form.topic}
              onChange={setFieldValue('topic')}
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="subject"
              label={t('topicCreationPage.form.subject')}
              placeholder={t('topicCreationPage.form.subject')}
              options={generateEnum(subjectOptions || [], 'id', labelKey)}
              value={form.subject_id}
              onChange={setFieldValue('subject_id')}
              dropIcon
              validators={[isRequired()]}
            />
            {form.subject === 'Other (write a subject)' && (
              <TextInput
                name="otherSubject"
                label="Other Subject"
                placeholder="Enter other subject"
                value={form.other_subject}
                onChange={setFieldValue('other_subject')}
                isRequired
                validators={[isRequired()]}
              />
            )}
            <ComboboxInput
              name="audience"
              label={t('topicCreationPage.form.audience')}
              placeholder={t('topicCreationPage.form.audience')}
              options={generateEnum(audienceOptions || [], 'id', labelKey)}
              value={form.audience_id}
              onChange={setFieldValue('audience_id')}
              dropIcon
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="language"
              label={t('topicCreationPage.form.language')}
              placeholder={t('topicCreationPage.form.language')}
              options={generateEnum(languageOptions || [], 'id', labelKey)}
              value={form.language_id}
              onChange={setFieldValue('language_id')}
              dropIcon
              validators={[isRequired()]}
            />
            <div className="grid grid-cols-2 gap-3">
              {blooms_taxonomyOptions?.map((e) => (
                <div key={e.id} className="flex gap-2">
                  <Checkbox
                    id="blooms_taxonomyOptions"
                    onCheckedChange={(checked) =>
                      checked ? setFieldValue(e.name_en.toLowerCase())(1) : setFieldValue(e.name_en.toLowerCase())(0)
                    }
                  />
                  <label
                    htmlFor="blooms_taxonomyOptions"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {labelKey === 'name_en' ? e.name_en : e.name_ar}
                  </label>
                </div>
              ))}
            </div>
            <TextInput
              name="context"
              label={t('topicCreationPage.form.context')}
              placeholder={t('topicCreationPage.form.contextPlaceholder')}
              value={form.long_text}
              onChange={setFieldValue('long_text')}
            />
          </div>
          {!topicFromFind?.dnas && (
            <div className="flex gap-2 mt-8 col-span-1">
              <Button type="submit" loading={isPending} disabled={isPending}>
                {t('generate')}
              </Button>
              <Button disabled={isPending} type="reset" variant={'outline'} onClick={handleClear}>
                {t('clear')}
              </Button>
            </div>
          )}
        </Form>
        <div className="col-span-6 mt-4 space-y-4">
          {topicFromFind?.dnas && (
            <div>
              <div className="flex justify-between mt-5">
                <p onClick={() => handelHardReset()} className="text-primary underline cursor-pointer self-center">
                  {t('topicCreationPage.startOver')}
                </p>
              </div>
              <div className="mt-6">
                <TopicTable topicId={topicId} topics={tableData} setTopics={setTableData} topic={topicFromFind} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TopicCreatePage;
