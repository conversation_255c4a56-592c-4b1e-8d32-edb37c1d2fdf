import { Button } from '@/components/ui/button';

import { Dialog, DialogContent } from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';

export const ConfirmDialog = ({ options, onClose }: any) => {
  const { t } = useTranslation();
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-[400px]">
        <div className="text-center px-6 spacy-y-5">
          <h3 className="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">{options.message}</h3>
          <div className="flex gap-2 items-center justify-center">
            <Button onClick={onClose} variant="secondary">
              {options.cancelLabel || t('confirmDialog.cancel')}
            </Button>
            <Button onClick={options.onConfirm} variant={options.danger ? 'destructive' : 'default'}>
              {options.confirmLabel || t('confirmDialog.confirm')}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
