// /src/modules/app/routes.js
import { Navigate } from 'react-router-dom';
import { lazy } from 'react';
import { AppMainLayout } from './layout';
import { Outlet } from 'react-router-dom';
import { RoleBasedRedirect } from '@/components';

// For route arrays, we import them normally since they're configuration, not components
import ToolsPage from './tools/routes';
import UsersPage from './users/routes';
import TermsPage from './terms/routes';
import DashboardPage from './dashboard/routes';
import RolesPage from './roles/routes';
import Slideshow from './slideshow/routes';
import Tasks from './tasks/routes';
import Profile from './profile/routes';
import DnaRoutes from './dna/routes';
import TopicRoutes from './topic/routes';
import CoursesRoutes from './course/routes';
import NotificationRoutes from './notification/routes';
import StatisticsDashboardPage from './statistics-dashboard/routes';
import ContnetLibraryRoutes from './content-library/routes';

// Content Pages - these can be lazy loaded since they're components
const ContentNeed = lazy(() => import('./content-need/pages/list'));
const ProgramPlanList = lazy(() => import('./program/pages/list'));
const CourseGroupList = lazy(() => import('./course-group/pages/list'));

export default [
  {
    path: 'app',
    element: <AppMainLayout />,
    loader() {
      return {
        label: 'Home',
      };
    },
    children: [
      {
        path: '',
        element: (
          <RoleBasedRedirect>
            <Navigate to="terms" replace />,
          </RoleBasedRedirect>
        ),
      },
      // content routes
      {
        path: 'my-content',
        element: <Outlet />,
        loader() {
          return {
            label: 'breadcrumb.myContentPage.MyContent',
          };
        },
        children: [
          {
            path: '',
            element: <Navigate to="DNAs" />,
          },
          // Default
          ...DnaRoutes,
          ...TopicRoutes,
          ...CoursesRoutes,
          {
            path: 'content-need',
            element: <ContentNeed />,
            loader() {
              return {
                label: 'breadcrumb.myContentPage.contentNeed',
                title: 'breadcrumb.myContentPage.contentNeed',
              };
            },
          },

          {
            path: 'course-group',
            element: <CourseGroupList />,
            loader() {
              return {
                label: 'breadcrumb.myContentPage.courseGroup',
                title: 'breadcrumb.myContentPage.courseGroup',
              };
            },
          },

          {
            path: 'program-plan',
            element: <ProgramPlanList />,
            loader() {
              return {
                label: 'breadcrumb.myContentPage.programPlan',
                title: 'breadcrumb.myContentPage.programPlan',
              };
            },
          },
        ],
      },
      // Child Routes
      ...ToolsPage,
      ...UsersPage,
      ...DashboardPage,
      ...StatisticsDashboardPage,
      ...TermsPage,
      ...RolesPage,
      ...Slideshow,
      ...Tasks,
      ...Profile,
      ...ContnetLibraryRoutes,
      ...NotificationRoutes,
    ],
  },
];
