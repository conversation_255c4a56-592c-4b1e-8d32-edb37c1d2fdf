import { useFetchList, Icon, ProtectedComponent } from '@/index';
import { useMemo, useState } from 'react';

import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';
import { useTranslation } from 'react-i18next';
import { useDeleteContentNeed } from '@/modules/app/content-need/apis/queries';
import { Button } from '@/components/ui/button';
import { useContentTablesTabs } from '../../content-tabs';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import i18n from '@/utils/i18n';
import { localizePresenter } from '@/utils/helpers';
function DnaList() {
  //  Hooks
  const { mutate: deleteContent, isPending: Deleting, variables: variant } = useDeleteContentNeed();
  const contentTablesTabs = useContentTablesTabs();

  const { t } = useTranslation();

  const navegate = useLanguageNavigate();

  const { ready, loading, list, count, refresh, search, filters, pagination } = useFetchList(
    '/content',
    'content-needs',
    {
      search: '',
      pagination: {
        page_num: 1,
        page_size: 30,
      },
    }
  );

  const CollapsibleContent = ({ content }: { content: string }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    const previewLength = 70;
    const needsCollapse = content && content.length > previewLength;

    const toggleExpand = () => {
      setIsExpanded(!isExpanded);
    };

    if (!needsCollapse) {
      return <div>{content}</div>;
    }

    return (
      <div>
        <div>{isExpanded ? content : `${content.substring(0, previewLength)}...`}</div>
        <Button variant="link" onClick={toggleExpand} className="text-primary text-xs mt-1 p-0 h-auto">
          {isExpanded ? t('dnaArticulate.table.seeLess') : t('dnaArticulate.table.seeMore')}
        </Button>
      </div>
    );
  };

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'organization_vision',
        header: t('contentNeed.table.job_title'),
        width: '480px',
        cell: ({ row, level, index }) => {
          switch (level) {
            case 0:
              return (
                <div className="rtl:text-right font-medium">
                  <CollapsibleContent content={row.organization_vision} />
                </div>
              );
            case 1:
              return (
                <div className="flex gap-2">
                  <p> {index}.</p>
                  <div className="space-y-3 ">
                    <p className="font-medium">{row.title}</p>
                    <p>{row.learning_outcome}</p>
                  </div>
                </div>
              );
          }
        },
      },
      {
        accessorKey: 'skill',
        header: t('Skills'),
        width: '620px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return (
                <div className="grid grid-cols-[0.5fr_3fr_2fr_1fr_1fr] gap-4 p-4">
                  <div className="col-span-5 grid grid-cols-[0.5fr_3fr_2fr_1fr_1fr] text-sm pb-2 text-gray-500 border-b border-gray-400">
                    <div>#</div>
                    <div>{t('Skills title')}</div>
                    <div>{t('Skills correlation')}</div>
                    <div>{t('Skills type')}</div>
                    <div>{t('contentNeed.steps.skills.table.levels')}</div>
                  </div>

                  {row.skills.map((skill: any, index: number) => (
                    <>
                      <div className="col-span-5 grid grid-cols-[0.5fr_3fr_2fr_1fr_1fr] rtl:text-right items-center">
                        <div>{index + 1}</div>
                        <div className="w-[200px] flex gap-1">
                          <Badge variant="outline" className="font-light">
                            {skill.title}
                          </Badge>
                        </div>
                        <div>{skill.correlation}</div>
                        <div>{skill.type}</div>
                        <div>{localizePresenter(skill.level, i18n.language)}</div>
                      </div>
                      <hr className="col-span-5 border-gray-200" />
                    </>
                  ))}
                </div>
              );
          }
        },
      },
      {
        accessorKey: 'roles',
        header: t('Roles title'),
        width: '400px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return (
                <ul className="rtl:text-right space-y-2">
                  {row.roles.map((role: any, index: number) => (
                    <li className="list-decimal" key={index}>
                      {role.title}
                    </li>
                  ))}
                </ul>
              );
          }
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex gap-1">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Icon
                          onClick={() => {
                            navegate(`/app/tools/content-need?contentId=${row.id}`);
                          }}
                          icon="basil:edit-outline"
                          width={25}
                          className="text-primary cursor-pointer"
                        />
                      </TooltipTrigger>
                      <TooltipContent>{t('edit')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <ProtectedComponent requiredPermissions={'content_create'}>
                    <Button loading={Deleting && row.id === variant} disabled={Deleting} variant="ghost" size={'icon'}>
                      <Icon
                        onClick={() => {
                          deleteContent(row.id, { onSuccess: () => refresh() });
                        }}
                        width="22"
                        className="text-red-500 cursor-pointer"
                        icon="gg:trash"
                      />
                    </Button>
                  </ProtectedComponent>
                </div>
              );
          }
        },
      },
    ];
  }, [Deleting, variant, t]);

  return (
    <>
      <Table
        loading={loading}
        rows={list}
        columns={columns}
        nestedConfig={{
          enabled: true,
          childProperties: ['jobs'],
        }}
      >
        <TableContent>
          <TableContentHeader tabs={contentTablesTabs} className="flex justify-end">
            <Button
              type="button"
              size={'sm'}
              onClick={() => {
                navegate('/app/tools/content-need');
              }}
            >
              {t('Create New Content need')}
            </Button>
          </TableContentHeader>
          <TableContentBody />
        </TableContent>
      </Table>
    </>
  );
}

export default DnaList;
