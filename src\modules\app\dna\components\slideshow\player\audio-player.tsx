import React, { useRef, useState, useEffect, memo } from 'react';
import { Button } from '@/components/ui/button';
import { Icon } from '@/index';
import { useAtom } from 'jotai';
import { ISlide } from '@/modules/app/dna/apis/slideshow/types';
import {
  currentTimeAtom,
  currentTrackIndexAtom,
  isControlsVisibleAtom,
  isFullscreenAtom,
  totalDurationAtom,
} from '../../../store';
import ProgressBar from './progress-bar';
import PlayerSettings from './player-settings';

interface AudioPlayerProps {
  audioTracks: { url: string; duration: number; startTime: number; index: number }[];
  slides: ISlide[];
  onTrackChange: (trackIndex: number) => void;
  toggleFullscreen: () => void;
  setAudioTracks: (x: any) => void;
}

const icons = {
  play: <Icon icon="solar:play-bold" className="text-primary" width={20} />,
  pause: <Icon icon="solar:pause-bold" className="text-primary" width={20} />,
  volumeHigh: <Icon icon="ph:speaker-high-bold" width={20} />,
  volumeLow: <Icon icon="ph:speaker-low-bold" width={20} />,
  volumeMute: <Icon icon="ph:speaker-x-bold" width={20} />,
};

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioTracks,
  slides,
  onTrackChange,
  toggleFullscreen,
  setAudioTracks,
}) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  // slideshow playe settings
  const [isControlsVisible] = useAtom(isControlsVisibleAtom);
  const [currentTrackIndex, setCurrentTrackIndex] = useAtom(currentTrackIndexAtom);
  const [totalDuration, setTotalDuration] = useAtom(totalDurationAtom);
  const [currentTime, setCurrentTime] = useAtom(currentTimeAtom);
  const [isFullscreen] = useAtom(isFullscreenAtom);
  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleTimeUpdate = (e: React.SyntheticEvent<HTMLAudioElement>) => {
    const currentAudio = e.target as HTMLAudioElement;
    const newTime = audioTracks[currentTrackIndex].startTime + currentAudio.currentTime;
    setCurrentTime(newTime);
  };

  const handleTrackEnd = () => {
    if (currentTrackIndex < audioTracks.length - 1) {
      setCurrentTrackIndex((prev) => prev + 1);
      onTrackChange(currentTrackIndex + 1);
    }
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      audioRef.current.paused ? audioRef.current.play() : audioRef.current.pause();
    }
  };

  const navigateToTrack = (trackIndex: number, timeWithinTrack: number = 0) => {
    setCurrentTrackIndex(trackIndex);
    onTrackChange(trackIndex);

    if (audioRef.current) {
      audioRef.current.src = audioTracks[trackIndex].url;
      audioRef.current.currentTime = timeWithinTrack;
      audioRef.current.play().catch(console.error);
    }
  };

  //  ${
  //           isControlsVisible
  //             ? 'opacity-100 translate-y-0 pointer-events-auto'
  //             : 'opacity-0 translate-y-full pointer-events-none'
  //         }
  return (
    <div
      className={`
        relative transition-all duration-500 ease-in-out
        ${isFullscreen ? 'mt-auto w-full px-4' : 'mt-4 px-8'}
        ${
          isControlsVisible
            ? 'opacity-100 translate-y-0 pointer-events-auto'
            : 'opacity-0 translate-y-full pointer-events-none'
        }
        before:content-[''] before:absolute before:bottom-0 before:left-0 before:right-0 
        before:h-48 before:bg-gradient-to-t before:from-background before:to-transparent 
        before:-z-50 before:pointer-events-none
      `}
    >
      <audio
        ref={audioRef}
        src={audioTracks[currentTrackIndex]?.url}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleTrackEnd}
        autoPlay
      />
      <div className="space-y-3">
        <ProgressBar
          navigateToTrack={navigateToTrack}
          audioTracks={audioTracks}
          totalDuration={totalDuration}
          slides={slides}
        />
        <div>
          <div className="flex justify-between items-center gap-2">
            <Button variant="ghost" onClick={togglePlayPause} className="p-2 play-slide">
              {audioRef.current?.paused ? icons.play : icons.pause}
            </Button>
            <div className="flex gap-3 text-gray-400">
              <div className="flex gap-1">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(totalDuration)}</span>
              </div>
              {/* // settings */}
              <PlayerSettings toggleFullscreen={toggleFullscreen} audioRef={audioRef} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioPlayer;
