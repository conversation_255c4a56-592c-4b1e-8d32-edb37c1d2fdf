import { lazy } from 'react';
import { ProtectedRoute } from '@/components';

const List = lazy(() => import('./pages/list'));

export default [
  {
    path: 'keys',
    element: (
      <ProtectedRoute requiredPermissions={'for_administration'}>
        <List />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.dashboard.keys',
        title: 'breadcrumb.dashboard.keys',
      };
    },
  },
];
