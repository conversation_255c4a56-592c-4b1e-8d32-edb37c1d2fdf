import { useState } from 'react';

import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

import { Button } from '@/components/ui/button';
import { Form, Card, UPLOAD_DNA_SOURCE, useForm, TextInput, FILE_ANALUZER, Api, useNotify, Icon } from '@/index';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
const FileAnalyzer = () => {
  // State
  const [loading, setLoading] = useState<any>(false);
  const [isLoading, setIsLoading] = useState<any>(false);
  const [data, setDate] = useState<any>(null);
  const [toggle, setToggle] = useState<any>('Web');

  // Hooks
  const { notify } = useNotify();
  const { t } = useTranslation();

  const { form, setFieldValue } = useForm({
    file_path: '',
    url: '',
    youtube_url: '',
    method: 0,
  });

  const handleFileUpload = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('dna', file);

      try {
        setLoading(true);
        const response = await Api.post(UPLOAD_DNA_SOURCE, formData);
        notify.success('File uploaded!');
        if (response.data.data.url) {
          setFieldValue('file_path')(response.data.data.url);
          setLoading(false);
        }
      } catch (error) {}
    }
  };

  const handleGenerate = async () => {
    if (form.method === 0) {
      notify.error('Please select engine type');
    } else {
      setDate(null);
      try {
        setIsLoading(true);
        const response = await Api.post(FILE_ANALUZER, form);
        setDate(response.data.data);
        notify.success('File Analyzed!');
      } catch (error: any) {
        notify.error(error.message);
        setIsLoading(false);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleToggleChange = (newValue: any) => {
    setToggle(newValue);
    if (newValue === 'Web') {
      setFieldValue('method')(1);
    }
  };

  return (
    <div>
      <Card className="p-4">
        <Form onSubmit={handleGenerate}>
          <div className="mb-4">
            <RadioGroup
              className="mt-5 flex flex-col sm:flex-row gap-4"
              onValueChange={setFieldValue('method', Number)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="1" id="r1" />
                <Label htmlFor="r1">
                  {' '}
                  <div className="flex gap-2 items-center text-base">
                    <div>{t('fileAnalyzer.engine')} 1</div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger type="button">
                          <Icon icon="solar:info-circle-broken" width={18} />
                        </TooltipTrigger>
                        <TooltipContent side="right" className="max-w-72">
                          <p>{t('fileAnalyzer.engineProment1')}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="2" id="r2" />
                <Label htmlFor="r2">
                  <div className="flex gap-2 items-center text-base">
                    <div>{t('fileAnalyzer.engine')} 2</div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger type="button">
                          <Icon icon="solar:info-circle-broken" width={18} />
                        </TooltipTrigger>
                        <TooltipContent side="right" className="max-w-72">
                          <p>{t('fileAnalyzer.engineProment2')}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>
          <div className="my-4 w-fit">
            <p className="text-sm font-medium">{t('dnaCreationPage.form.sources')}</p>
            <ToggleGroup
              className="pt-4"
              variant="outline"
              value={toggle || 'Url'}
              type="single"
              onValueChange={handleToggleChange}
            >
              <ToggleGroupItem value="URL" aria-label="Toggle URL">
                <p>{t('URL')}</p>
              </ToggleGroupItem>
              <ToggleGroupItem value="File" aria-label="Toggle File">
                <p>{t('File')}</p>
              </ToggleGroupItem>
              {form.method === 2 && (
                <ToggleGroupItem value="youtube_url" aria-label="Toggle Text">
                  <p>{t('youtube_url')}</p>
                </ToggleGroupItem>
              )}
            </ToggleGroup>
          </div>
          <div className="flex gap-4">
            {toggle === 'File' && (
              <div className="self-center">
                <div id="fileUpload" className="max-w-md">
                  <div className="mb-2 block">
                    <Label htmlFor="file">{t('uploadFile')} </Label>
                  </div>
                  <Input
                    className={`${loading ? 'border-[.5px] border-red-500' : ''}`}
                    type="file"
                    accept="application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    onChange={handleFileUpload}
                  />
                  <span className="block text-sm text-gray-500 mt-2">
                    {loading ? 'Your file is being uploading please wait' : 'Accepted file types: PDF, txt, csv'}{' '}
                  </span>
                </div>
              </div>
            )}
            {toggle === 'URL' && (
              <div className=" space-y-1">
                <div className="">
                  <TextInput
                    name="context"
                    label={t('dnaCreationPage.form.title')}
                    value={form.url}
                    onChange={(value: string) => {
                      setFieldValue('url')(value);
                      setFieldValue('youtube_url')('');
                      setFieldValue('file_path')('');
                    }}
                  />
                </div>
              </div>
            )}

            {toggle === 'youtube_url' && (
              <div className=" space-y-1">
                <div className="">
                  <TextInput
                    name="context"
                    label={t('dnaCreationPage.form.title')}
                    value={form.youtube_url}
                    onChange={(value: string) => {
                      setFieldValue('youtube_url')(value);
                      setFieldValue('url')('');
                      setFieldValue('file_path')('');
                    }}
                  />
                </div>
              </div>
            )}
          </div>
          <Button
            variant={'outline'}
            loading={loading || isLoading}
            disabled={loading || isLoading}
            className="min-w-[100px] mt-5"
            type="submit"
          >
            {t('analyze')}
          </Button>
        </Form>
        <div className="mt-5">
          <ul className="pl-4">
            {data?.split('\n').map((item: any, index: any) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default FileAnalyzer;
