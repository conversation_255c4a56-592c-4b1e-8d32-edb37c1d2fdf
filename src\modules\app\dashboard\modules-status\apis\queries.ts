import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNotify } from '@/index';
import { getModulesStatus, updateModulePhase, updateModuleStatus, getModuleStatusByTitle } from './endpoints';
import { useTranslation } from 'react-i18next';
export const useGetModulesStatus = () => {
  return useQuery({
    queryKey: ['modulesStatus'],
    queryFn: getModulesStatus,
  });
};

export const useGetModuleStatusByTitle = (title: string) => {
  return useQuery({
    queryKey: ['signleModuleStatus', title],
    queryFn: () => getModuleStatusByTitle(title),
  });
};

export const useUpdateModulesStatus = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: updateModuleStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['modulesStatus'] });
      notify.success(t('modulesStatusUpdated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateModulePhase = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();

  return useMutation({
    mutationFn: updateModulePhase,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['modulesStatus'] });
      notify.success(t('modulesPhaseUpdated'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
