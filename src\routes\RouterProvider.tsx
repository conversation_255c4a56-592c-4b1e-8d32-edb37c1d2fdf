import { Navigate, Outlet, useLocation, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import globalHooks from '@/services/global-router';
import { useGetUser } from '@/modules/auth/apis/queries';
import { userAtom } from '@/modules/auth/store';
import { useAtom } from 'jotai';
import { useNotify } from '@/hooks';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { useTranslation } from 'react-i18next';
import { PageLoader } from '@/components';

const AppWrapper = () => {
  //hooks
  const [user, setUser] = useAtom(userAtom);
  const navigate = useLanguageNavigate();
  const location = useLocation();
  const { notify } = useNotify();
  const supportedLanguages = ['en', 'ar'];
  const { i18n } = useTranslation();
  const { lng } = useParams();
  globalHooks.globalRouter.navigate = navigate;
  globalHooks.globalRouter.location = location;
  globalHooks.globalNotify = notify.error;

  const { data, isLoading, isError } = useGetUser();

  const isAuthenticated = Boolean(localStorage.getItem('token'));

  // State to track when the user data is ready
  const [isReady, setIsReady] = useState(false);
  useEffect(() => {
    if (!supportedLanguages.includes(lng as any)) {
    } else if (i18n.language !== lng) {
      i18n.changeLanguage(lng);
    }
  }, [lng, i18n, navigate]);
  useEffect(() => {
    if (isLoading) return; // Wait until loading is finished
    if (isError || !data) {
      if (!location.pathname.includes('/auth')) {
        navigate('/auth/login');
      }
      localStorage.removeItem('token');
      setIsReady(true);
    } else {
      setUser(data);
      setIsReady(true);
    }
  }, [isError, isLoading, data]);

  // Prevent rendering the routes until user data is ready
  if (!isReady) return <PageLoader fullScreen />;

  if (!isAuthenticated && !location.pathname.includes('/auth')) {
    return <Navigate to="/auth/login" replace />;
  }
  return <Outlet />;
};

export default AppWrapper;
