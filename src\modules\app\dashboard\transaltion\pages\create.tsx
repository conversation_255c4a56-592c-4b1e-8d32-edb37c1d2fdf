import React from 'react';
import { Form, Modal, useForm, TextInput, useValidate } from '@/index';
import { useGetLanguages, useCreateTranslationKey } from '@/modules/app/dashboard/transaltion/apis/queries';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
interface Props {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const CreateKeyWithTranslationsDialog = ({ isOpen, onOpenChange }: Props) => {
  const { data: languages } = useGetLanguages();
  const { mutate: createKeyWithTranslations, isPending } = useCreateTranslationKey();
  const { t } = useTranslation();
  const { isRequired } = useValidate();

  const initialFormState = languages?.reduce(
    (acc: any, lang: any) => {
      acc[lang.code] = '';
      return acc;
    },
    { keyName: '' }
  );

  const { form, setFieldValue } = useForm(initialFormState || {});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!form.keyName || languages?.some((lang) => !form[lang.code])) {
      return;
    }

    const payload = {
      key: form.keyName,
      translations: languages?.map((lang) => ({
        language_id: lang.id,
        value: form[lang.code],
      })),
    };

    createKeyWithTranslations(payload as any, {
      onSuccess: () => {
        onOpenChange(false);
      },
    });
  };

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('dashboardPage.keys.newKeyDialog.title')}>
      <Form onSubmit={handleSubmit} className="space-y-4">
        <TextInput
          name="keyName"
          label={t('dashboardPage.keys.newKeyDialog.value')}
          value={form.keyName}
          onChange={setFieldValue('keyName')}
          validators={[isRequired()]}
        />

        {languages?.map((lang) => (
          <TextInput
            key={lang.id}
            name={lang.code}
            label={`${t('dashboardPage.keys.newKeyDialog.translation')} (${lang.name})`}
            value={form[lang.code]}
            onChange={setFieldValue(lang.code)}
            validators={[isRequired()]}
          />
        ))}

        <div className="flex justify-end gap-3 mt-3">
          <Button onClick={() => onOpenChange(false)} variant={'outline'} className="min-w-[100px] mt-4">
            {t('cancel')}
          </Button>
          <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
            {t('create')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default CreateKeyWithTranslationsDialog;
