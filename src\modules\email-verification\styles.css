body {
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: radial-gradient(circle, #a18cd1 0%, #fbc2eb 100%);
  color: #333;
}

.container {
  background: #ffffff;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 450px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.container:hover {
  transform: translateY(-8px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.status.success {
  color: #4caf50;
  font-weight: bold;
}

.status.error {
  color: #f44336;
  font-weight: bold;
}

.status.info {
  color: #ff9800;
  font-weight: bold;
}

.button {
  display: inline-block;
  margin-top: 25px;
  padding: 15px 30px;
  background-color: #6200ea;
  color: #ffffff;
  text-decoration: none;
  border-radius: 12px;
  font-size: 16px;
  transition: background-color 0.3s ease, transform 0.3s ease;
}

.button:hover {
  background-color: #3700b3;
  transform: scale(1.1);
}

.icon {
  font-size: 60px;
  margin-bottom: 20px;
  color: #6200ea;
}

.container h1 {
  font-size: 26px;
  margin-bottom: 15px;
  color: #333;
}

.container p {
  font-size: 18px;
  margin-bottom: 20px;
  color: #555;
}
