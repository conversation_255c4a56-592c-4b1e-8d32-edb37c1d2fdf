import { useMemo, useState } from 'react';
import { useFetchList, Icon, useNotify, useConfirmDialog, COURSES, ProtectedComponent } from '@/index';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
  TableBulkActions,
} from '@/components/theTable';
import {
  convertStatusToFilterEnumByTitle,
  dateAndTimeFormat,
  formatUserByNameAndEmail,
  getAllNames,
  getContentStatusStyle,
} from '@/utils/helpers';
import { ICourse } from '@/modules/app/course/types';
import { useDeleteCourse, useDeleteCourses } from '@/modules/app/course/apis/queries';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, Toolt<PERSON>Provider, TooltipTrigger } from '@/components/ui/tooltip';
import LanguageLink from '@/components/language-link';
import EditDna from '../../components/edit-dna';
import AssignTaskDialog from '../components/assign-task-dialog';
import { CourseGroupDialog } from '../components/createCourseGroupDialog';
import { useGetModulesStatus } from '../../dashboard/modules-status/apis/queries';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import { useGetOperations } from '../../dashboard/operation/apis/queries';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { useContentTablesTabs } from '../../content-tabs';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
const CoursesList = () => {
  //  State
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [editDialog, setEditDialog] = useState<any>(false);
  const [danData, setDanData] = useState<any>(null);
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<ICourse | null>(null);
  const [open, setOpen] = useState(false);
  const [editData, setEditData] = useState<any>(null);
  //  Hooks
  const { confirm } = useConfirmation();
  const { mutate: deleteCourse } = useDeleteCourse();
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { t, i18n } = useTranslation();
  const { data: operations } = useGetOperations();
  const operationsIds = useMemo(() => operations?.map((operation) => operation.id) || [], [operations]);
  const contentTablesTabs = useContentTablesTabs();

  const { data: modulesStatus } = useGetModulesStatus();
  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'Course', i18n.language);
  const { loading, list, count, refresh, search, filters, pagination } = useFetchList(COURSES, 'courses', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      status_id: {
        placeholder: 'dnaConentPgae.statusFilter',
        dynamicEnum: statusFilter,
      },
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'dnaConentPgae.userFIlter',
            },
          }
        : {}),
    },
  });

  const labelKey = i18n.dir() === 'ltr';

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('cousrePlanContentPage.courseDeleteText')}</p>
      </div>
    );
  };

  const handleDeleteCousre = (id: string) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        deleteCourse(id, {
          onSuccess: () => {
            notify.success('Courses deleted successfully');
          },
        });
      },
    });
  };
  const handleCreateCourseGroup = () => {
    setEditData({ courses: selectedRows });
    setOpen(true);
  };

  const ConfirmDeleteText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>{t('deleteCourseConfirmation')}</p>
      </div>
    );
  };
  const { mutate: deleteCourses } = useDeleteCourses();
  const handleDeleteCourses = () => {
    const ids = selectedRows.map((row: any) => row.id);
    deleteCourses(ids, {
      onSuccess: () => {
        refresh();
        hideConfirm();
      },
    });
  };
  const showDeleteAllConfirm = async () => {
    showConfirm(ConfirmDeleteText(), { onConfirm: handleDeleteCourses });
  };

  // Computed values
  const bulkActions = useMemo(() => {
    return [
      {
        icon: 'gg:trash',
        iconColor: 'text-[#f05252]',
        label: 'Delete',
        action: showDeleteAllConfirm,
      },
      {
        icon: 'uit:layer-group',
        iconColor: 'text-[#3971EC]',
        label: t('courseGroup.createNewCourseGroup'),
        action: handleCreateCourseGroup,
      },
    ];
  }, [selectedRows]);

  const columns = useMemo((): ITableColumn<ICourse>[] => {
    return [
      {
        accessorKey: 'status',
        header: t('cousrePlanContentPage.table.status'),
        width: '250px',
        cell: ({ row }) => getContentStatusStyle(row?.courseStatus),
      },
      {
        accessorKey: 'title',
        header: t('cousrePlanContentPage.table.title'),
        width: '200px',
        cell: ({ row }) => (
          <div className="w-full justify-between flex rtl:text-right">
            <LanguageLink to={`/app/my-content/courses/${row.id}`} className="font-semibold text-primary">
              {row.title}
            </LanguageLink>
          </div>
        ),
      },
      {
        accessorKey: 'subject',
        header: t('topicContentPage.table.subject'),
        width: '180px',
        cell: ({ row }) => getAllNames(row.subject, i18n.language),
      },
      {
        accessorKey: 'audience',
        header: t('topicContentPage.table.audience'),
        width: '200px',
        cell: ({ row }) => getAllNames(row.audience, i18n.language),
      },
      {
        accessorKey: 'language',
        header: t('topicContentPage.table.language'),
        width: '100px',
        cell: ({ row }) => getAllNames(row.language, i18n.language),
      },
      {
        accessorKey: 'level',
        header: t('cousrePlanContentPage.table.level'),
        width: '100px',
        cell: ({ row }) => (
          <p className="rtl:text-right">{labelKey ? row.difficultyLevel.name_en : row.difficultyLevel.name_ar}</p>
        ),
      },
      {
        accessorKey: 'date',
        header: t('cousrePlanContentPage.table.date'),
        width: '110px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'user',
        header: t('cousrePlanContentPage.table.author'),
        width: '200px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.user);
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '100px',
        cell: ({ row }) => (
          <div className="flex  gap-1 ">
            {/* Assign task Button */}
            {row.tasks.can_create_new_task && (
              <ProtectedComponent requiredPermissions={'task_create'}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => {
                          setIsTaskDialogOpen(true);
                          setSelectedCourse(row);
                        }}
                      >
                        <Icon
                          className="text-primary"
                          icon="material-symbols:assignment-add-outline-rounded"
                          width={25}
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('cousrePlanContentPage.assignTask')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ProtectedComponent>
            )}
            {/* Delete Button */}
            <ConditionalComponent
              status={row?.courseStatus}
              wantedStatus={[
                StatusClass.COURSE.EDIT.DRAFT,
                StatusClass.COURSE.EDIT.FEEDBACK,
                StatusClass.COURSE.EDIT.NO_CONTENT,
              ]}
            >
              <ProtectedComponent requiredPermissions={'course_delete'}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button size="icon" variant="ghost" onClick={() => handleDeleteCousre(row.id)}>
                        <Icon icon="gg:trash" width={25} className="text-red-500" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('delete')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ProtectedComponent>
            </ConditionalComponent>
          </div>
        ),
      },
    ];
  }, [selectedRows, operationsIds, t, i18n]);

  return (
    <>
      <Table
        rows={list}
        columns={columns}
        loading={loading}
        multipleSelect={{
          selectedRows,
          setSelectedRows,
        }}
      >
        <TableContent>
          <TableContentHeader tabs={contentTablesTabs}>
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="flex ms-auto gap-3">
              <ProtectedComponent requiredPermissions={'course_create'}>
                <LanguageLink to={'/app/tools/course-plan'}>
                  <Button>
                    <span className="flex items-center gap-2">
                      <Icon icon="ic:round-add" width={17} />
                      <span className="text-sm">{t('cousrePlanContentPage.createNewCourse')}</span>
                    </span>
                  </Button>
                </LanguageLink>
              </ProtectedComponent>
              <div>{selectedRows.length > 0 && <TableBulkActions actions={bulkActions} />}</div>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {editDialog && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false), setDanData(null);
          }}
          data={danData}
        />
      )}
      {isTaskDialogOpen && (
        <AssignTaskDialog
          onOpen={isTaskDialogOpen}
          onClose={() => {
            setIsTaskDialogOpen(false), setSelectedCourse(null);
          }}
          course={selectedCourse}
        />
      )}
      {open && (
        <CourseGroupDialog
          onOpen={open}
          opOpenChange={() => {
            setOpen(false);
            setEditData(null);
          }}
          editData={editData}
          isCourseList
        />
      )}
    </>
  );
};

export default CoursesList;
