import React from 'react';
import { Form, Modal, useForm, TextInput, useValidate, Textarea, DnaEnums } from '@/index';
import { useCreateMetadata } from '@/modules/app/dashboard/metadata/apis/queries';

import { useTranslation } from 'react-i18next';
import { ComboboxInput } from '@/components/form/inputs/combo-box';

import { useGetLanguages } from '@/modules/app/dashboard/transaltion/apis/queries';
import { Button } from '@/components/ui/button';

const CreateMetadataDialog = ({ isOpen, onOpenChange, refresh }: any) => {
  const { data: languages } = useGetLanguages();
  const { mutate: createMetadata, isPending } = useCreateMetadata();
  const { t } = useTranslation();
  const { isRequired } = useValidate();

  const typeOptions = [
    { label: 'Audio', value: 'audio' },
    { label: 'Video', value: 'video' },
    { label: 'Text', value: 'text' },
  ];

  const initialFormState = languages?.reduce(
    (acc: any, lang: any) => {
      acc[lang.code] = ''; // For title translations
      return acc;
    },
    { type: '', description: null } // Title in all languages, description only in English
  );

  const { form, setFieldValue } = useForm(initialFormState || {});

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const payload: any = {
      type: form.type,
      description: form.description, // Only English description
    };
    languages?.forEach((lang: any) => {
      payload[`name_${lang.code}`] = form[lang.code];
    });

    createMetadata(payload as any, {
      onSuccess: () => {
        refresh();
        onOpenChange(false);
      },
    });
  };

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('metadataPage.newMetadataDialog.title')}>
      <Form onSubmit={handleSubmit} className="space-y-4">
        {/* Dropdown for type (audio, video, etc.) */}
        <ComboboxInput
          placeholder={t('selectType')}
          label={t('type')}
          options={DnaEnums.metadata}
          value={form.type}
          onChange={setFieldValue('type')}
          dropIcon
          validators={[isRequired()]}
        />

        {/* Title translations for all available languages */}
        {languages?.map((lang) => (
          <TextInput
            key={lang.id}
            name={lang.code}
            label={`${t('metadataPage.title')} (${lang.name})`}
            value={form[lang.code]}
            onChange={setFieldValue(lang.code)}
            validators={[isRequired()]}
          />
        ))}

        {/* Description in English */}
        <Textarea
          rows={5}
          label={t('metadataPage.description')}
          value={form.description}
          onChange={setFieldValue('description')}
          className="w-full p-2 border rounded"
          validators={[isRequired()]}
        />

        {/* Form buttons */}
        <div className="flex justify-end gap-3 mt-3">
          <Button onClick={() => onOpenChange(false)} variant={'outline'} className="min-w-[100px] mt-4">
            {t('cancel')}
          </Button>
          <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
            {t('create')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default CreateMetadataDialog;
