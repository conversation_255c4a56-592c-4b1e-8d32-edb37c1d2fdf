{
  "compilerOptions": {
    "target": "es5", // Specify ECMAScript target version: "ES5"
    "lib": ["dom", "dom.iterable", "esnext"], // Specify library files to be included in the compilation
    "allowJs": true, // Allow JavaScript files to be compiled
    "skipLibCheck": true, // Skip type checking of declaration files
    "esModuleInterop": true, // Enables emit interoperability between CommonJS and ES Modules
    "allowSyntheticDefaultImports": true, // Allow default imports from modules with no default export
    "strict": true, // Enable all strict type-checking options
    "forceConsistentCasingInFileNames": true, // Disallow inconsistently-cased references to the same file
    "noFallthroughCasesInSwitch": true, // Report errors for fallthrough cases in switch statements
    "module": "esnext", // Specify module code generation: "ESNext"
    "moduleResolution": "node", // Specify module resolution strategy: "Node"
    "resolveJsonModule": true, // Include modules imported with .json extension
    "isolatedModules": true, // Ensure that each file can be safely transpiled without relying on other imports
    "noEmit": true, // Do not emit outputs
    "jsx": "react-jsx", // Specify JSX code generation: "react-jsx"
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"],
      "/src*": ["src/*"]
    }
  },

  "include": ["src"], // Include the src folder in the compilation
  "exclude": ["node_modules"] // Exclude the node_modules folder from the compilation
}
