import { DragEndEvent } from '@dnd-kit/core';

export interface ITableCellParams<Row> {
  row: Row;
  level?: number;
  parentRow?: Row;
  index?: number;
}

export interface ITableColumn<Row> {
  accessorKey: string;
  header: string | React.ReactNode;
  width?: string;
  cell?: (params: ITableCellParams<Row>) => React.ReactNode;
  tooltip?: boolean;
}

export interface Button {
  label: string;
  color?: string;
  icon?: string;
  onClick?: () => void;
}

export interface TableContextValue {
  selectedRows: any[];
  onSelectRow: (row: any) => void;
  onSelectAll: (event: React.ChangeEvent<HTMLInputElement>) => void;
  isRowSelected: (rowId: string | number) => boolean;
  isMultipleSelect: boolean;

  isDraggable: boolean;
  rows: any[];
  setRows: (rows: any[]) => void;
  columns: ITableColumn<any>[];
  setColumns?: any;
  rowKey: string;
  loading?: boolean;
  nestedConfig: Required<NestedConfig>;
  draggingConfig?: DraggingConfig;
  expandedRows: Set<string>;
  setExpandedRows: any;
}

export interface NestedConfig {
  enabled?: boolean;
  childProperties?: string[];
}
export interface DraggingConfig {
  enabled?: boolean;
  onDragEnd?: (active: any, over: any) => void;
}

export interface TableError {
  type: 'INVALID_NESTING' | 'MAX_DEPTH_EXCEEDED' | 'INVALID_DATA_TYPE';
  message: string;
  rowId: string;
  level: number;
}

export interface TableProps extends React.HTMLAttributes<HTMLDivElement> {
  rows: any[];
  columns: ITableColumn<any>[];
  rowKey?: string;
  multipleSelect?: {
    selectedRows: any[];
    setSelectedRows: (rows: any[]) => void;
  };
  draggingConfig?: {
    enabled?: boolean;
    onDragEnd?: (active: any, over: any) => void;
  };
  loading?: boolean;
  nestedConfig?: NestedConfig;
  children: React.ReactNode;
}

export interface PaginationProps {
  count: number;
  page: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export interface IFilter {
  label: string;
  key: string;
  placeholder: string;
  options: Array<{ label: string; value: any }>;
  selectedValue: any;
  onChange: (value: any) => void;
  reset: () => void;
  type?: 'single' | 'multi';
}
