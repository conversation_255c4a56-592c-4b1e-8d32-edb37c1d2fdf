import { useEffect, useState } from 'react';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { DnaEnums } from '@/services';
import { Form, Modal, useForm, useValidate } from '@/index';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  useAddTransaltionSlideshow,
  useGetAllowedLanguages,
  useGetTranslatedSlideshowAllowedLanguages,
} from '../../apis/slideshow/queries';
import { generateEnum } from '@/utils/helpers';
import { useGenerateSlideshow } from '../../apis/queries';
import { ISingleSlideShow } from '../../apis/slideshow/types';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  id: number;
  data: ISingleSlideShow[];
}
const SlideshowGenerationDialog = ({ open, onOpenChange, id, data }: IProps) => {
  const { t, i18n } = useTranslation();
  const { data: allowedLanguages } = useGetAllowedLanguages(id);
  const { isRequired } = useValidate();
  const [currentTab, setCurrentTab] = useState('parentSlideshowTab');
  const allowedLanguagesCurrentLabel = i18n.language === 'en' ? 'name_en' : 'name_ar';
  const { form, setFieldValue } = useForm({
    id: id,
    slideshow_sound_type: 'gpt-4o',
    language_id: 0,
    slideshow_id: 0,
    model: 'gpt-4o',
  });
  const { data: translatedAllowedLanguages, isPending: isGettingAllowedTranslatedLanguages } =
    useGetTranslatedSlideshowAllowedLanguages({ dnaId: id, slideshowId: form.slideshow_id });

  useEffect(() => {
    setFieldValue('id')(id);
  }, [id]);
  const { mutate, isPending } = useGenerateSlideshow();
  const { mutate: addnew, isPending: isAdding } = useAddTransaltionSlideshow();

  const handleSubmitParentSlide = async () => {
    mutate(form, {
      onSuccess: () => {
        onOpenChange(false);
      },
    });
  };

  const handleSubmitChildSlide = () => {
    addnew(form, {
      onSuccess: () => {
        onOpenChange(false);
      },
    });
  };

  return (
    <Modal open={open} width={450} onOpenChange={onOpenChange} modalHeader={t('Generate slideshow')}>
      <Tabs defaultValue="parentSlideshowTab" className="w-full" value={currentTab} onValueChange={setCurrentTab}>
        <TabsList className="flex w-full mb-4">
          <TabsTrigger value="parentSlideshowTab" className="w-full">
            {t('slideshow.originalSlide')}
          </TabsTrigger>
          <TabsTrigger className="w-full" value="childSlideshowTab">
            {t('slideshow.translatedSlide')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="parentSlideshowTab">
          <Form onSubmit={handleSubmitParentSlide}>
            <ComboboxInput
              name="language_id"
              placeholder={t('slideshow.allwowedLanguagesPlaceholder')}
              label={t('slideshow.allwowedLanguages')}
              options={generateEnum(allowedLanguages || [], 'id', allowedLanguagesCurrentLabel)}
              value={form.language_id}
              onChange={setFieldValue('language_id')}
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="slideshow_sound_type"
              placeholder={t('slideshow.voiceTypePlaceholder')}
              label={t('slideshow.voiceType')}
              options={DnaEnums.voice_type}
              value={form.slideshow_sound_type}
              onChange={setFieldValue('slideshow_sound_type')}
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="model"
              placeholder={t('slideshow.model')}
              label={t('slideshow.model')}
              options={DnaEnums.ai_base_model}
              value={form.model}
              onChange={setFieldValue('model')}
              validators={[isRequired()]}
            />

            <div className="flex justify-end gap-2 mt-5">
              <Button type="button" variant={'outline'} onClick={() => onOpenChange(false)}>
                {t('slideshow.cancel')}
              </Button>
              <Button loading={isPending || isAdding}>{t('slideshow.generateSlides')}</Button>
            </div>
          </Form>
        </TabsContent>
        <TabsContent value="childSlideshowTab">
          <Form onSubmit={handleSubmitChildSlide}>
            <ComboboxInput
              placeholder={t('slideshow.generateSlides.selectSlideshow')}
              label={t('slideshow.generateSlides.selectSlideshow')}
              options={data}
              optionLabelKey="title"
              optionValueKey="id"
              value={form.slideshow_id}
              onChange={setFieldValue('slideshow_id')}
              validators={[isRequired()]}
              name="slideshow_id"
            />
            <ComboboxInput
              placeholder={t('slideshow.generateSlides.selectLanguage')}
              label={t('slideshow.generateSlides.translatedLanguage')}
              options={generateEnum(translatedAllowedLanguages || [], 'id', allowedLanguagesCurrentLabel)}
              value={form.language_id}
              onChange={setFieldValue('language_id')}
              validators={[isRequired()]}
              name="language_id"
            />

            <div className="flex justify-end gap-2 mt-5">
              <Button type="button" variant={'outline'} onClick={() => onOpenChange(false)}>
                {t('slideshow.cancel')}
              </Button>
              <Button loading={isPending || isAdding}>{t('slideshow.generateSlides')}</Button>
            </div>
          </Form>
        </TabsContent>
      </Tabs>
    </Modal>
  );
};

export default SlideshowGenerationDialog;
