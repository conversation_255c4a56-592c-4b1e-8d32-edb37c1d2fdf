import { Card, ComboboxInput, Modal, Textarea } from '@/components';
import { Form } from '@/components';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useForm, useValidate } from '@/hooks';
import { DnaEnums } from '@/index';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { Check, ChevronDown } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { useAddNewSlide } from '../../apis/queries';
import { useParams } from 'react-router-dom';
import { SLIDE_TEMPLATES } from './sildeshow-helpers';

const AddSlideDialog = ({
  slideshowId,
  next_slide_id,
  open,
  onClose,
}: {
  slideshowId: number;
  next_slide_id: number | null;
  open: boolean;
  onClose: () => void;
}) => {
  const { form, setFieldValue } = useForm({
    template: null,
    prompt: '',
    voice_over_sound: null,
  });
  const { isRequired } = useValidate();
  const { t } = useTranslation();
  const { mutate: addNewSlide, isPending } = useAddNewSlide();
  const { dnaId, taskId } = useParams();
  const handleSubmit = () => {
    console.log({
      taskCode: taskId || '',
      dnaId: dnaId || '',
      template: form.template,
      prompt: form.prompt,
      voice_over_sound: form.voice_over_sound,
      slideshow_id: slideshowId,
      next_slide_id,
    });
    addNewSlide(
      {
        taskCode: taskId || '',
        dnaId: dnaId || '',
        template: form.template,
        prompt: form.prompt,
        voice_over_sound: form.voice_over_sound,
        slideshow_id: slideshowId,
        next_slide_id,
      },
      {
        onSuccess: () => {
          onClose();
        },
      }
    );
  };
  return (
    <div>
      <Modal modalHeader="Add Slide" open={open} onOpenChange={onClose}>
        <Form onSubmit={handleSubmit} setFieldValue={setFieldValue} className="flex flex-col gap-4">
          <TemplateSelector label={t('Select Template')} value={form.template} onChange={setFieldValue('template')} />
          <ComboboxInput
            placeholder={t('Select voice sound type')}
            label={'Voice sound type'}
            options={DnaEnums.voice_type}
            value={form.voice_over_sound}
            onChange={setFieldValue('voice_over_sound')}
            dropIcon
          />
          <Textarea
            name="prompt"
            label={t('slieshow.slideDescription')}
            placeholder={t('slieshow.slideDescription')}
            value={form.prompt}
            onChange={setFieldValue('prompt')}
            validators={[isRequired()]}
          />
          <div className="flex gap-2 border-t pt-4 border-border justify-end">
            <Button variant="secondary" onClick={onClose}>
              Cancel
            </Button>
            <Button disabled={isPending} loading={isPending} className="px-10">
              {isPending ? 'Adding...' : 'Save'}
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AddSlideDialog;

interface TemplateSelectorProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
}

export const TemplateSelector = ({ value, onChange, label }: TemplateSelectorProps) => {
  const [open, setOpen] = useState(false);
  const selectedTemplate = SLIDE_TEMPLATES.find((t) => t.value === value);

  return (
    <div className="w-full">
      {label && <Label className="block mb-2">{label}</Label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full h-16 rounded-md justify-between"
          >
            <div className="flex items-center gap-2">
              {selectedTemplate ? (
                <>
                  <img
                    src={selectedTemplate.image}
                    alt={selectedTemplate.label}
                    className="w-12 h-12 object-contain rounded-sm"
                  />
                  <span>{selectedTemplate.label}</span>
                </>
              ) : (
                <span>Select template...</span>
              )}
            </div>
            <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0">
          <ScrollArea className="h-[300px]">
            <div className="grid grid-cols-2 gap-2 p-2">
              {SLIDE_TEMPLATES.map((template) => (
                <Card
                  key={template.value}
                  className={cn(
                    'cursor-pointer transition-all hover:bg-accent',
                    'p-2 flex flex-col items-center gap-2',
                    value === template.value && 'bg-accent'
                  )}
                  onClick={() => {
                    onChange(template.value);
                    setOpen(false);
                  }}
                >
                  <div className="relative w-full">
                    <img src={template.image} alt={template.label} className="w-full h-20 object-contain rounded-md" />
                    {value === template.value && (
                      <div className="absolute top-1 right-1 bg-primary rounded-full p-0.5">
                        <Check className="h-4 w-4 text-primary-foreground" />
                      </div>
                    )}
                  </div>
                  <span className="text-sm font-medium truncate w-full text-center">{template.label}</span>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>
    </div>
  );
};
