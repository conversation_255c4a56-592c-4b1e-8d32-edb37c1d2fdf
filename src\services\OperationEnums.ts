import { defineEnum } from '../utils/defineEnum';

export const OperationEnums = Object.freeze({
  AudioProfile: defineEnum([
    {
      value: 'Man',
      label: 'Man',
    },
    {
      value: 'Woman',
      label: 'Woman',
    },
    {
      value: 'Child',
      label: 'Child',
    },
    {
      value: 'gpt-4o',
      label: 'gpt-4o',
    },
  ]),
  WebSources: defineEnum([
    {
      value: 'Web',
      label: 'Web',
    },
    {
      value: 'URL',
      label: 'URL',
    },
    {
      value: 'File',
      label: 'File',
    },
    {
      value: 'Text',
      label: 'Text',
    },
    // {
    //   value: 'youtube_url',
    //   label: 'Youtube URL',
    // },
  ]),
  Activity: defineEnum([
    {
      value: 't_f',
      label: 'True or false',
    },
    {
      value: 'mcq',
      label: 'Multiple choice',
    },
    {
      value: 'fill_in_blank',
      label: 'Fill in the blank',
    },
    {
      value: 'case_study',
      label: 'Case study',
    },
  ]),
});
