import Lottie from 'lottie-react';
import loadingLottie from '../../public/loading-2.json';
import { t } from 'i18next';
interface PageLoaderProps {
  fullScreen?: boolean;
  className?: string;
}

export const PageLoader = ({ fullScreen = false, className = '' }: PageLoaderProps) => {
  return (
    <div
      className={`flex justify-center items-center ${fullScreen ? 'h-[100dvh]' : 'min-h-[500px] h-full'} ${className}`}
    >
      <div className="text-center">
        <Lottie animationData={loadingLottie} loop={true} autoplay={true} className="w-80" />
        <p className="text-2xl">{t('loading...')}</p>
      </div>
    </div>
  );
};
