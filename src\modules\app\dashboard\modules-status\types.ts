export interface IPhase {
  id: number;
  name_en: string;
  name_ar: string;
  action: string;
  status: IStatus[];
}
export interface IStatus {
  id: number;
  phase_with_status: string;
  name_en: string;
  name_ar: string;
  description: string;
  action: string;
  module_status_id: number;
  phase: IPhase;
  background_color: string;
  text_color: string;
}
export interface ISystemModuleStatus {
  id: number;
  title: string;
  phases: IPhase[];
}

export interface IPhasePayload {
  name_ar: string;
  name_en: string;
  phase_id: number;
}

export interface IModuleStatusPayload {
  module_id: number;
  status_id: number;
  name_ar: string;
  name_en: string;
  description: string;
}
