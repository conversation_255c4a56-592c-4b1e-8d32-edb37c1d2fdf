import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  deleteNotification,
  getInitialNotifications,
  getUnreadNotificationCount,
  markAllNotificationsAsRead,
  changeNotificationReadStatus,
} from './endpoints';

export const useGetInitialNotifications = () => {
  return useQuery({
    queryKey: ['initialNotifications'],
    queryFn: getInitialNotifications,
  });
};

export const useGetUnreadNotificationsCount = () => {
  return useQuery({
    queryKey: ['unreadNotificationCount'],
    queryFn: getUnreadNotificationCount,
  });
};

export const useChangeNotificationReadStatus = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: changeNotificationReadStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['initialNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotificationCount'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};

export const useMarkAllNotificationAsRead = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['initialNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotificationCount'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};

export const useDeleteNotification = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteNotification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['initialNotifications'] });
      queryClient.invalidateQueries({ queryKey: ['unreadNotificationCount'] });
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
    },
  });
};
