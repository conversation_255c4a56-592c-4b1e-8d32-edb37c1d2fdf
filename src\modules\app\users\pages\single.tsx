import { cn } from '@/lib/utils';
import { useState } from 'react';
import GeneralInformation from '../../profile/components/general-information';
import UserInfo from '../components/user-information';
import { t } from 'i18next';
import { useGetUserById } from '@/modules/app/users/apis/queries';
import { useParams } from 'react-router-dom';

const Page = () => {
  const [activeTab, setActiveTab] = useState('UserInfo');
  const { id } = useParams();
  const { data } = useGetUserById(id);
  const handleSelect = (tab: string) => {
    setActiveTab(tab);
  };

  const tabFinder = (tab: string) => {
    switch (tab) {
      case 'UserInfo':
        return <UserInfo data={data} />;
      case 'Information':
        return <GeneralInformation />;
    }
  };
  const tabs = [
    {
      title: 'UserInfo',
    },
    {
      title: 'Information',
    },
  ];
  return (
    <div>
      <div className="bg-background p-6 px-0 min-h-96 mt-5 shadow-sm rounded-lg border-border border flex">
        <div className="border-e border-border min-w-36 me-5 flex flex-col gap-1 ">
          {tabs.map((tab) => (
            <div
              onClick={() => handleSelect(tab.title)}
              className={cn(
                'cursor-pointer p-2.5  px-5 text-sm font-medium relative after:opacity-0 transition-all duration-300  after:content-[""] after:absolute after:bottom-0 after:end-0 after:h-full  after:w-0.5 after:bg-primary',
                activeTab === tab.title ? 'bg-primary/5  text-primary after:opacity-100' : 'text-[#6B7280]'
              )}
            >
              {tab.title}
            </div>
          ))}
        </div>
        {/* //the Component */}
        <div className="w-full">{tabFinder(activeTab)}</div>
      </div>
    </div>
  );
};

export default Page;
