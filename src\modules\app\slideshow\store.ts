import exp from 'constants';
import { atom, useSetAtom } from 'jotai';

export const currentTimeAtom = atom(0);
export const currentTrackIndexAtom = atom(0);
export const totalDurationAtom = atom(0);
export const previousVolumeAtom = atom(1);
export const isFullscreenAtom = atom(false);
export const captionsAtom = atom(false);
export const isControlsVisibleAtom = atom(false);

export const atomsCleaner = atom(
  null, // it's a convention to pass `null` for the first argument
  (get, set, update) => {
    // `update` is any single value we receive for updating this atom
    set(currentTimeAtom, 0);
    set(currentTrackIndexAtom, 0);
    set(totalDurationAtom, 0);
    set(previousVolumeAtom, 1);
    set(isFullscreenAtom, false);
    set(captionsAtom, false);
    set(isControlsVisibleAtom, false);
  }
);

// export const atomsCleander = () => {
//   currentTimeAtom;
//   currentTrackIndexAtom.reset();
//   totalDurationAtom.reset();
//   previousVolumeAtom.reset();
//   isFullscreenAtom.reset();
//   captionsAtom.reset();
//   isControlsVisibleAtom.reset();
// };
