import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  toggleUserStatus,
  updatedUserPassword,
  createUser,
  getUserById,
  toggleUsersStatus,
  updateProfile,
  editUser,
} from './endpoints';
import { useNotify } from '@/index';
import { IPasswordPayload, ICreateUser } from '../types';
import { useTranslation } from 'react-i18next';

// Hook to delete a role
export const useToggleUserStatus = () => {
  const { notify } = useNotify();

  return useMutation<void, Error, number>({
    mutationFn: toggleUserStatus,
    onSuccess: () => {
      notify.success('User status changed successfully');
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

export const useToggleUsersStatus = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();

  return useMutation<void, Error, { ids: number[]; status: 'active' | 'blocked' }>({
    mutationFn: toggleUsersStatus,
    onSuccess: () => {
      notify.success(t('notify.userStatus'));
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

export const useUpdatePassword = () => {
  const { notify } = useNotify();

  return useMutation<void, Error, { id: number; payload: IPasswordPayload }>({
    mutationFn: ({ id, payload }) => updatedUserPassword(id, payload),
    onSuccess: () => {
      notify.success('Password Updated successfully');
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

export const useEditUser = () => {
  const { t } = useTranslation();

  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editUser,
    onSuccess: () => {
      notify.success(t('Profile Updated successfully'));
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user'] });
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

// Create user
export const useCreateUser = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();

  return useMutation<void, Error, { payload: ICreateUser }>({
    mutationFn: ({ payload }) => createUser(payload),
    onSuccess: () => {
      notify.success('User status changed successfully');
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};

// Hook to get user by ID
export const useGetUserById = (id: string | undefined) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => (id ? getUserById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null
  });
};

export const useUpdateProfile = () => {
  const { t } = useTranslation();

  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateProfile,
    onSuccess: () => {
      notify.success(t('Profile Updated successfully'));
      queryClient.invalidateQueries({ queryKey: ['user'] });
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};
