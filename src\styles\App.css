@import url('https://fonts.googleapis.com/css2?family=Source+Sans+3:ital,wght@0,200..900;1,200..900&display=swap');
@import './arabic-font.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'DIN Next LT Arabic R', sans-serif;
}

body .font-bold,
body .font-semibold,
body .font-medium,
body .font-extrabold {
  font-family: 'DIN Next LT Arabic medium', sans-serif;
}

/* making popover shadcn take full width */
.PopoverContent {
  width: var(--radix-popover-trigger-width);
  max-height: var(--radix-popover-content-available-height);
}
/* select styles */
.custom-select {
  border: 1px solid rgb(209 213 219 / 1);
  background-color: rgb(249 250 251 / 1);
  border-radius: 8px;
  display: inline-block;
  font: inherit;
  line-height: 1.5em;
  padding: 0.5em 3.5em 0.5em 1em;
  background-image: linear-gradient(45deg, transparent 50%, gray 50%),
    linear-gradient(135deg, gray 50%, transparent 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 20px) calc(1em + 2px), calc(100% - 15px) calc(1em + 2px), calc(100% - 2.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1px 1.5em;
  background-repeat: no-repeat;

  /* reset */
  margin: 0;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.custom-select:focus {
  background-image: linear-gradient(45deg, green 50%, transparent 50%),
    linear-gradient(135deg, transparent 50%, green 50%), linear-gradient(to right, #ccc, #ccc);
  background-position: calc(100% - 15px) 1em, calc(100% - 20px) 1em, calc(100% - 2.5em) 0.5em;
  background-size: 5px 5px, 5px 5px, 1px 1.5em;
  background-repeat: no-repeat;
  border-color: green;
  outline: 0;
}

.custom-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #000;
}

.ck .ck-editor__main .ck-blurred,
.ck-content {
  min-height: 400px;
}

.ck .ck-editor__main .ck-blurred,
.ck-content,
.ck.ck-editor__main .ck-editor__editable {
  padding-inline: 30px;
}

button[role='switch'] div::after {
  background: white;
  border-radius: 50%;
  transition-duration: 0.1s;
  right: 21px;
}

div[role='dialog'] {
  height: auto !important;
}

/* Custom styles to disable hover animation on navigation arrows */
.reveal .controls .navigate-left,
.reveal .controls .navigate-right,
.reveal .controls .navigate-up,
.reveal .controls .navigate-down {
  @apply transition-none; /* Disable transitions */
}

/* Override any hover styles */
.reveal .controls .navigate-left:hover,
.reveal .controls .navigate-right:hover,
.reveal .controls .navigate-up:hover,
.reveal .controls .navigate-down:hover {
  @apply transform-none; /* Disable transform animations */
}

/* Container */
.rhap_container {
  @apply bg-background border border-border shadow-sm rounded-lg;
}
.rhap_progress-indicator {
  @apply bg-primary;
}
/* Play/Pause Button */
.rhap_play-pause-button {
  color: #007bff; /* Change play/pause button color */
}
.rhap_download-progress {
  @apply bg-secondary;
}
/* Progress Bar */
.rhap_progress-bar {
  background-color: #e0e0e0; /* Change background of the progress bar */
}

.rhap_progress-filled {
  background-color: #007bff; /* Change filled color of the progress bar */
}

/* Controls */
.rhap_controls {
  color: #333; /* Change color of all control icons */
}

.rhap_volume-indicator {
  background-color: #007bff; /* Change volume slider color */
}

html {
  scrollbar-width: 10px;
  scrollbar-track-color: hsl(var(--primary));
  scrollbar-color: #e5e7eb hsl(var(--muted));
}

/* removing calendar base header to add custome  year and month */
.rdp-vhidden {
  @apply hidden;
}
.ck-editor__editable {
  @apply bg-white dark:bg-black !important;
  @apply text-gray-900 dark:text-gray-100 !important;
}

.ck.ck-toolbar {
  @apply bg-background;
}

.ck-sticky-panel__content {
  @apply border border-muted dark:border-black rounded-t-md !important;
}

.ck.ck-editor__main > .ck-editor__editable:not(.ck-focused) {
  @apply border-none rounded-md   !important;
}

.ck-toolbar__items {
  @apply gap-2 rtl:flex-row-reverse px-5 py-0.5 !important;
}

.rhap_repeat-button {
  display: none !important;
}

.rhap_controls-section {
  @apply flex flex-row-reverse justify-between gap-2 items-center text-gray-500 !important;
}

.rhap_volume-controls {
  @apply hidden !important;
}

.rhap_additional-controls {
  @apply flex  gap-2 items-center justify-end !important;
}

.rhap_container {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.rhap_main {
  @apply space-y-7 !important;
}

/* view only mode styles for ckeditor */

.read-only-ckeditor * {
  background: transparent !important;
  background-color: transparent;
}

.read-only-ckeditor .ck-sticky-panel__content,
.disabled-ckeditor .ck-sticky-panel__content {
  display: none;
}
