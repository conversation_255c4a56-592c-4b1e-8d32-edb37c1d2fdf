import React from 'react';
import { useHasPermission } from '@/modules/auth/store';

interface ProtectedComponentProps {
  children: React.ReactNode;
  requiredPermissions: string | string[];
  fallbackComponent?: React.ReactNode;
  requireAll?: boolean;
}

const ProtectedComponent: React.FC<ProtectedComponentProps> = ({
  children,
  requiredPermissions,
  fallbackComponent = null,
  requireAll = true,
}) => {
  if (requiredPermissions === 'public') {
    return <>{children}</>;
  }
  const hasPermission = useHasPermission(requiredPermissions, requireAll);

  if (!hasPermission) {
    return <>{fallbackComponent}</>; // Render fallback if the user lacks permissions
  }

  return <>{children}</>; // Render the protected component if the user has permissions
};

export { ProtectedComponent };
