import { useEffect } from 'react';
import { Modal, useForm, TextInput, Form } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useUpdateSlideshowData } from '../../apis/queries';
import { ISingleSlideShow } from '@/modules/app/dna/apis/slideshow/types';
import { useParams } from 'react-router-dom';

const SlideShowEditDialog = ({
  onOpen,
  onClose,
  data,
}: {
  onOpen: boolean;
  onClose: () => void;
  data: ISingleSlideShow;
}) => {
  const { t } = useTranslation();
  const { taskId } = useParams();
  const { mutate, isPending } = useUpdateSlideshowData();

  const { form, setFieldValue } = useForm({
    taskCode: taskId,
    slideshow_id: data.id,
    dnaId: data.dna_id,
    title: data.title,
  });

  useEffect(() => {
    if (data) {
      setFieldValue('title')(data.title);
      setFieldValue('slideshow_id')(data.id);
      setFieldValue('dnaId')(data.dna_id);
      setFieldValue('taskCode')(taskId);
    }
  }, []);

  const handleSubmit = () => {
    mutate(form, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Modal open={onOpen} onOpenChange={onClose} modalHeader={t('slideshowEditModal.title')}>
      <Form className="space-y-5" onSubmit={handleSubmit}>
        <TextInput
          label={t('slideshowEditModal.title')}
          placeholder={t('slideshowEditModal.title')}
          value={form?.title}
          onChange={setFieldValue('title')}
        />
        <Button loading={isPending} className="min-w-[100px]">
          {t('submit')}
        </Button>
      </Form>
    </Modal>
  );
};

export default SlideShowEditDialog;
