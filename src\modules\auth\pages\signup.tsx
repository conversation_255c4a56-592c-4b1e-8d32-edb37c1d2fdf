import { useState } from 'react';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Form, TextInput, useForm, useValidate, Icon, Regex, useNotify, Modal } from '@/index';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';
import { useSignUp } from '@/modules/auth/apis/queries';

export const SignUpPage = () => {
  // State
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState(false);

  // Hooks
  const navigate = useLanguageNavigate();
  const { isRequired, minLength, maxLength, validatePasswordRegex, isEmail } = useValidate();
  const { notify } = useNotify();
  const { t, i18n } = useTranslation();
  const { mutate, isPending } = useSignUp();

  const { form, setFieldValue } = useForm({
    email: '',
    name: '',
    password: '',
    password_confirmation: '',
  });

  // Funcations
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    if (form.password !== form.password_confirmation) {
      notify.error('Password must be matched');
    } else {
      mutate(form, { onSuccess: () => setConfirmDialog(true) });
    }
  };

  const isRTL = i18n.dir() === 'rtl';

  return (
    <div className="flex items-center justify-center min-h-screen dark:bg-background px-4">
      <div className="bg-card max-w-md w-full shadow-md overflow-hidden rounded-xl border border-border">
        <div className="px-6 py-8">
          <h2 className="text-2xl font-bold mb-4">{t('signUp.title')}</h2>
          <Form className="flex flex-col gap-5" onSubmit={handleSubmit}>
            <div>
              <TextInput
                name="name"
                label={t('name')}
                placeholder={t('name')}
                disabled={isPending}
                value={form.name}
                onChange={setFieldValue('name')}
                validators={[isRequired()]}
              />
            </div>

            <div>
              <TextInput
                name="email"
                label={t('email')}
                placeholder={t('email')}
                disabled={isPending}
                value={form.email}
                onChange={setFieldValue('email')}
                validators={[isRequired(), isEmail()]}
              />
            </div>

            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  name={'password'}
                  label={t('password')}
                  placeholder={t('password')}
                  type={showPassword ? 'text' : 'password'}
                  disabled={isPending}
                  value={form.password}
                  onChange={setFieldValue('password')}
                  validators={[isRequired(), validatePasswordRegex(Regex.password, 8), minLength(8), maxLength(50)]}
                />
              </div>

              <div className="mt-8" onClick={() => togglePasswordVisibility()}>
                <Icon
                  className={`${
                    isRTL ? 'mr-3 ' : 'ml-3'
                  } p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400`}
                  width="25"
                  icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>

            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  name={'password'}
                  label={t('confirmPassword')}
                  placeholder={t('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  disabled={isPending}
                  value={form.confirmPassword}
                  onChange={setFieldValue('password_confirmation')}
                  validators={[isRequired(), validatePasswordRegex(Regex.password, 8)]}
                />
                {form.password !== form.password_confirmation && form.password_confirmation ? (
                  <label className="text-red-500 text-sm">{t("Confirm password doesn't match the password")}</label>
                ) : null}
              </div>

              <div className="mt-8" onClick={() => toggleConfirmPasswordVisibility()}>
                <Icon
                  className={`${
                    isRTL ? 'mr-3 ' : 'ml-3'
                  } p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400`}
                  width="25"
                  icon={!showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>

            <Button type="submit" disabled={isPending} loading={isPending} className="mt-4 flex gap-2 items-center">
              <Icon className={`mt-1 ${i18n.dir() === 'rtl' ? 'rotate-180' : ''}`} icon="mdi:send" width={20} />
              <p>{t('signUp.cardSignUp')}</p>
            </Button>
            <div>
              <p className=" text-center grid md:block">
                {t('signUp.cardSiginIn')}{' '}
                <span
                  onClick={() => navigate('/auth/login')}
                  className="cursor-pointer text-primary hover:underline font-medium"
                >
                  {t('signUp.cardLogin')}
                </span>
              </p>
            </div>
          </Form>
        </div>
      </div>
      <ToastContainer />
      {confirmDialog && (
        <Modal open={confirmDialog} onOpenChange={setConfirmDialog}>
          <p>{t('check your email and await approval from the administrator')}</p>
          <div className="flex justify-end mt-5">
            <Button
              onClick={() => {
                setConfirmDialog(false), navigate('/');
              }}
            >
              {t('ok')}
            </Button>
          </div>
        </Modal>
      )}
    </div>
  );
};
