import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { t } from 'i18next';
import { <PERSON><PERSON>, CopyCheck } from 'lucide-react';
import { useNotify } from '@/hooks';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useEffect, useState } from 'react';
const PromptViewerDialog = ({ prompt, model }: { prompt: string; model: string }) => {
  const [isPromptCopied, setIsPromptCopied] = useState<boolean>(false);
  useEffect(() => {
    if (isPromptCopied) {
      setTimeout(() => {
        setIsPromptCopied(false);
      }, 15000);
    }
  }, [isPromptCopied]);
  const { notify } = useNotify();

  const handleCopyPrompt = () => {
    navigator.clipboard.writeText(prompt);
    setIsPromptCopied(true);
    notify.success(t('promptCopied'));
  };
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" className="mt-1">
          {t('prompt')}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[800px]">
        <DialogHeader>
          <DialogTitle>{t('DNAPage.prompt')} </DialogTitle>
          <DialogDescription>{t('DNAPage.promptDescription')}</DialogDescription>
        </DialogHeader>

        <div className="">
          <div className="font-medium my-2 text-lg flex justify-between items-center">
            <div> {`${t('model')}: ${model}`}</div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger className="me-5">
                  {isPromptCopied ? <CopyCheck /> : <Copy onClick={handleCopyPrompt} />}
                </TooltipTrigger>
                <TooltipContent>
                  <p>{t('copy')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <div className="max-h-[550px] overflow-y-auto [&_.ck-content]:!p-1">
            <pre className="whitespace-pre-wrap text-sm overflow-x-auto">
              <code>{prompt}</code>
            </pre>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PromptViewerDialog;
