import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useEditSkill } from '../../../apis/queries';

const ProgramEditDialog = ({
  isOpen,
  setIsOpen,
  rowBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  rowBeingEdited: {
    program_skill_id: number;
    program_id: number;
    name: string;
    description: string;
    skill_type: string;
  };
}) => {
  const { t } = useTranslation();
  const { form, setFieldValue } = useForm({
    program_skill_id: rowBeingEdited?.program_skill_id || 0,
    name: rowBeingEdited?.name || '',
    description: rowBeingEdited.description || '',
    skill_type: rowBeingEdited?.skill_type || '',
  });
  const { mutate: editSkill, isPending: isEditing } = useEditSkill();

  const handleSubmit = () => {
    editSkill(
      { id: rowBeingEdited.program_id, form },
      {
        onSuccess: () => {
          setIsOpen(false);
        },
      }
    );
  };

  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('contentNeed.steps.skills.table.job')}>
      <Form onSubmit={handleSubmit} className="space-y-3">
        <TextInput
          name="name"
          label={t('contentNeed.steps.skills.table.job')}
          value={form.name}
          onChange={setFieldValue('name')}
        />
        <TextInput
          name="description"
          label={t('assignedTask.table.description')}
          value={form.description}
          onChange={setFieldValue('description')}
        />
        <TextInput
          name="skill_type"
          label={t('contentNeed.steps.skills.table.type')}
          value={form.skill_type}
          onChange={setFieldValue('skill_type')}
        />
        <div className="flex justify-end">
          <Button loading={isEditing} disabled={isEditing} type="submit" className="mt-4 ml-auto w-fit">
            {t('save')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ProgramEditDialog;
