// /src/modules/auth/routes.js
import { lazy } from 'react';
import { MainLayout } from './layout';

// Dynamic imports for auth pages
const ForgetPassword = lazy(() => import('./pages/forget-password').then((m) => ({ default: m.ForgetPassword })));
const LoginPage = lazy(() => import('./pages/login').then((m) => ({ default: m.LoginPage })));
const ResetPassword = lazy(() => import('./pages/reset-password').then((m) => ({ default: m.ResetPassword })));
const SignUpPage = lazy(() => import('./pages/signup').then((m) => ({ default: m.SignUpPage })));
const VerifiedEmail = lazy(() => import('./pages/verified-email').then((m) => ({ default: m.VerifiedEmail })));

export default [
  {
    path: 'auth',
    element: <MainLayout />,
    children: [
      {
        path: 'login',
        element: <LoginPage />,
      },
      {
        path: 'signup',
        element: <SignUpPage />,
      },
      {
        path: 'verify-email',
        element: <VerifiedEmail />,
      },
      {
        path: 'forget-password',
        element: <ForgetPassword />,
      },
      {
        path: 'reset-password',
        element: <ResetPassword />,
      },
    ],
  },
];
