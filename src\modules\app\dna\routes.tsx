import { lazy } from 'react';

const DnaList = lazy(() => import('./pages/list'));
const SingleDna = lazy(() => import('./pages/single'));

export default [
  {
    path: 'DNAS',
    element: <DnaList />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.DNAs',
        title: 'breadcrumb.myContentPage.DNAs',
      };
    },
  },
  {
    path: 'DNAs/:dnaId',
    element: <SingleDna />,
  },
];
