import { useMemo } from 'react';

import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';
import { useAtom } from 'jotai';
import { contentNeedsAtom } from '../../tools/store/contnet-need';
import { Button } from '@/components/ui/button';
const OutcomeStep = ({ onComplete }: { onComplete: (data: any) => void }) => {
  //  State
  const [data] = useAtom(contentNeedsAtom);
  //  Hooks
  const { t } = useTranslation();

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'learning_outcome',
        header: t('contentNeed.steps.outcome.title'),
        width: '600px',
        cell: ({ row }) => <div className="break-words font-medium">{row.learning_outcome}</div>,
      },
      {
        accessorKey: 'title',
        header: t('contentNeed.steps.skills.table.job'),
      },
      {
        accessorKey: 'skillCount',
        header: t('contentNeed.steps.skills.table.skill'),
        cell: ({ row }) => (
          <div className="rtl:text-right flex flex-wrap gap-3">
            {row.skills.map((skill: any, index: number) => (
              <Badge variant={'outline'} className="font-medium">
                {skill.title}
              </Badge>
            ))}
          </div>
        ),
      },
    ];
  }, [t]);

  return (
    <>
      <div className="mt-[-1.75rem] flex items-center justify-center">
        <div className="max-w-[1500px] w-full bg-background p-6">
          <p className="text-lg font-medium my-3">{t('contentNeed.steps.outcome.title')}</p>
          <Table rows={data.jobs || []} columns={columns}>
            <TableContent>
              <TableContentBody />
            </TableContent>
          </Table>
        </div>
      </div>
      <div className="flex justify-center items-center my-5">
        <Button onClick={() => onComplete('fdasfas')} className="text-white mt-3">
          {t('table.pagination.next')}
        </Button>
      </div>
    </>
  );
};
export default OutcomeStep;
