import { useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';
import { Form, TextInput, useForm, useValidate, Icon, Regex, useNotify } from '@/index';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';
import { useSearchParams } from 'react-router-dom';
import { useResetPassword } from '../apis/queries';

export const ResetPassword = () => {
  // State
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { mutate: resetPassword, isPending } = useResetPassword();
  // Hooks
  const navigate = useLanguageNavigate();
  const { isRequired, minLength, maxLength, validatePasswordRegex } = useValidate();
  const { notify } = useNotify();
  const { t, i18n } = useTranslation();
  const [searchParams] = useSearchParams();

  const { form, setFieldValue } = useForm({
    password: '',
    password_confirmation: '',
    token: searchParams.get('token'),
    email: searchParams.get('email'),
  });

  // Funcations
  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleSubmit = async (event: any) => {
    event.preventDefault();
    if (form.password !== form.password_confirmation) {
      notify.error('Password must be matched');
    } else {
      resetPassword(form);
    }
  };

  const isRTL = i18n.dir() === 'rtl';

  return (
    <div className="flex items-center justify-center min-h-screen dark:bg-background px-4">
      <div className="bg-card max-w-md w-full shadow-md overflow-hidden rounded-xl border border-border">
        <div className="px-6 py-8">
          <h2 className="text-2xl font-bold mb-4">{t('resetPassword.title')}</h2>
          <Form className="flex flex-col gap-5" onSubmit={handleSubmit}>
            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  name={'password'}
                  label={t('password')}
                  placeholder={t('password')}
                  type={showPassword ? 'text' : 'password'}
                  value={form.password}
                  onChange={setFieldValue('password')}
                  validators={[isRequired(), validatePasswordRegex(Regex.password, 8), minLength(8), maxLength(50)]}
                />
              </div>

              <div className="mt-8" onClick={() => togglePasswordVisibility()}>
                <Icon
                  className={`${
                    isRTL ? 'mr-3 ' : 'ml-3'
                  } p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400`}
                  width="25"
                  icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>

            <div className="flex w-full">
              <div className="w-full">
                <TextInput
                  name={'password'}
                  label={t('confirmPassword')}
                  placeholder={t('confirmPassword')}
                  type={showConfirmPassword ? 'text' : 'password'}
                  value={form.password_confirmation}
                  onChange={setFieldValue('password_confirmation')}
                  validators={[isRequired(), validatePasswordRegex(Regex.password, 8)]}
                />
                {form.password !== form.password_confirmation && form.password_confirmation ? (
                  <label className="text-red-500 text-sm">{t("Confirm password doesn't match the password")}</label>
                ) : null}
              </div>

              <div className="mt-8" onClick={() => toggleConfirmPasswordVisibility()}>
                <Icon
                  className={`${
                    isRTL ? 'mr-3 ' : 'ml-3'
                  } p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400`}
                  width="25"
                  icon={!showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                />
              </div>
            </div>

            <Button loading={isPending} type="submit" className="mt-4 flex gap-2 items-center">
              <Icon className={`mt-1 ${i18n.dir() === 'rtl' ? 'rotate-180' : ''}`} icon="mdi:send" width={20} />
              <p>{t('resetPassword.cardButton')}</p>
            </Button>
          </Form>
        </div>
      </div>
    </div>
  );
};
