import { useState } from 'react';
import { IDNA } from '../dna/types';
import { cn } from '@/lib/utils';
import { localizePresenter } from '@/utils/helpers';
import { useTranslation } from 'react-i18next';

interface ITranslationContentTabs {
  Dna: IDNA;
  setSelectedContent: any;
  setSelectedContentLanguage: any;
}

export interface ITranslationContentTab {
  title: string;
  dna: string;
  lang: { id: string; name_ar: string; name_en: string };
}
const TranslationContentTabs = ({ Dna, setSelectedContent, setSelectedContentLanguage }: ITranslationContentTabs) => {
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const { t, i18n } = useTranslation();
  const handleLanguageSelect = (content: ITranslationContentTab) => {
    setSelectedContent(content.dna);
    setSelectedContentLanguage(content.lang);
  };
  const translationsTabs: ITranslationContentTab[] = [
    {
      title: Dna?.language,
      dna: Dna?.dna_content,
      lang: Dna?.language,
    },
    ...(Dna?.content_translations
      ? Dna?.content_translations.map((translation: any) => ({
          title: translation.language,
          dna: translation.content,
          lang: translation?.language,
        }))
      : []),
  ];
  return Dna?.content_translations?.length ? (
    <div className="px-0 mt-2 py-1 shadow-sm rounded-lg flex" dir={i18n.language === 'ar' ? 'rtl' : 'ltr'}>
      {translationsTabs?.map((translation, index) => (
        <div
          key={index}
          onClick={() => {
            handleLanguageSelect(translation), setActiveTabIndex(index);
          }}
          className={cn(
            'cursor-pointer p-2.5 truncate px-5 text-sm font-medium relative after:content-[""] after:absolute after:bottom-0 after:start-0 after:h-0.5 after:w-full after:bg-primary after:opacity-0 transition-all duration-300',
            activeTabIndex === index ? ' text-primary after:opacity-100' : 'text-[#6B7280] hover:text-primary'
          )}
        >
          {localizePresenter(translation?.lang, i18n.language)}
        </div>
      ))}
    </div>
  ) : null;
};

export default TranslationContentTabs;
