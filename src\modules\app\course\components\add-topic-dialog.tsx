import { useForm, TextInput, useValidate, Form } from '@/index';
import { Button } from '@/components/ui/button';
import { useAddTopic, useGetCourseById } from '@/modules/app/course/apis/queries';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useState } from 'react';
interface IProps {
  courseId: string;
}
const AddTopicDialog = ({ courseId }: IProps) => {
  const [open, setOpen] = useState(false);
  // hooks
  const { isRequired } = useValidate();
  const { mutate: addTopic, isPending, data } = useAddTopic();
  const { refetch: refetchCourse } = useGetCourseById(data?.id || courseId);
  const { form, setFieldValue } = useForm({
    new_topic_info: '',
  });

  const { t } = useTranslation();
  const handleSubmit = (e: Event) => {
    e.preventDefault();
    addTopic(
      { id: data?.id || courseId, payload: form },
      {
        onSuccess: () => {
          refetchCourse();
          setOpen(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger>
        <Button variant={'outline'} className="px-4" type="button">
          + {t('CoursePlanCreationPage.addTopicInsideCourse')}
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('CoursePlanCreationPage.addTopicInsideCourse')}</DialogTitle>
          <DialogDescription>{t('CoursePlanCreationPage.addTopicInsideCourseDescription')}</DialogDescription>
        </DialogHeader>

        <Form onSubmit={handleSubmit} className={`text-primary pt-5 font-semibold flex flex-col`}>
          <div>
            <TextInput
              name="new_topic_info"
              placeholder={t('CoursePlanCreationPage.addTopic')}
              value={form.new_topic_info}
              onChange={setFieldValue('new_topic_info')}
              // isRequired
              // validators={[isRequired()]}
            />
          </div>
          <Button type="submit" className="mt-4 ms-auto" loading={isPending} disabled={isPending}>
            {t('CoursePlanCreationPage.submit')}
          </Button>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export { AddTopicDialog };
