import { ScrollArea } from '@/components/ui/scroll-area';
import { memo } from 'react';

const SkeletonGrid = () => {
  return (
    <ScrollArea className="h-[500px] pe-3 relative overflow-hidden">
      {/* Animated gradient overlay */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div className="animate-shine h-full w-full bg-[linear-gradient(110deg,transparent_40%,rgba(255,255,255,0.25)_50%,transparent_60%)] bg-[length:200%_100%]" />
      </div>

      <div className="grid grid-cols-[repeat(auto-fill,minmax(180px,1fr))] gap-3">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="group relative space-y-2 overflow-hidden">
            {/* Image skeleton */}
            <div className="aspect-square w-full rounded-md bg-gray-200/80 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-gray-300/30 to-gray-100/30" />
            </div>

            {/* Text skeleton */}
            <div className="space-y-2">
              <div className="h-4 rounded bg-gray-200/50" />
              <div className="h-3 w-3/4 rounded bg-gray-200/50" />
            </div>

            {/* Checkbox skeleton */}
            <div className="absolute right-3 top-3 h-5 w-5 rounded-full bg-gray-300/50 backdrop-blur-sm" />
          </div>
        ))}
      </div>
    </ScrollArea>
  );
};

export default memo(SkeletonGrid);
