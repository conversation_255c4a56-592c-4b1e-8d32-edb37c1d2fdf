import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from './ui/separator';
import { cn } from '@/lib/utils';
// interface to extend all the div props
interface IProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title?: string;
  width?: string | number;
  description?: string;
  modalHeader?: string;
  hasPadding?: boolean;
  isModal?: boolean;
}
export const Modal: React.FC<IProps> = ({
  children,
  open,
  onOpenChange,
  title,
  width = '550px',
  description,
  modalHeader,
  hasPadding = true,
  isModal = true,
  ...props
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange} {...props} modal={isModal}>
      {!isModal && <div className="fixed top-[-17px] left-0 w-screen h-screen bg-black/50 z-40" />}
      <DialogContent
        style={{ maxWidth: width }}
        className={cn(
          'max-h-screen overflow-y-auto p-0 gap-0',
          modalHeader && '!mt-5',
          '[&_svg]:h-5 [&_svg]:w-5',
          '[&_>button]:top-6'
        )}
        onInteractOutside={(event) => event.preventDefault()}
      >
        <DialogHeader className="pt-1 mt-5 px-7 border-b border-border w-full">
          <DialogTitle className="font-medium pb-4">{modalHeader}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        {modalHeader && <Separator />}
        <div className={cn(hasPadding && 'px-5 pb-5 pt-5')}>{children}</div>
      </DialogContent>
    </Dialog>
  );
};
