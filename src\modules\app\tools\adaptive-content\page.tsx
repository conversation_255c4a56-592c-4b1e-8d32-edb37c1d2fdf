import { GET_DNA_LIST, useFetchList } from '@/index';
import { useState, useMemo } from 'react';
import { IDNA } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import RewriteContentDialog from './components/rewrite-content-dialog';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { useHasPermission } from '@/modules/auth/store';
import { Table, TableContent, TableContentBody } from '@/components/theTable';
import { Button } from '@/components/ui/button';
function RewriteContent() {
  //  State
  const [editDialog, setEditDialog] = useState<any>(false);
  const [danData, setDanData] = useState<any>(null);
  //  Hooks
  const navigate = useLanguageNavigate();
  const { t } = useTranslation();

  const { loading, list, refresh } = useFetchList(`${GET_DNA_LIST}?status_action=ready_for_review`, 'approved-dnas', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'dnaConentPgae.userFIlter',
            },
          }
        : {}),
    },
  });

  const columns = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('dnaConentPgae.table.dnaTitle'),
        cell: ({ row }: { row: IDNA }) => (
          <div className="break-words rtl:text-right">
            <Button
              variant="link"
              className="text-primary underline cursor-pointer p-0 h-auto"
              onClick={() => navigate(`/app/tools/rewrite-content/${row.id}`)}
            >
              {row.title}
            </Button>
          </div>
        ),
      },
      {
        accessorKey: 'user',
        header: t('dnaConentPgae.table.author'),
        cell: ({ row }: { row: IDNA }) => <div className="max-w-72 truncate">{row.user.email}</div>,
      },
    ];
  }, [t, navigate]);

  return (
    <>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentBody />
        </TableContent>
      </Table>
      {editDialog && (
        <RewriteContentDialog
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false), setDanData(null);
          }}
          details={danData}
          onFinish={refresh}
        />
      )}
    </>
  );
}

export default RewriteContent;
