import {
  Modal,
  useForm,
  Form,
  useValidate,
  CalendarInput,
  ComboboxInput,
  MultiSelect,
  DnaEnums,
  Textarea,
} from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useGetOperations } from '@/modules/app/dashboard/operation/apis/queries';
import moment from 'moment';
import { useGetUsers, useReAssigneTask } from '@/modules/app/tasks/apis/queries';
import { IOperation } from '@/modules/app/dashboard/operation/types';
import { ITask } from '../../tasks/types';

const UpdateTaskDialog = ({
  open,
  onOpenChange,
  task,
}: {
  open: boolean;
  onOpenChange: () => void;
  task: ITask | any;
}) => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { data: operations } = useGetOperations();
  const { t } = useTranslation();
  const { mutate: reAssign, isPending } = useReAssigneTask();
  const { data: users } = useGetUsers();
  const operationsOptions = operations?.map((operation) => ({ label: operation.name, value: operation.id })) || [];

  const { form, setFieldValue } = useForm({
    assigned_to: task?.assigned_to?.id,
    content_type: 'course',
    priority: task?.priority,
    due_date: new Date(task?.due_date),
    description: task?.description,
    operations: task?.operations?.map((operation: IOperation) => operation.id),
  });

  const handleReAssginSubmit = () => {
    const formattedDate = moment(form.due_date).format('YYYY-MM-DD');
    const finalForm = {
      ...form,
      due_date: formattedDate,
      content_id: task?.content?.tool_id || task?.content_id,
    };

    reAssign(
      { code: task?.code, payload: finalForm },
      {
        onSuccess: () => {
          onOpenChange();
        },
      }
    );
  };

  return (
    <Modal
      className="overflow-visible"
      width={800}
      open={open}
      onOpenChange={onOpenChange}
      modalHeader={t('updateTaskDialog.title')}
    >
      <Form onSubmit={handleReAssginSubmit} className="min-h-96 ">
        <div className="flex gap-4">
          <div className="flex flex-col gap-3">
            <ComboboxInput
              name="reviewer"
              label={t('assignTask.form.reviewer')}
              placeholder={t('assignTask.form.assigned_to')}
              options={users?.items || []}
              optionLabelKey={'email'}
              optionValueKey="id"
              value={form.assigned_to}
              onChange={setFieldValue('assigned_to')}
              dropIcon
              validators={[isRequired()]}
            />
            <CalendarInput
              label={t('assignTask.form.due_date')}
              name="due_date"
              value={form.due_date}
              onChange={setFieldValue('due_date')}
              validators={[isRequired()]}
              disabled={(date: Date) =>
                date < new Date(new Date().getTime() + 8 * 60 * 60 * 1000) || date < new Date('1900-01-01')
              }
            />
          </div>
          <div className="w-full flex flex-col gap-1">
            <MultiSelect
              name="operations"
              label={t('operations')}
              options={operationsOptions}
              onChange={setFieldValue('operations')}
              value={form.operations || []}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={2}
              validators={[isNotEmptyArray()]}
            />
            <Textarea
              rows={6}
              label={t('metadataPage.description')}
              value={form.description}
              onChange={setFieldValue('description')}
              className="w-full p-2 border rounded mb-3.5"
              validators={[isRequired()]}
            />
            <ComboboxInput
              name="priority"
              label={t('assignTask.form.priority')}
              placeholder={t('assignTask.form.priority')}
              options={DnaEnums.TaskPriorityStatus}
              value={form.priority}
              onChange={setFieldValue('priority')}
              dropIcon
              validators={[isRequired()]}
            />
          </div>
        </div>
        <div className="pt-1 mt-5 border-t border-border flex justify-end">
          <Button loading={isPending} disabled={isPending} type="submit" className="mt-4 w-fit">
            {t('task.updatePopup.button')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default UpdateTaskDialog;
