import { useGetUserStatistics } from '@/modules/auth/apis/queries';
import { ConetntPieChart } from './content-pie-chart';
import { ContentBarChart } from './content-bar-chart';
import { Icon } from '@/components';
const GeneralInformation = () => {
  const { data, isPending } = useGetUserStatistics();

  if (isPending) {
    return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />;
  }

  return (
    data && (
      <div className="flex flex-col xl:flex-row justify-center gap-3">
        <ConetntPieChart data={data} />
        <ContentBarChart data={data} />
      </div>
    )
  );
};

export default GeneralInformation;
