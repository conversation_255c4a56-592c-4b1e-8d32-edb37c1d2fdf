export const getSlideStatusStyle = (status: string) => {
  if (status === 'completed') return 'text-green-500 bg-green-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'draft') return 'text-indigo-500 bg-indigo-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'pending') return 'text-yellow-500 bg-yellow-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'in_progress') return 'text-blue-500 bg-blue-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'open') return 'text-teal-500 bg-teal-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'reviewed') return 'text-emerald-500 bg-emerald-500/20 text-sm py-1 px-2 rounded-md';
  if (status === 'rejected') return 'text-red-500 bg-red-500/20 text-sm py-1 px-2 rounded-md';
  return '';
};
export const SLIDE_TEMPLATES = [
  {
    value: 'definition',
    label: 'Definition',
    image: '/definition_slide.png',
  },
  {
    value: 'explanation',
    label: 'Explanation',
    image: '/explanation_slide.png',
  },
  {
    value: 'list',
    label: 'List',
    image: '/list_slide.png',
  },
  {
    value: 'comparison',
    label: 'Comparison',
    image: '/comparison_slide.png',
  },
  {
    value: 'hierarchy',
    label: 'Hierarchy',
    image: '/hierarchies_slide.png',
  },
  {
    value: 'main_ideas',
    label: 'Main Ideas',
    image: '/mainIdeas_slide.png',
  },
  {
    value: 'header',
    label: 'Header',
    image: '/header_slide.png',
  },
  {
    value: 'summary',
    label: 'Summary',
    image: '/summary_slide.png',
  },
] as const;
