import { useEffect, useState } from 'react';
import { Form, TextInput, Textarea, useForm, DnaEnums, useValidate, Icon, useNotify } from '@/index';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { useGenarateDnaData, useGetDnaById, useUploadDnaFile } from '@/modules/app/dna/apis/queries';
import { Outlet, useLocation, useNavigate, useParams } from 'react-router-dom';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { But<PERSON> } from '@/components/ui/button';

import ToolCreation from '@/components/creation-ui/creation';
const DnaCreation = () => {
  // State
  const [details, setDetails] = useState<any>(null);
  const [toggle, setToggle] = useState<any>('Web');
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Hooks
  const { isRequired } = useValidate();
  const { notify } = useNotify();
  const { t, i18n } = useTranslation();
  const { dnaId } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { data } = useGetDnaById(dnaId || details?.id);
  const { mutate, isPending } = useGenarateDnaData();
  const { mutate: uploadDna } = useUploadDnaFile();
  const { data: subjectOptions } = useGetSingleMetadata('subject');
  const { data: audienceOptions } = useGetSingleMetadata('audience');
  const { data: languageOptions } = useGetSingleMetadata('language');
  const { data: blooms_taxonomyOptions } = useGetSingleMetadata('bloom"s_taxonomy');
  const { data: dna } = useGetDnaById(dnaId || '');

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const handleDnaNavigation = (newDnaId: string | number) => {
    if (dnaId) {
      // remove the id from the cuernt location
      const myLocation = location.pathname.replace(`/${dnaId}`, '');
      navigate(`${myLocation}/${newDnaId}`);
      return;
    }
    navigate(`${location.pathname}/${newDnaId}`);
  };

  useEffect(() => {
    if (data && Object.keys(data).length > 0) {
      if (data.title) {
        setDetails({ title: data.title, learning_obj: data.learning_objectives, id: data.id });
      }

      setFormValue({
        version: 'v.1.02',
        topic: data.title,
        subject_id: data?.subject?.id,
        other_subject: data?.other_subject,
        audience_id: data.audience?.id,
        bloom_tax_id: data.bloom_tax?.id,
        language_id: data.language?.id,
        model: data.model || 'gpt-4o',
        long_text: data.long_text,
        old_dna: data.dna,
        file_path: data.file_path,
        url: data.url,
        txt: data.txt,
        audio_length: data.audio_length,
        method: 1,
      });
    }
  }, [data]);

  useEffect(() => {
    if (!dnaId) {
      handleClear();
    }
  }, [dnaId]);
  //Form
  const { form, setFieldValue, setFormValue } = useForm({
    version: 'v.1.02',
    topic: '',
    subject_id: '',
    other_subject: '',
    audience_id: '',
    bloom_tax_id: '',
    language_id: '',
    model: 'gpt-4.1',
    long_text: '',
    old_dna: '',
    file_path: '',
    url: '',
    txt: '',
    audio_length: '',
    method: 1,
  });

  const handleClear = () => {
    setFormValue({
      version: 'v.1.02',
      topic: '',
      subject_id: '',
      other_subject: '',
      audience_id: '',
      bloom_tax_id: '',
      language_id: '',
      model: 'gpt-4.1',
      long_text: '',
      old_dna: '',
      file_path: '',
      url: '',
      txt: '',
      audio_length: '',
      method: 1,
    });
    setDetails(null);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };
  const handleToggleChange = (newValue: string) => {
    setToggle(newValue);
    if (newValue === 'Web') {
      setFieldValue('method')(1);
    }
  };

  const handleGenerate = async () => {
    mutate(form, {
      onSuccess: (data) => {
        setDetails(data);
        handleDnaNavigation(data.id);
        setIsCollapsed(true);
      },
    });
  };

  const handleFileUpload = async (event: any) => {
    const file = event.target.files[0];
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('dnaCreationPage.form.file.validation'));
      event.target.value = '';
    } else {
      uploadDna(file, {
        onSuccess: (data) => {
          setFieldValue('file_path')(data.url);
        },
      });
    }
  };

  return (
    <div className="grid relative">
      <div className={`flex  transition-all duration-500 ease-in-out h-[calc(100dvh-152px)]`}>
        <div
          className={`
          transition-all duration-500 ease-in-out 
          ${isCollapsed ? 'w-0 opacity-0' : 'w-[27%] opacity-100 '}
        `}
        >
          <Form onSubmit={handleGenerate} className="h-full">
            <ToolCreation>
              <ToolCreation.Content>
                <div className="space-y-3">
                  <TextInput
                    name="title"
                    label={t('dnaCreationPage.form.title')}
                    placeholder={t('dnaCreationPage.form.title')}
                    value={form.topic}
                    onChange={setFieldValue('topic')}
                    isRequired
                    validators={[isRequired()]}
                  />
                  <ComboboxInput
                    name="subject"
                    label={t('dnaCreationPage.form.subject')}
                    placeholder={t('dnaCreationPage.form.subject')}
                    options={generateEnum(subjectOptions || [], 'id', labelKey)}
                    value={form.subject_id}
                    onChange={setFieldValue('subject_id')}
                    dropIcon
                    validators={[isRequired()]}
                  />
                  {form.subject === 'Other (write a subject)' && (
                    <TextInput
                      name="otherSubject"
                      label={t('dnaCreationPage.form.otherSubject')}
                      placeholder={t('dnaCreationPage.form.otherSubject')}
                      value={form.other_subject}
                      onChange={setFieldValue('other_subject')}
                      isRequired
                      validators={[isRequired()]}
                    />
                  )}
                  <ComboboxInput
                    name="audience"
                    label={t('dnaCreationPage.form.audience')}
                    placeholder={t('dnaCreationPage.form.audience')}
                    options={generateEnum(audienceOptions || [], 'id', labelKey)}
                    value={form.audience_id}
                    onChange={setFieldValue('audience_id')}
                    dropIcon
                    validators={[isRequired()]}
                  />
                  <ComboboxInput
                    name="bloom'sTaxonomy"
                    label={t("dnaCreationPage.form.bloom'sTaxonomy")}
                    placeholder={t("dnaCreationPage.form.bloom'sTaxonomy")}
                    options={generateEnum(blooms_taxonomyOptions || [], 'id', labelKey)}
                    value={form.bloom_tax_id}
                    onChange={setFieldValue('bloom_tax_id')}
                    dropIcon
                    validators={[isRequired()]}
                  />
                  <ComboboxInput
                    name="language"
                    label={t('dnaCreationPage.form.language')}
                    placeholder={t('dnaCreationPage.form.language')}
                    options={generateEnum(languageOptions || [], 'id', labelKey)}
                    value={form.language_id}
                    onChange={setFieldValue('language_id')}
                    dropIcon
                    validators={[isRequired()]}
                  />
                  <ComboboxInput
                    name="aiBaseModel"
                    label={t('dnaCreationPage.form.aiBaseModel')}
                    placeholder={t('dnaCreationPage.form.aiBaseModel')}
                    options={DnaEnums.ai_base_model}
                    value={form.model}
                    onChange={setFieldValue('model')}
                    disabled
                    dropIcon
                    validators={[isRequired()]}
                  />
                  <ComboboxInput
                    name="contentLength"
                    label={t('dnaCreationPage.form.contentLength')}
                    placeholder={t('dnaCreationPage.form.contentLength')}
                    options={DnaEnums.content_length}
                    value={form.audio_length}
                    onChange={setFieldValue('audio_length')}
                    dropIcon
                    validators={[isRequired()]}
                  />
                  <TextInput
                    name="context"
                    label={t('dnaCreationPage.form.context')}
                    placeholder={t('dnaCreationPage.form.contextPlaceholder')}
                    value={form.long_text}
                    onChange={setFieldValue('long_text')}
                  />
                  <p className="text-sm font-medium">{t('dnaCreationPage.form.sources')}</p>
                  {form.model === 'gpt-4o' && (
                    <div>
                      <ToggleGroup
                        className="pt-2 pl-3 rtl:flex-row-reverse"
                        variant="outline"
                        value={toggle || 'Web'}
                        type="single"
                        onValueChange={handleToggleChange}
                      >
                        <ToggleGroupItem value="Web" aria-label="Toggle Web">
                          <p>{t('Web')}</p>
                        </ToggleGroupItem>
                        <ToggleGroupItem value="URL" aria-label="Toggle URL">
                          <p>{t('URL')}</p>
                        </ToggleGroupItem>
                        <ToggleGroupItem value="File" aria-label="Toggle File">
                          <p>{t('File')}</p>
                        </ToggleGroupItem>
                        <ToggleGroupItem value="Text" aria-label="Toggle Text">
                          <p>{t('Text')}</p>
                        </ToggleGroupItem>
                      </ToggleGroup>
                      {toggle !== 'Web' && toggle !== '' && (
                        <div>
                          <RadioGroup
                            dir={i18n.dir()}
                            className="mt-5 flex flex-col sm:flex-row gap-4"
                            defaultValue="comfortable"
                            onValueChange={setFieldValue('method', Number)}
                          >
                            <div className="flex items-center gap-2">
                              <RadioGroupItem value="1" id="r1" />
                              <Label htmlFor="r1">{t('Method 1')}</Label>
                            </div>
                            <div className="flex items-center gap-2">
                              <RadioGroupItem value="2" id="r2" />
                              <Label htmlFor="r2">{t('Method 2')}</Label>
                            </div>
                          </RadioGroup>
                        </div>
                      )}
                    </div>
                  )}
                  {toggle === 'URL' ? (
                    <div className=" space-y-1">
                      <TextInput
                        name="context"
                        label={t('dnaCreationPage.form.title')}
                        value={form.url}
                        onChange={setFieldValue('url')}
                      />
                    </div>
                  ) : (
                    ''
                  )}
                  {toggle === 'File' && (
                    <div className=" space-y-1">
                      <div id="fileUpload" className="max-w-md">
                        <div className="mb-2 block">
                          <Label htmlFor="file">{t('File')}</Label>
                        </div>
                        <Input
                          type="file"
                          accept="application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                          onChange={handleFileUpload}
                        />
                        <span className="block text-sm text-gray-500 mt-2">Accepted file types: PDF, DOC, DOCX</span>
                      </div>
                    </div>
                  )}
                  {toggle === 'Text' && (
                    <div className=" space-y-1">
                      <div className="">
                        <Textarea
                          className=""
                          label={t('dnaCreationPage.form.text')}
                          value={form.txt}
                          onChange={setFieldValue('txt')}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </ToolCreation.Content>
              <ToolCreation.Buttons>
                <div className="flex gap-2 w-full">
                  <Button
                    loading={isPending}
                    disabled={isPending}
                    className="min-w-[150px] w-full mt-4 rtl:order-last"
                    type="submit"
                  >
                    {t('generate.dna')}
                  </Button>
                  <Button
                    disabled={isPending}
                    type="button"
                    variant={'outline'}
                    className="mt-4 min-w-[100px]"
                    onClick={handleClear}
                  >
                    {t('clear')}
                  </Button>
                </div>
              </ToolCreation.Buttons>
            </ToolCreation>
          </Form>
        </div>
        <div
          className={`bg-transparent self-center cursor-pointer w-[10px] ${isCollapsed ? 'mr-4' : 'mx-2'} `}
          onClick={toggleCollapse}
        >
          <Icon
            className={`${i18n.language === 'ar' ? 'rotate-180' : ''}`}
            icon={`line-md:chevron-small-${isCollapsed ? 'right' : 'left'}`}
            width="25"
          />
        </div>
        <div
          className={`transition-all duration-500 ease-in-out overflow-x-auto grid w-full
            `}
        >
          <div className="overflow-x-auto">
            {dnaId && dna ? (
              <Outlet />
            ) : (
              <div className="flex flex-col bg-white dark:bg-background justify-center items-center ms-2 h-full min-h-[650px] border rounded-lg relative">
                <img src="/empty-activity.svg" className="" />
                <img
                  src="/arrow.png"
                  className={`absolute top-[20%] ${
                    i18n.dir() === 'ltr' ? 'left-[25%] rotate-[223deg]' : 'right-[24%]'
                  } `}
                />
                <div className="flex flex-col gap-2 my-5 ml-6">
                  <h1 className="self-center text-2xl">{t('empty.DNATitle')}</h1>
                  <p className="self-center text-slate-500">{t('empty.DNADescription')}</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DnaCreation;
