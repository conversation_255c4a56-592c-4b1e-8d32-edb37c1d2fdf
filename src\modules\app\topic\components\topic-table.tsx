import { useEffect, useMemo, useState } from 'react';
import { useConfirmDialog, Icon, useNotify, ProtectedComponent, DnaEnums } from '@/index';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
import { getContentStatusStyle } from '@/utils/helpers';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';
import { ITopic } from '../types';
import { useCreateDna, useGenerateDNASingleTopic } from '../apis/queries';
import { Button } from '@/components/ui/button';
import { useQueryClient } from '@tanstack/react-query';
import { useDeleteDna } from '../../dna/apis/queries';
import EditDna from '../../components/edit-dna';
import { useGenerateAllDnasInTopic } from '../../course/apis/queries';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import { Input } from '@/components/ui/input';
import { IDNA } from '../../dna/types';

const TopicTable = ({
  topicId,
  topics,
  setTopics,
  topic,
}: {
  topicId: string;
  topics: any;
  setTopics: any;
  topic: any;
}) => {
  // State
  const [edit, setEdit] = useState<any>(false);
  const [editData, setEditdata] = useState<any>(null);
  const [newDnaTitle, setNewDnaTitle] = useState<string>('');
  const [fakeLoading, setFakeLoading] = useState<boolean>(false);

  const { mutate: generate, variables } = useCreateDna();
  const {
    mutate: generateAllDnasInTopic,
    isPending: isGeneratingAllDnas,
    variables: generateAllDnasPayload,
  } = useGenerateAllDnasInTopic();
  const { mutate: deleteDna } = useDeleteDna();
  const queryClient = useQueryClient();

  // Hooks
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { t } = useTranslation();
  const { mutate: addNewDnaInsideTopic, isPending: isAddingNewDna } = useGenerateDNASingleTopic();

  // Functions
  const generateDnaFromTopic = async (topic: any) => {
    generate(
      { id: topic.id, audio_length: topic.audio_length },
      {
        onSuccess: () => {
          notify.success(t('notify.danGenerated'));
        },
      }
    );
  };
  const handleAddNewDnaInsideTopic = async () => {
    addNewDnaInsideTopic(
      {
        id: topicId,
        title: newDnaTitle,
      },
      {
        onSuccess: () => {
          notify.success(t('Title added successfully'));
        },
      }
    );
  };

  const handleEdit = (topic: any) => {
    setEditdata(topic);
  };

  const handleDropdownChange = (e: any, topic: any) => {
    const updatedTopic = { ...topic, audio_length: e };
    setTopics(topics?.map((t: any) => (t.id === topic.id ? updatedTopic : t)));
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationText')}</p>
      </div>
    );
  };

  useEffect(() => {
    if (fakeLoading) {
      setTimeout(() => {
        setFakeLoading(false);
      }, 10000);
    }
  }, [fakeLoading]);

  const handleTopicDelete = async (topic: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        deleteDna(topic.id, {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ['topic'] });
            notify.success(t('notify.dnaDeleted'));
          },
        });
      },
    });
  };
  const columns: ITableColumn<ITopic & IDNA>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('topicContentPage.table.status'),
        width: '150px',
        cell: ({ row }) => getContentStatusStyle(row?.dna_status),
      },
      {
        accessorKey: 'title',
        header: t('topicContentPage.table.title'),
        width: '150px',
        cell: ({ row }) => (
          <div dir="auto">
            <p className="font-medium">{row.title}</p>
          </div>
        ),
      },
      {
        accessorKey: 'learning_objectives',
        header: t('dnaCreationPage.learningObjective'),
        width: '180px',
        cell: ({ row }) => (
          <div dir="auto">
            <p>{row.learning_objectives}</p>
          </div>
        ),
      },
      {
        accessorKey: 'bloom_tax',
        header: t('topicContentPage.table.audience'),
        width: '200px',
        cell: ({ row }) => row.audio_length,
        // <Combobox
        //   placeholder=""
        //   options={DnaEnums.content_length}
        //   value={row.audio_length}
        //   onChange={(e: any) => handleDropdownChange(e, row)}
        // />
      },

      {
        accessorKey: 'actions',
        header: t('topicContentPage.table.actions'),
        width: '190px',
        cell: ({ row }) => (
          <div className="flex gap-1 justify-start lg:justify-end items-center">
            {!row.dna_content ? (
              <ProtectedComponent requiredPermissions={'dna_create'}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Button
                        onClick={() => {
                          generateDnaFromTopic(row);
                        }}
                        loading={
                          variables?.id === row.id ||
                          isGeneratingAllDnas ||
                          topic.multi_dnas_generation_status === 'processing' ||
                          fakeLoading
                        }
                        className="flex gap-2 items-center"
                      >
                        <Icon icon="mage:stars-a-fill" width={22} className="cursor-pointer" />
                        {t('generate')}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('generate')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ProtectedComponent>
            ) : (
              <ProtectedComponent requiredPermissions={'dna_edit'}>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Button variant="outline" size="icon" type="button">
                        <Icon
                          onClick={() => {
                            handleEdit(row), setEdit(true);
                          }}
                          icon="basil:edit-outline"
                          width={25}
                          className="text-primary cursor-pointer"
                        />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('edit')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </ProtectedComponent>
            )}
            <ProtectedComponent requiredPermissions={'dna_delete'}>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Button variant="outline" size="icon" type="button">
                      <Icon
                        onClick={(e: any) => {
                          e.stopPropagation();
                          e.preventDefault();
                          handleTopicDelete(row);
                        }}
                        icon="gg:trash"
                        width="25"
                        className="text-red-500 cursor-pointer"
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>{t('delete')}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </ProtectedComponent>
          </div>
        ),
      },
    ];
  }, [variables, isGeneratingAllDnas, topic]);

  return (
    <>
      <Table rows={topics || []} columns={columns}>
        <TableContentHeader className="border broder-t rounded-md justify-end">
          <div className="flex gap-2 items-center">
            <Input
              placeholder={t('topic.addDna')}
              value={newDnaTitle}
              onChange={(e: any) => setNewDnaTitle(e.target.value)}
            />
            <Button
              type="button"
              loading={isAddingNewDna || topic.multi_dnas_generation_status === 'processing' || fakeLoading}
              onClick={handleAddNewDnaInsideTopic}
              className="border-primary"
            >
              + {t('CoursePlanCreationPage.add')}
            </Button>
          </div>
          <ConditionalComponent status={topic?.topic_status} wantedStatus={StatusClass.TOPIC.EDIT.NO_CONTENT}>
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger>
                  <Button
                    loading={
                      (isGeneratingAllDnas && generateAllDnasPayload === topic?.id) ||
                      topic.multi_dnas_generation_status === 'processing' ||
                      (fakeLoading && generateAllDnasPayload === topic?.id)
                    }
                    onClick={() => {
                      generateAllDnasInTopic(topic?.id);
                      setFakeLoading(true);
                    }}
                    className="flex items-center gap-2 min-w-[100px]"
                  >
                    {t('generateAll')}
                    <Icon icon="mdi:stars-outline" width={20} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>{t('generateAllDnasInsideTopic')}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </ConditionalComponent>
        </TableContentHeader>
        <TableContent>
          <TableContentBody />
        </TableContent>
      </Table>
      {edit && <EditDna onOpen={edit} onClose={() => setEdit(false)} data={editData} />}
    </>
  );
};

export default TopicTable;
