import { useEffect, useRef, useState } from 'react';
import { useValidate, useForm, TextInput, Textarea, Form, ComboboxInput, useNotify, Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useGetUser } from '@/modules/auth/apis/queries';
import { Button } from '@/components/ui/button';
import { useUpdateProfile } from '@/modules/app/users/apis/queries';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon } from 'lucide-react';
import moment from 'moment';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';

const Profile = () => {
  const { data: allmetaData } = useGetSingleMetadata('');
  const { t, i18n } = useTranslation();
  const { notify } = useNotify();
  const [loading, setLoading] = useState(false);
  const [imageLoading, setImageLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  const educationsOptions = allmetaData?.filter((item) => item.type === 'educations') || [];
  const universitiesOptions = allmetaData?.filter((item) => item.type === 'universities') || [];
  const nationalitiesOptions = allmetaData?.filter((item) => item.type === 'nationalities') || [];
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const labelSubKey = i18n.dir() === 'ltr';

  const { isRequired } = useValidate();
  // id ? useGetUserById(id) : useGetUser();
  const { data } = useGetUser();
  const { mutate, isPending } = useUpdateProfile();
  const { form, setFieldValue } = useForm({
    name: data.name || null,
    info: data.info || null,
    profession: data.profession || null,
    nationality_id: data?.nationality?.id || null,
    education_id: data?.education?.id || null,
    university_id: data?.university?.id || null,
    nationally_number: data?.nationally_number || null,
    birth_date: data?.birth_date || null,
    sex: data?.sex || null,
    image: data?.image || null,
    cv: data?.cv || null,
  });

  const isDataChanged = () => {
    return (
      form.name != data.name ||
      form.info != data.info ||
      form.profession != data.profession ||
      form.nationality_id != data?.nationality_id ||
      form.education_id != data.education_id ||
      form.university_id != data.university_id ||
      form.nationally_number != data?.nationally_number ||
      form.birth_date != data?.birth_date ||
      form.sex != data?.sex ||
      form.image != data?.image ||
      form.cv != data?.cv
    );
  };
  // Functions
  const handleSubmit = async () => {
    let finalForm = form;
    if (typeof form.image == 'string') {
      const { image, ...restFields } = finalForm;
      finalForm = restFields;
    }
    if (typeof form.cv == 'string') {
      const { cv, ...restFields } = finalForm;
      finalForm = restFields;
    }
    mutate(finalForm);
  };

  console.log(data);

  useEffect(() => {
    setFieldValue({
      name: data.name || null,
      info: data.info || null,
      profession: data.profession || null,
      nationality_id: data?.nationality_id || null,
      education_id: data?.education_id || null,
      university_id: data?.university_id || null,
      nationally_number: data?.nationally_number || null,
      birth_date: data?.birth_date || null,
      sex: data?.sex || null,
      image: data?.image || null,
      cv: data?.cv || null,
    });
  }, [data]);

  const handleFileUpload = (event: any) => {
    setLoading(true);
    const file = event.target.files[0];
    const allowedTypes = ['application/pdf']; // Only allow PDF

    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('dnaCreationPage.form.file.validation'));
      event.target.value = '';
    } else {
      const formData = new FormData();
      formData.append('cv', file);
      setFieldValue('cv')(file);
    }
    setLoading(false);
  };

  const handleImageUpload = (event: any) => {
    setImageLoading(true);
    const file = event.target.files[0];
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']; // Only allow images

    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('Please select a valid image file (JPEG, PNG, JPG)'));
      event.target.value = '';
      setImageLoading(false);
    } else if (file) {
      const formData = new FormData();
      formData.append('image', file);
      setFieldValue('image')(file);
      setImageLoading(false);
    } else {
      setImageLoading(false);
    }
  };

  const accountStatus = data?.account_status;
  const capitalizedStatus = accountStatus
    ? accountStatus.charAt(0).toUpperCase() + accountStatus.slice(1).toLowerCase()
    : '';

  return (
    <Form className="space-y-5 " onSubmit={handleSubmit}>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full gap-5">
        <div>
          <h4 className="font-medium text-zinc-400 mb-2">{t('profile.profilePicture')}</h4>
          <div className="relative">
            <Input
              ref={imageInputRef}
              className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer `}
              type="file"
              id="image-upload"
              accept="image/*"
              onChange={handleImageUpload}
            />

            <div className="flex items-center gap-5">
              <div className={`relative size-24 border rounded-md overflow-hidden`}>
                <div className="w-full h-full bg-gray-100 flex flex-col justify-center">
                  {imageLoading ? (
                    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit mx-auto" width={30} />
                  ) : form?.image ? (
                    // Show image preview if there's an image
                    <img
                      src={typeof form.image === 'string' ? form.image : URL.createObjectURL(form.image)}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    // Show icon if no image
                    <div className="p-3 flex flex-col items-center justify-center">
                      <Icon icon={'ph:image'} className="text-gray-400" width={35} />
                    </div>
                  )}
                </div>
              </div>
              <Button type="button" onClick={() => imageInputRef.current?.click()}>
                {form?.image ? t('Change Image') : t('courseCreation.file.chooseFile')}
              </Button>
            </div>
            <span className="text-sm text-gray-500">
              {form?.image?.name ||
                (typeof form?.image === 'string' && form?.image
                  ? 'Current image'
                  : t('courseCreation.file.selectedFile'))}
            </span>
          </div>
        </div>
        <div>
          <h4 className="font-medium text-zinc-400 mb-2">{t('profile.myCv')}</h4>
          <div className="relative">
            <Input
              ref={fileInputRef}
              className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer `}
              type="file"
              id="file-upload"
              accept=".pdf"
              onChange={handleFileUpload}
            />

            <div className="flex items-center gap-5">
              <div className={`relative size-24 border rounded-md overflow-hidden`}>
                <div className="w-full h-full p-3 bg-gray-100 flex flex-col justify-center">
                  {loading ? (
                    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
                  ) : (
                    <Icon icon={'ph:file'} className="text-gray-400" width={35} />
                  )}
                  {form?.cv && <div className="text-xs max-w-16 truncate font-medium">{form?.cv}</div>}
                </div>
              </div>
              <Button type="button" onClick={() => fileInputRef.current?.click()}>
                {t('courseCreation.file.chooseFile')}
              </Button>
            </div>
            <span className="text-sm text-gray-500">{form?.cv || t('courseCreation.file.selectedFile')}</span>
          </div>
        </div>
        <TextInput name="email" label={t('email')} placeholder={data?.email} disabled={true} className="w-full" />
        <TextInput
          name="status"
          label={t('profile.status')}
          placeholder={t(capitalizedStatus)}
          disabled={true}
          className="w-full"
        />

        <TextInput
          name="name"
          label={t('name')}
          placeholder={t('profile.name')}
          value={form.name}
          onChange={setFieldValue('name')}
          validators={[isRequired()]}
          className="w-full"
        />

        <TextInput
          name="profession"
          label={t('profile.profession')}
          placeholder={t('profile.profession')}
          value={form.profession}
          onChange={setFieldValue('profession')}
        />
        <ComboboxInput
          placeholder={t('profile.education')}
          name="education"
          label={t('profile.education')}
          options={generateEnum(educationsOptions, 'id', labelKey)}
          value={form.education_id}
          onChange={setFieldValue('education_id')}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('profile.universities')}
          name="universities"
          label={t('profile.universities')}
          options={generateEnum(universitiesOptions, 'id', labelKey)}
          value={form.university_id}
          onChange={setFieldValue('university_id')}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('profile.nationalities')}
          name="nationalities"
          label={t('profile.nationalities')}
          options={generateEnum(nationalitiesOptions, 'id', labelKey)}
          value={form.nationality_id}
          onChange={setFieldValue('nationality_id')}
          dropIcon
        />

        <div>
          <h3 className="text-sm mb-2 font-medium">{t('profile.birthDate')}</h3>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={'outline'}
                className={cn(
                  'pl-3 w-full text-left font-normal rounded-md',
                  !form.birth_date && 'text-muted-foreground'
                )}
              >
                {form.birth_date ? moment(form.birth_date).format('DD/MM/YYYY') : <span>{t('profile.pickDate')}</span>}
                <CalendarIcon className="ltr:ml-auto rtl:mr-auto h-4 w-4 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                captionLayout="dropdown-buttons"
                selected={form.birth_date}
                onSelect={setFieldValue('birth_date')}
                disabled={(date) => date > new Date() || date < new Date('1900-01-01')}
                fromYear={1930}
                toYear={new Date().getFullYear()}
                initialFocus
                showOutsideDays={false}
              />
            </PopoverContent>
          </Popover>
        </div>

        <ComboboxInput
          placeholder={t('profile.gender')}
          name="gender"
          label={t('profile.gender')}
          options={[
            { value: 'male', label: labelSubKey ? 'Male' : 'ذكر' },
            { value: 'female', label: labelSubKey ? 'Female' : 'انثي' },
          ]}
          value={form.sex}
          onChange={setFieldValue('sex')}
          dropIcon
        />
        <TextInput
          name="profile.nationalNumber"
          label={t('profile.nationalNumber')}
          placeholder={t('profile.nationalNumber')}
          value={form.nationally_number}
          onChange={setFieldValue('nationally_number')}
        />
        <Textarea
          name="info"
          label={t('profile.info')}
          placeholder={t('profile.info')}
          value={form.info}
          onChange={setFieldValue('info')}
          validators={[isRequired()]}
        />
      </div>

      <Button disabled={!isDataChanged() || isPending} loading={isPending} className="min-w-[100px]">
        {t('profile.saveChanges')}
      </Button>
    </Form>
  );
};

export default Profile;
