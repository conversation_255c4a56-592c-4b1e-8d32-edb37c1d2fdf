import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarMenuSubItem,
  SidebarMenuSub,
  useSidebar,
} from '@/components/ui/sidebar';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useLocation } from 'react-router-dom';
import { Icon, ProtectedComponent, useScreenSize } from '@/index';
import { useTranslation } from 'react-i18next';
import LanguageLink from '@/components/language-link';
import { ChevronDown } from 'lucide-react';
import { ScrollArea } from './ui/scroll-area';
import { useHasPermission } from '@/modules/auth/store';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useEffect } from 'react';

export function AppSidebar() {
  // Hooks
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const { open, setOpen } = useSidebar();
  const screen = useScreenSize();
  // Computed
  const getActiveClasess = (itemPath: any) => {
    if (location.pathname.includes(itemPath)) {
      return 'bg-secondary';
    }
    return '';
  };

  useEffect(() => {
    if (screen.lt.xxl()) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [screen.size]);

  const menuItems = [
    {
      label: 'tools',
      path: '/app/tools',
      icon: 'radix-icons:dashboard',
      permission: [
        'dna_create',
        'topic_create',
        'course_create',
        'file_analyzer',
        'content_create',
        'program_plan_create',
        'content_dna_reformat',
      ],
      visible: true,
      subMenu: [
        {
          icon: 'ph:cube',
          label: 'dnaCreation',
          path: '/app/tools/dna-creation',
          permission: 'dna_create',
        },
        {
          icon: 'vaadin:cubes',
          label: 'topicCreation',
          path: '/app/tools/topic-creation',
          permission: 'topic_create',
        },
        {
          icon: 'ph:chart-bar',
          label: 'courseCreation',
          path: '/app/tools/course-plan',
          permission: 'course_create',
        },
        {
          icon: 'ph:file-thin',
          label: 'fileAnalyzer',
          path: '/app/tools/file-analyzer',
          permission: 'file_analyzer',
        },
        {
          icon: 'carbon:need',
          label: 'contentNeed',
          path: '/app/tools/content-need',
          permission: 'content_create',
        },
        {
          icon: 'solar:programming-broken',
          label: 'programPlan',
          path: '/app/tools/program-plan',
          permission: 'program_plan_create',
        },
        {
          icon: 'streamline:chat-bubble-square-write',
          label: 'contentRewrite',
          path: '/app/tools/rewrite-content',
          permission: 'content_dna_reformat',
        },
        {
          icon: 'bi:filetype-pdf',
          label: 'pdfSummarize',
          path: '/app/tools/pdf-summarize',
          permission: 'for_administration',
        },
      ],
    },
    {
      label: useHasPermission('user_index') ? 'usersManagement' : t('sidebar.roles'),
      icon: 'ph:users-three-duotone',
      path: useHasPermission('user_index') ? '/app/users' : '/app/roles',
      visible: true,
      permission: ['user_index', 'role_index'],
      subMenu: [
        {
          icon: 'flowbite:users-outline',
          label: 'users',
          path: '/app/users',
          permission: 'user_index',
        },
        {
          icon: 'oui:app-users-roles',
          label: 'roles',
          path: '/app/roles',
          permission: 'role_index',
        },
      ],
    },
    {
      label: 'myContent',
      path: '/app/my-content',
      icon: 'ph:database',
      visible: true,
      permission: 'dna_index',
      subMenu: [
        {
          icon: 'solar:dna-broken',
          label: 'DNAs',
          path: '/app/my-content/DNAs',
          permission: 'dna_index',
        },
        {
          icon: 'material-symbols:topic',
          label: 'Topics',
          path: '/app/my-content/topics',
          permission: 'topic_index',
        },
        {
          icon: 'hugeicons:course',
          label: 'Courses',
          path: '/app/my-content/courses',
          permission: 'course_index',
        },
        {
          icon: 'bi:collection-fill',
          label: 'Course Groups',
          path: '/app/my-content/course-group',
          permission: 'course_group_index',
        },
        {
          icon: 'fluent:content-view-28-regular',
          label: 'Content Needs',
          path: '/app/my-content/content-need',
          permission: 'content_index',
        },
        {
          icon: 'fluent-mdl2:education',
          label: 'Program Plans',
          path: '/app/my-content/program-plan',
          permission: 'program_plan_index',
        },
      ],
    },
    {
      label: 'contentLibrary',
      path: '/app/content-library',
      icon: 'hugeicons:library',
      visible: true,
      permission: 'content_lib_index',
    },
    {
      label: 'tasks',
      icon: 'hugeicons:task-01',
      visible: true,
      path: useHasPermission('for_administration') ? '/app/all-tasks' : '/app/my-tasks',
      permission: 'task_index',
      subMenu: [
        {
          icon: 'hugeicons:task-01',
          label: 'allTasks',
          path: '/app/all-tasks',
          permission: 'for_administration',
        },
        {
          icon: 'hugeicons:task-01',
          label: 'myTasks',
          path: '/app/my-tasks',
        },
      ],
    },
    {
      label: 'dashboard',
      path: '/app/dashboard',
      icon: 'carbon:dashboard',
      visible: true,
      permission: 'for_administration',
      subMenu: [
        {
          icon: 'ion:language',
          label: 'Languages',
          path: '/app/dashboard/keys',
          permission: 'for_administration',
        },
        {
          icon: 'icon-park-outline:data-two',
          label: 'Metadata',
          path: '/app/dashboard/metadata',
          permission: 'for_administration',
        },
        {
          icon: 'solar:dna-broken',
          label: 'Operations',
          path: '/app/dashboard/operations',
          permission: 'for_administration',
        },
        {
          icon: 'tdesign:system-2',
          label: 'modulesStatus',
          path: '/app/dashboard/modulesStatus',
          permission: 'for_administration',
        },
        {
          icon: 'system-uicons:keyboard',
          label: 'SystemLogs',
          path: '/app/dashboard/systemLogs',
          permission: 'show_log',
        },
      ],
    },
    {
      label: 'statistics-dashboard',
      path: '/app/statistics-dashboard',
      icon: 'file-icons:dashboard',
      visible: true,
      permission: 'for_administration',
    },
    {
      label: 'terms',
      path: '/app/terms',
      icon: 'iconamoon:options-light',
      visible: true,
    },
  ];
  const isRTL = i18n.language === 'ar';

  return (
    <TooltipProvider delayDuration={300}>
      <Sidebar collapsible="icon" side={isRTL ? 'right' : 'left'}>
        <SidebarHeader className={`flex items-center justify-center `}>
          <LanguageLink to="app/tools" className="flex flex-col items-center px-6 ">
            <span className={`font-bold ${open ? 'text-3xl' : 'text-xl mb-2'} `}>
              {open ? t('contentAssistant') : 'FX'}
            </span>
            {open && <span className="text-center">{t('contentAssistantSub')}</span>}
          </LanguageLink>
        </SidebarHeader>
        <SidebarContent className="py-2">
          <ScrollArea className="h-[calc(100vh-65px)]">
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu>
                  {menuItems
                    .filter((item) => item.visible)
                    .map((item, index) => (
                      <ProtectedComponent
                        requiredPermissions={item.permission || 'public'}
                        key={index}
                        requireAll={false}
                      >
                        {item.subMenu ? (
                          // Render collapsible menu item if it has subMenu
                          <Collapsible defaultOpen={index === 0} className="w-full group/collapsible">
                            <SidebarMenuItem>
                              <CollapsibleTrigger asChild>
                                <SidebarMenuButton className="w-full justify-between gap-2 rounded-lg px-2 py-1.5 hover:bg-accent">
                                  <div className="flex items-center gap-3.5">
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <span>
                                          <Icon icon={item.icon} width={20} />
                                        </span>
                                      </TooltipTrigger>
                                      <TooltipContent side={isRTL ? 'left' : 'right'}>
                                        {t(`sidebar.${item.label}`)}
                                      </TooltipContent>
                                    </Tooltip>
                                    <span>{t(`sidebar.${item.label}`)}</span>
                                  </div>
                                  <ChevronDown className="size-8 shrink-0 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-180" />
                                </SidebarMenuButton>
                              </CollapsibleTrigger>
                              <CollapsibleContent>
                                <SidebarMenuSub>
                                  {item.subMenu.map((subItem) => (
                                    <ProtectedComponent
                                      requiredPermissions={subItem?.permission || 'public'}
                                      key={subItem.path}
                                      requireAll={false}
                                    >
                                      <SidebarMenuSubItem
                                        className={`w-full justify-start gap-2 rounded-lg px-2 py-1.5 hover:bg-accent ${getActiveClasess(
                                          subItem.path
                                        )}`}
                                      >
                                        <LanguageLink
                                          to={subItem.path}
                                          className={`flex items-center gap-3.5 rt:justify-end`}
                                        >
                                          <Tooltip>
                                            <TooltipTrigger asChild>
                                              <span>
                                                <Icon icon={subItem.icon} width={22} />
                                              </span>
                                            </TooltipTrigger>
                                            <TooltipContent side={isRTL ? 'left' : 'right'}>
                                              {t(`sidebar.${subItem.label}`)}
                                            </TooltipContent>
                                          </Tooltip>
                                          <span>{t(`sidebar.${subItem.label}`)}</span>
                                        </LanguageLink>
                                      </SidebarMenuSubItem>
                                    </ProtectedComponent>
                                  ))}
                                </SidebarMenuSub>
                              </CollapsibleContent>
                            </SidebarMenuItem>
                          </Collapsible>
                        ) : (
                          // Render regular menu item if no subMenu
                          <ProtectedComponent
                            requiredPermissions={item.permission || 'public'}
                            key={index}
                            requireAll={false}
                          >
                            <SidebarMenuItem>
                              <SidebarMenuButton
                                asChild
                                className="w-full justify-start gap-2 rounded-lg px-2 py-1.5 hover:bg-accent"
                              >
                                <LanguageLink
                                  to={item.path}
                                  className={`flex items-center gap-3.5 ${getActiveClasess(item.path)}`}
                                >
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <span>
                                        <Icon icon={item.icon} width={22} />
                                      </span>
                                    </TooltipTrigger>
                                    <TooltipContent side={isRTL ? 'left' : 'right'}>
                                      {t(`sidebar.${item.label}`)}
                                    </TooltipContent>
                                  </Tooltip>
                                  <span>{t(`sidebar.${item.label}`)}</span>
                                </LanguageLink>
                              </SidebarMenuButton>
                            </SidebarMenuItem>
                          </ProtectedComponent>
                        )}
                      </ProtectedComponent>
                    ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </ScrollArea>
        </SidebarContent>
      </Sidebar>
    </TooltipProvider>
  );
}
