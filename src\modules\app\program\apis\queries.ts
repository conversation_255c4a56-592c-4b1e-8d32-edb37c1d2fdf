import { useNotify } from '@/hooks';
import { programId, programPlan<PERSON>tom } from '@/modules/app/program/store';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useSetAtom } from 'jotai';
import {
  addCourseLearningOutcome,
  addNewCourse,
  addNewLearningOutcome,
  addNewSkill,
  deleteCourse,
  deleteCourseLearningOutcome,
  deleteLearningOutcome,
  deleteSkill,
  editCourse,
  editLearningOutcome,
  editSkill,
  findProgramPlanById,
  programInfoStart,
  programStepTwo,
  setProgramPlanStep,
  updateCourseLearningOutcome,
} from './endpoints';
import { useTranslation } from 'react-i18next';

export const useGetProgramPLanById = (id: string | undefined) => {
  return useQuery({
    queryKey: ['programPlan', id],
    queryFn: () => (id ? findProgramPlanById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null
  });
};

export const useProgramInfoStart = () => {
  const { notify } = useNotify();
  const setData = useSetAtom(programPlanAtom);
  const setId = useSetAtom(programId);
  return useMutation({
    mutationFn: programInfoStart,
    onSuccess: (data) => {
      setData(data);
      setId(data.id);
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useProgramStepTwo = () => {
  const { notify } = useNotify();
  const setData = useSetAtom(programPlanAtom);

  return useMutation({
    mutationFn: programStepTwo,
    onSuccess: (data) => {
      setData((prev: any) => {
        return {
          ...prev,
          ...data,
        };
      });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAddProgramPlanLearningOutcome = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addNewLearningOutcome,
    onSuccess: () => {
      notify.success('Learing outcome add!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAddProgramPlanSkill = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addNewSkill,
    onSuccess: () => {
      notify.success('Learing outcome add!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useAddProgramPlanCourse = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addNewCourse,
    onSuccess: () => {
      notify.success('Learing outcome add!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateProgramPlanStep = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: setProgramPlanStep,
    onSuccess: () => {
      notify.success(t('notify.programPlanUpdated'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteLearningOutcome = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteLearningOutcome,
    onSuccess: () => {
      notify.success(t('notify.learningDeleted'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditLearningOutcome = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: editLearningOutcome,
    onSuccess: () => {
      notify.success(t('notify.editOutcome'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditSkill = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: editSkill,
    onSuccess: () => {
      notify.success(t('notify.editSkill'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteSkill = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteSkill,
    onSuccess: () => {
      notify.success(t('notify.skillDeleted'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteCourse = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteCourse,
    onSuccess: () => {
      notify.success(t('notify.courseDeleted'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useEditCourse = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: editCourse,
    onSuccess: () => {
      notify.success(t('notify.editCourse'));
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// hook to add new learning outcome into the program plan course
export const useAddCourseLearningOutcome = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addCourseLearningOutcome,
    onSuccess: () => {
      notify.success('Learing outcome add!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
// hook to delete learning outcome into the program plan course

export const useDeleteCourseLearningOutcome = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteCourseLearningOutcome,
    onSuccess: () => {
      notify.success('Learing outcome deleted!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// hook to update learning outcome into the program plan course

export const useUpdateCourseLearningOutcome = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateCourseLearningOutcome,
    onSuccess: () => {
      notify.success('Learing outcome updated!!');
      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
