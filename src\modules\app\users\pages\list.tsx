import { useF<PERSON>ch<PERSON>ist, GET_USERS_LIST, Icon, useConfirmDialog, ProtectedComponent } from '@/index';
import { Button } from '@/components/ui/button';
import { useMemo, useState } from 'react';
import EditUser from '../components/user-dialog';
import { dateAndTimeFormat } from '@/utils/helpers';
import { IUser } from '../types';
import { useToggleUsersStatus, useToggleUserStatus } from '@/modules/app/users/apis/queries';
import UserPasswordDialog from '../components/user-password-dilaog';
import { useTranslation } from 'react-i18next';

import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
  TableBulkActions,
} from '@/components/theTable';
import LanguageLink from '@/components/language-link';
import { useUsersTablesTabs } from '../../content-tabs';

function UserListpage() {
  // State
  const [selectedRows, setSelectedRows] = useState<any>([]);

  const [idBeingDeleted, setIdBeingDeleted] = useState<number | null>(null);
  const [editDialog, setEditDialog] = useState(false);
  const [updatePassword, setUpdatePassword] = useState(false);
  const [userData, setUserDate] = useState<IUser | null>(null);
  const userTableTab = useUsersTablesTabs();

  // hooks
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { mutate, isPending } = useToggleUserStatus();
  const { t, i18n } = useTranslation();

  const ConfirmApproveText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>{t('user.status.confirmApprove')}</p>
      </div>
    );
  };

  const ConfirmBlockText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>{t('user.status.confirmBlock')}</p>
      </div>
    );
  };
  const { mutate: toggleUsersStatus } = useToggleUsersStatus();

  const handleApproveUsers = () => {
    const ids = selectedRows.map((row: any) => row.id);
    toggleUsersStatus(
      { ids: ids, status: 'active' },
      {
        onSuccess: () => {
          refresh();
          hideConfirm();
          setSelectedRows([]);
        },
      }
    );
  };

  const handleBlockUsers = () => {
    const ids = selectedRows.map((row: any) => row.id);
    toggleUsersStatus(
      { ids, status: 'blocked' },
      {
        onSuccess: () => {
          refresh();
          hideConfirm();
        },
      }
    );
  };

  const showApproveUsersConfirm = async () => {
    showConfirm(ConfirmApproveText(), { onConfirm: handleApproveUsers });
  };

  const showBlockUsersConfirm = async () => {
    showConfirm(ConfirmBlockText(), { onConfirm: handleBlockUsers });
  };

  const handleToggleUser = (id: number) => {
    setIdBeingDeleted(id);
    mutate(id, {
      onSuccess: () => {
        refresh();
      },
    });
  };
  const {
    // Load States
    ready,
    loading,
    // List
    list,
    count,
    refresh,
    // Data Manipulation
    search,
    filters,
    pagination,
    setSearch,
  } = useFetchList<IUser[]>(GET_USERS_LIST, 'users', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      status: {
        enum: 'account_status',
        placeholder: 'userPage.filters.status',
      },
      role: {
        api: 'roles',
        placeholder: 'userPage.filters.role',
        filterBy: 'name',
      } as any,
    },
  });

  // Functions
  const colors: Record<any, { color: string; background: string }> = useMemo(() => {
    return {
      active: {
        color: 'text-green-500',
        background: 'bg-green-500/5',
      },
      blocked: {
        color: 'text-red-500',
        background: 'bg-red-500/5',
      },
      pending: {
        color: 'text-yellow-500',
        background: 'bg-yellow-500/5',
      },
    };
  }, []);
  // Computed values
  const bulkActions = useMemo(() => {
    return [
      {
        icon: 'solar:check-circle-linear',
        iconColor: 'text-green-600',
        label: 'approve',
        action: showApproveUsersConfirm,
      },

      {
        icon: 'iconoir:xmark-circle',
        iconColor: 'text-red-500',
        label: 'block',
        action: showBlockUsersConfirm,
      },
    ];
  }, [selectedRows]);
  const columns = useMemo((): ITableColumn<IUser>[] => {
    return [
      {
        accessorKey: 'id',
        header: t('userPage.table.id'),
        width: '100px',
      },
      {
        accessorKey: 'user',
        header: t('userPage.table.name'),
        width: '150px',
        cell: ({ row }) => (
          <LanguageLink to={`/app/users/${row.id}`} className="rtl:text-right">
            <Button variant={'link'}>
              <p>{row.name}</p>
            </Button>
          </LanguageLink>
        ),
      },
      {
        accessorKey: 'email',
        header: t('userPage.table.email'),
        width: '300px',
      },
      {
        accessorKey: 'roles',
        header: t('userPage.table.role'),
        width: '180px',
        cell: ({ row }) => {
          return row.roles.map((name: any) => <p className="rtl:text-right">{t(name)}</p>);
        },
      },
      {
        accessorKey: 'date',
        header: t('userPage.table.date'),
        width: '120px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'email_verified_at',
        header: t('userPage.table.verificationData'),
        width: '120px',
        cell: ({ row }) => (
          <div className="rtl:text-right">
            {row.email_verified_at ? (
              <p>{dateAndTimeFormat(row.email_verified_at, i18n.language)}</p>
            ) : (
              <p>{t('userPage.table.notVerification')}</p>
            )}
          </div>
        ),
      },
      {
        accessorKey: 'account_status',
        header: t('userPage.table.status'),
        width: '120px',
        cell: ({ row }) => {
          const element = row.account_status;
          return (
            <p
              className={`${colors[element].color} ${colors[element].background} text-center max-w-36 py-1 px-2 rounded-md `}
            >
              {t(`userPage.accountStatus.${element}`)}
            </p>
          );
        },
      },
      {
        accessorKey: 'actions',
        header: t('userPage.table.actions'),
        width: '230px',
        cell: ({ row }) => (
          <div className="flex gap-3">
            <Icon
              onClick={() => {
                setEditDialog(true), setUserDate(row);
              }}
              icon="basil:edit-outline"
              width={25}
              className="text-primary  cursor-pointer"
            />
            <Icon
              onClick={() => {
                setUpdatePassword(true), setUserDate(row);
              }}
              icon="mdi:password-outline"
              width={25}
              className="text-primary cursor-pointer"
            />
            <div className="flex gap-2">
              {row.account_status === 'active' ? (
                <Button
                  loading={isPending && row.id === idBeingDeleted}
                  className="min-w-24"
                  onClick={() => handleToggleUser(row.id)}
                  variant={'destructive'}
                >
                  {t('userPage.buttons.accountDeActivate')}
                </Button>
              ) : (
                <Button
                  loading={isPending && row.id === idBeingDeleted}
                  className="min-w-24"
                  onClick={() => handleToggleUser(row.id)}
                >
                  {t('userPage.buttons.accountActivate')}
                </Button>
              )}
            </div>
          </div>
        ),
      },
    ];
  }, [selectedRows]);

  return (
    <>
      <Table
        rows={list}
        columns={columns}
        loading={loading}
        multipleSelect={{
          selectedRows,
          setSelectedRows,
        }}
      >
        <TableContent>
          <TableContentHeader tabs={userTableTab}>
            <div className="flex gap-3 items-center">
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="ms-auto">{selectedRows.length > 0 && <TableBulkActions actions={bulkActions} />}</div>

            <ProtectedComponent requiredPermissions={'user_create'}>
              <div className="ms-auto">
                <LanguageLink to={'/app/user-creation'}>
                  <Button>
                    <span className="flex items-center gap-2">
                      <Icon icon="ic:round-add" width={17} />
                      <span className="text-sm">{t('userPage.buttons.headerButton')}</span>
                    </span>
                  </Button>
                </LanguageLink>
              </div>
            </ProtectedComponent>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {editDialog && userData && (
        <EditUser onOpen={editDialog} onOpenChange={() => setEditDialog(false)} data={userData} />
      )}
      {updatePassword && (
        <UserPasswordDialog onOpen={updatePassword} onClose={setUpdatePassword} data={userData} onFinish={refresh} />
      )}
    </>
  );
}

export default UserListpage;
