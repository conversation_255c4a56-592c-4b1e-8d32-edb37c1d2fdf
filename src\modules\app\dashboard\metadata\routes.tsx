import { lazy } from 'react';
import { ProtectedRoute } from '@/components';

const Metadata = lazy(() => import('./pages/list'));

export default [
  {
    path: 'metadata',
    element: (
      <ProtectedRoute requiredPermissions={'for_administration'}>
        <Metadata />
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.dashboard.metadata',
        title: 'breadcrumb.dashboard.metadata',
      };
    },
  },
];
