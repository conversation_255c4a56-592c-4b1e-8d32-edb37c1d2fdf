
import { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { DnaEnums } from '@/services';
import { useForm } from '@/index';
import { useTranslation } from 'react-i18next';
import { useGenerateMediaAnalysis } from '../../apis/queries';
import { Button } from '@/components/ui/button';
import { Form, ComboboxInput } from '@/index';
import { ProtectedComponent } from '@/components/protected-component';

const EmptyAnalysis = () => {
  const { t } = useTranslation();
  const { dnaId } = useParams();
  const { mutate, isPending } = useGenerateMediaAnalysis();

  const { form, setFieldValue } = useForm({
    id: dnaId,
    model: 'gpt-4.1',
  });


  useEffect(() => {
    if (dnaId) {
      setFieldValue('id')(dnaId);
    }
  }, [dnaId]);

  const handleSubmit = () => {
      mutate(form);
  };

  return (
    <div className="flex flex-col items-center justify-center my-10 ms-2 min-h-96">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-2xl text-slate-800">{t('dnaSinglePage.mediaAnalysis.noAnalysisTitle')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.mediaAnalysis.noAnalysisDescription')}</p>
      </div>
      <Form onSubmit={handleSubmit}>
        <div className="mb-5 w-80">
          <ComboboxInput
            name="model"
            placeholder={t('analysis.model')}
            label={t('analysis.model')}
            options={DnaEnums.ai_base_model}
            value={form.model}
            onChange={setFieldValue('model')}
          />
        </div>
        <ProtectedComponent requiredPermissions={'dna_analysis_create'}>
          <Button
            loading={isPending}
            className="flex items-center gap-2 px-5 mx-auto"
          >
            {t('dnaSinglePage.mediaAnalysis.analyze')}
          </Button>
        </ProtectedComponent>
      </Form>
    </div>
  );
};

export default EmptyAnalysis;
