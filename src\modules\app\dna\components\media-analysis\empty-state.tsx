import { ProtectedComponent } from '@/components/protected-component';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useGenerateMediaAnalysis } from '../../apis/queries';

const EmptyAnalysis = ({ dnaId }: { dnaId: number }) => {
  const { t } = useTranslation();
  const { mutate, isPending } = useGenerateMediaAnalysis();
  const handleGenerateMediaAnalysis = () => {
    if (dnaId) {
      mutate({ id: dnaId });
    }
  };

  return (
    <div className="flex flex-col items-center justify-center my-10 ms-2 min-h-96">
      <img src="/empty-activity.svg" alt="" />
      <div className="flex flex-col gap-2 my-5">
        <h1 className="self-center text-2xl text-slate-800">{t('dnaSinglePage.mediaAnalysis.noAnalysisTitle')}</h1>
        <p className="self-center text-slate-500">{t('dnaSinglePage.mediaAnalysis.noAnalysisDescription')}</p>
      </div>
      <ProtectedComponent requiredPermissions={'dna_analysis_create'}>
          <Button loading={isPending} className="flex items-center gap-2 px-5 mx-auto" onClick={handleGenerateMediaAnalysis}>
            {t('dnaSinglePage.mediaAnalysis.analyze')}
          </Button>
        </ProtectedComponent>
    </div>
  );
};

export default EmptyAnalysis;
