import { Form, Icon } from '@/components';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm, useNotify } from '@/hooks';
import { useTranslation } from 'react-i18next';
import { useSummarizePdf } from './apis/queries';
import { useState } from 'react';
import SummarizedPdflist from './components/summraized-list';

const PdfSummarize = () => {
  const [summarizedPdf, setSummarizedPdf] = useState<string[] | null>(null);
  const { notify } = useNotify();
  const { t } = useTranslation();
  const { mutate, isPending } = useSummarizePdf();

  const { form, setFieldValue } = useForm({
    file: '',
  });

  const handleFileUpload = async (event: any) => {
    const file = event.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      setFieldValue('file')(file);
    }
  };

  const handleSubmit = () => {
    mutate(form, { onSuccess: (data) => setSummarizedPdf(data?.summary) });
  };

  const handelCopyToClipboard = async (text: string[]) => {
    try {
      await navigator.clipboard.writeText(text.join('\n'));
      notify.success(t('promptCopied'));
    } catch (err) {
      notify.error(t('copyFailed'));
    }
  };

  return (
    <Card className="p-4 space-y-3">
      <Form className="space-y-4" onSubmit={handleSubmit}>
        <div className="max-w-md">
          <div className="mb-2 block">
            <Label htmlFor="file">{t('uploadFile')} </Label>
          </div>
          <Input type="file" accept="application/pdf" onChange={handleFileUpload} />
          <span className="block text-sm text-gray-500 mt-2">{t('pdfAnalyzer.acceptedFileTypes')}</span>
        </div>
        <div className="flex justify-between">
          <Button loading={isPending} disabled={!form.file} type="submit">
            {t('pdfAnalyzer.summarize')}
          </Button>
          {summarizedPdf && (
            <Icon
              icon="tabler:copy"
              width={25}
              className="text-primary cursor-pointer"
              onClick={() => handelCopyToClipboard(summarizedPdf)}
            />
          )}
        </div>
      </Form>
      {summarizedPdf && <SummarizedPdflist list={summarizedPdf} />}
    </Card>
  );
};

export default PdfSummarize;
