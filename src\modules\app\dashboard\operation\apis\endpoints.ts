import { IPermission } from '../../../roles/types';
import { Api } from '@/services';
import { IOperation } from '../types';
// get operation

export const getOperations = async (): Promise<IOperation[]> => {
  const { data } = await Api.get('/operations');
  return data.data.items;
};

// Get operation by ID
export const getOperationById = async (id: string | number): Promise<IOperation> => {
  const { data } = await Api.get(`/operations/${id}`);
  return data.data;
};

// Create a new operation
export const createOperation = async (payload: { name: string; permissions: IPermission }): Promise<IOperation> => {
  const { data } = await Api.post('/operations', payload);
  return data;
};

// Delete a operation by ID
export const deleteOperation = async (id: string | number): Promise<void> => {
  await Api.delete(`/operations/${id}`);
};

// Update a operation by ID
export const updateOperation = async (
  id: string,
  payload: { name: string; permissions: string[] }
): Promise<IOperation> => {
  const { data } = await Api.put(`/operations/${id}`, payload);
  return data;
};
