import { Outlet } from 'react-router-dom';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { memo, Suspense, lazy } from 'react';
import { Icon, PageLoader } from '@/components';
import { ChunkErrorBoundary } from '@/components/chunk-error-boundary';
import { useTranslation } from 'react-i18next';
import { lazyWithRetryAndExtract } from '@/utils/lazy-retry';

// Dynamic imports for better performance with retry functionality
const AppSidebar = lazyWithRetryAndExtract(
  () => import('@/components/app-sidebar'),
  (module) => module.AppSidebar
);
const AppHeader = lazyWithRetryAndExtract(
  () => import('./components/header'),
  (module) => module.AppHeader
);
const AppBreadcrumb = lazyWithRetryAndExtract(
  () => import('./components/breadcrumb'),
  (module) => module.AppBreadcrumb
);

export const AppMainLayout = memo(() => {
  const { i18n } = useTranslation();

  const currentLanguage = i18n.language;

  return (
    <SidebarProvider className="flex">
      <ChunkErrorBoundary>
        <Suspense fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}>
          <AppSidebar key={`sidebar-${currentLanguage}`} />
        </Suspense>
      </ChunkErrorBoundary>
      <main className="flex flex-col w-full overflow-x-auto">
        <div className="flex bg-sidebar shadow-sm py-3 justify-between items-center">
          <div className="flex gap-3 items-center">
            <SidebarTrigger className="ms-4" />
          </div>
          <ChunkErrorBoundary>
            <Suspense fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}>
              <AppHeader key={`header-${currentLanguage}`} showUserIcon={true} />
            </Suspense>
          </ChunkErrorBoundary>
        </div>
        <div className="p-6 space-y-4">
          <ChunkErrorBoundary>
            <Suspense fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}>
              <AppBreadcrumb key={`breadcrumb-${currentLanguage}`} />
            </Suspense>
          </ChunkErrorBoundary>
          <ChunkErrorBoundary>
            <Suspense fallback={<PageLoader />}>
              <Outlet />
            </Suspense>
          </ChunkErrorBoundary>
        </div>
      </main>
    </SidebarProvider>
  );
});

AppMainLayout.displayName = 'AppMainLayout';
