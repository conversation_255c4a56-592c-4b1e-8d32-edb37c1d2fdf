import { Outlet } from 'react-router-dom';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { memo, Suspense, lazy } from 'react';
import { Icon, PageLoader } from '@/components';
import { useTranslation } from 'react-i18next';
import ChunkErrorBoundary from '@/ChunkErrorBoundary';

// Dynamic imports for better performance
const AppSidebar = lazy(() => import('@/components/app-sidebar').then((module) => ({ default: module.AppSidebar })));
const AppHeader = lazy(() => import('./components/header').then((module) => ({ default: module.AppHeader })));
const AppBreadcrumb = lazy(() =>
  import('./components/breadcrumb').then((module) => ({ default: module.AppBreadcrumb }))
);

export const AppMainLayout = memo(() => {
  const { i18n } = useTranslation();

  const currentLanguage = i18n.language;

  return (
    <SidebarProvider className="flex">
      <ChunkErrorBoundary>
        <>
          <Suspense fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}>
            <AppSidebar key={`sidebar-${currentLanguage}`} />
          </Suspense>
          <main className="flex flex-col w-full overflow-x-auto">
            <div className="flex bg-sidebar shadow-sm py-3 justify-between items-center">
              <div className="flex gap-3 items-center">
                <SidebarTrigger className="ms-4" />
              </div>
              <Suspense
                fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}
              >
                <AppHeader key={`header-${currentLanguage}`} showUserIcon={true} />
              </Suspense>
            </div>
            <div className="p-6 space-y-4">
              <Suspense
                fallback={<Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />}
              >
                <AppBreadcrumb key={`breadcrumb-${currentLanguage}`} />
              </Suspense>
              <Suspense fallback={<PageLoader />}>
                <Outlet />
              </Suspense>
            </div>
          </main>
        </>
      </ChunkErrorBoundary>
    </SidebarProvider>
  );
});

AppMainLayout.displayName = 'AppMainLayout';
