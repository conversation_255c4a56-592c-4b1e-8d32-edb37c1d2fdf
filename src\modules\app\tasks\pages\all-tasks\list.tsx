import { useF<PERSON>ch<PERSON><PERSON>, ProtectedComponent, Icon } from '@/index';
import { useMemo, useState } from 'react';
import {
  convertStatusToFilterEnumByTitle,
  dateAndTimeFormat,
  getTaskStatusStyle,
  formatUserByNameAndEmail,
} from '@/utils/helpers';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

import LanguageLink from '@/components/language-link';
import { useRemoveTask, useReOpenTask } from '@/modules/app/tasks/apis/queries';
import AssignTaskDialog from '@/modules/app/course/components/assign-task-dialog';
import { ITask } from '../../types';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import { useGetModulesStatus } from '@/modules/app/dashboard/modules-status/apis/queries';
import { useTaskTablesTabs } from '@/modules/app/content-tabs';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import ListExpander from '@/modules/app/components/list-expander';
import UpdateTaskDialog from '@/modules/app/course/components/update-task-dialog';
function DnaList() {
  //  State
  const [taskDialog, setTaskDialog] = useState(false);
  const [isUpdateTaskDialogOpen, setIsUpdateTaskDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ITask | null>(null);
  const [taskData, setTaskData] = useState<ITask | null>(null);
  const { mutate, variables, isPending } = useRemoveTask();
  const { mutate: reOpenTask, isPending: isReOpening } = useReOpenTask();

  const { data: modulesStatus, isLoading: isLoadingStatus } = useGetModulesStatus();
  const taskTbaleTabs = useTaskTablesTabs();

  //  Hooks
  const { t, i18n } = useTranslation();
  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'Task', i18n.language);

  const {
    // Load States
    ready,
    loading,
    // List
    list,
    count,
    refresh,
    // Data Manipulation
    search,
    filters,
    pagination,
  } = useFetchList('/tasks', 'tasks', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 10,
    },
    filters: {
      status_id: {
        dynamicEnum: statusFilter,
        placeholder: 'assignedTask.statusFilter',
      },

      ...(useHasPermission('user_index')
        ? {
            assigned_to: {
              api: 'user/lookup',
              placeholder: 'assignedTask.userFIlter',
            },
          }
        : {}),
    },
  });

  const columns: ITableColumn<ITask>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '180px',
        cell: ({ row }) => getTaskStatusStyle(row?.task_status),
      },
      {
        accessorKey: 'title',
        header: t('assignedTask.table.title'),
        width: '180px',
        cell: ({ row }) => {
          return (
            <div className="break-words">
              <LanguageLink to={`/app/all-tasks/course/${row.code}`}>
                <Button className="px-0" variant={'link'}>
                  {row?.content?.tool_data?.title || row?.content?.tool_data?.info}
                </Button>
              </LanguageLink>
            </div>
          );
        },
      },
      {
        accessorKey: 'description',
        header: t('assignedTask.table.description'),
        width: '180px',
        tooltip: true,
      },
      {
        accessorKey: 'priority',
        header: t('assignedTask.table.priority'),
        width: '80px',
      },
      {
        accessorKey: 'code',
        header: t('assignedTask.table.code'),
        width: '250px',
        tooltip: true,
      },
      {
        accessorKey: 'due_date',
        header: t('assignedTask.table.due_date'),
        width: '100px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.due_date, i18n.language);
        },
      },
      {
        accessorKey: 'assigned_to',
        header: t('assignedTask.table.assigned_to'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.assigned_to);
        },
      },
      {
        accessorKey: 'operations',
        header: t('assignedTask.table.operations'),
        width: '250px',
        cell: ({ row }) => <ListExpander initialVisiableItems={1} list={row.operations.map((data) => data.name)} />,
      },
      {
        accessorKey: 'notes',
        header: t('assignedTask.table.notes'),
        width: '50px',
        cell: ({ row }) => row.content.total_reviewer_notes || "_",
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '120px',
        cell: ({ row }) => {
          return (
            <div className="flex">
              <ProtectedComponent requiredPermissions={'task_edit'}>
                {/* <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => {
                            setTaskDialog(true);
                            setTaskData(row);
                          }}
                        >
                          <Icon
                            className="text-primary"
                            icon="material-symbols:assignment-add-outline-rounded"
                            width={25}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.assignTask')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider> */}
                {row.can_update_task && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="ghost"
                          onClick={() => {
                            setIsUpdateTaskDialogOpen(true);
                            setSelectedTask(row);
                          }}
                        >
                          <Icon className="text-primary" icon="hugeicons:task-edit-01" width={27} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.updateTask')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}

                {row.can_reopen_task && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button loading={isReOpening} size="icon" variant="ghost" onClick={() => reOpenTask(row?.code)}>
                          <Icon
                            className="text-primary"
                            icon="material-symbols-light:assignment-return-rounded"
                            width={25}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.reopen')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </ProtectedComponent>
              {row.content.tool_data.tasks.can_assign_child_task_to_parent && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="icon"
                        variant="ghost"
                        onClick={() => {
                          setTaskDialog(true), setTaskData(row);
                        }}
                      >
                        <Icon className="text-primary" icon="fluent-mdl2:assign" width={25} />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t('alltask.table.assignChildTask')}</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
              <ConditionalComponent
                status={row.task_status}
                wantedStatus={[StatusClass.TASK.REVIEW.COMPLETED]}
                operator="not"
              >
                <ProtectedComponent requiredPermissions={'task_delete'}>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex justify-end w-full mx-3">
                          <Button
                            size="icon"
                            variant="ghost"
                            disabled={isPending || row.code === variables?.code}
                            loading={isPending || row.code === variables?.code}
                            onClick={() =>
                              mutate(
                                { code: row.code },
                                {
                                  onSuccess() {
                                    refresh();
                                  },
                                }
                              )
                            }
                          >
                            <Icon icon="gg:trash" width={25} className="text-red-500" />
                          </Button>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>{t('delete')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </ProtectedComponent>
              </ConditionalComponent>
            </div>
          );
        },
      },
    ];
  }, [taskData]);
  return (
    <>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={taskTbaleTabs}>
            <div className="flex items-center gap-3">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('assignedTask.searchPlaceholder')} />
              <TableFilters filters={filters} />
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {taskDialog && (
        <AssignTaskDialog
          onOpen={taskDialog}
          onClose={() => {
            setTaskDialog(false), setTaskData(null);
          }}
          course={taskData}
          canCreateChildTask={taskData?.content?.tool_data?.tasks.can_assign_child_task_to_parent}
        />
      )}
      {isUpdateTaskDialogOpen && selectedTask && (
        <UpdateTaskDialog
          open={isUpdateTaskDialogOpen}
          onOpenChange={() => {
            setIsUpdateTaskDialogOpen(false);
          }}
          task={selectedTask}
        />
      )}
    </>
  );
}

export default DnaList;
