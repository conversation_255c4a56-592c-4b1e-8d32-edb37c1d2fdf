import { lazy } from 'react';
import { UsersMainLayout } from './layout';
import { ProtectedRoute } from '@/components';

const UserListpage = lazy(() => import('./pages/list'));
const UserCrerationPage = lazy(() => import('./pages/create'));
const UserSinglePage = lazy(() => import('./pages/single'));

export default [
  {
    path: '',
    element: <UsersMainLayout />,

    children: [
      // Default

      {
        path: 'users',
        element: (
          <ProtectedRoute requiredPermissions={'user_index'}>
            <UserListpage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.userPage.users',
            title: 'breadcrumb.userPage.users',
          };
        },
      },
      {
        path: 'user-creation',

        element: (
          <ProtectedRoute requiredPermissions={'user_edit'}>
            <UserCrerationPage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.userPage.userCreation',
            title: 'breadcrumb.userPage.userCreation',
          };
        },
      },
      {
        path: 'users/:id',
        element: (
          <ProtectedRoute requiredPermissions={'user_show'}>
            <UserSinglePage />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.userPage.user',
          };
        },
      },
    ],
  },
];
