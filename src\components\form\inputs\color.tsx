import { Input } from '@/components/ui/input';
import { asField } from '../hocs/field';
import { Label } from '@/components/ui/label';
import { useEffect, useRef, useState } from 'react';

export const ColorInput = asField(
  ({
    name = '',
    label = '',
    value = '#000000',
    onChange = () => {},
    validatorsScroll,
    errorMessage,
    autoComplete,
    ...props
  }: any) => {
    // Refs and State
    const inputRef = useRef<HTMLInputElement>(null);
    const [color, setColor] = useState(value || '#000000');

    useEffect(() => {
      setColor(value);
    }, [value]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setColor(e.target.value);
      onChange(e.target.value);
    };

    const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
      e.preventDefault();
      const pastedText = e.clipboardData.getData('text');

      setColor(pastedText);
      onChange(pastedText);
      if (inputRef.current) {
        inputRef.current.value = pastedText;
      }
    };

    return (
      <div className="space-y-2">
        {label && (
          <div className="mb-2 block">
            <Label>{label}</Label>
          </div>
        )}
        <Input
          ref={inputRef}
          className={`${errorMessage ? 'border-red-600' : ''}`}
          type="color"
          id={name}
          value={color}
          onChange={handleChange}
          {...props}
          scrolltoerror={
            validatorsScroll &&
            errorMessage &&
            (document.getElementById(name) as any).scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        />
        <input
          type="text"
          value={color}
          onChange={handleChange}
          onPaste={handlePaste}
          className="border px-2 py-1 rounded"
        />
        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);
