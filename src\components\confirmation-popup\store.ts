import { atom } from 'jotai';
import { ConfirmationOptions } from './types';

export const DEFAULT_CONFIG: Required<ConfirmationOptions> = {
  title: 'Confirm Action',
  description: 'Are you sure you want to continue?',
  confirmLabel: 'Confirm',
  cancelLabel: 'Cancel',
  variant: 'default',
  onConfirm: () => {},
  onCancel: () => {},
  onClose: () => {},
};

export const dialogConfigAtom = atom<Required<ConfirmationOptions>>(DEFAULT_CONFIG);
export const isDialogOpenAtom = atom<boolean>(false);
