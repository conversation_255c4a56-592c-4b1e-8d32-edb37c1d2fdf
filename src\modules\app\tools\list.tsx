import { ToolCard } from './components/tool-card';
import { useTranslation } from 'react-i18next';
import { ProtectedComponent } from '@/components';

function ToolsPage() {
  const { t } = useTranslation();
  const toolPages = [
    {
      title: t('dnaCreationPage.title'),
      icon: 'ph:cube',
      route: 'dna-creation',
      permission: 'dna_create',
    },
    {
      title: t('topicCreationPage.title'),
      icon: 'vaadin:cubes',
      route: 'topic-creation',
      permission: 'topic_create',
    },
    {
      title: t('CoursePlanCreationPage.title'),
      icon: 'ph:chart-bar',
      route: 'course-plan',
      permission: 'course_create',
    },
    {
      title: t('fileAnalyzer.title'),
      icon: 'ph:file-thin',
      route: 'file-analyzer',
      permission: 'file_analyzer',
    },
    {
      title: t('contentNeed.title'),
      icon: 'carbon:need',
      route: 'content-need',
      permission: 'content_create',
    },
    {
      title: t('programCreation.title'),
      icon: 'solar:programming-broken',
      permission: 'program_plan_create',
      route: 'program-plan',
    },
    {
      title: t('contentRewrite.title'),
      icon: 'streamline:chat-bubble-square-write',
      permission: 'content_dna_reformat',
      route: 'rewrite-content',
    },
    {
      title: t('pdfsummarize.title'),
      icon: 'bi:filetype-pdf',
      permission: 'for_administration',
      route: 'pdf-summarize',
    },
  ];

  return (
    <div>
      <div className="flex flex-wrap gap-7">
        {toolPages.map((page, index) => (
          <ProtectedComponent key={index} requiredPermissions={page.permission}>
            <ToolCard key={index} title={page.title} icon={page.icon} route={page.route} />
          </ProtectedComponent>
        ))}
      </div>
    </div>
  );
}

export default ToolsPage;
