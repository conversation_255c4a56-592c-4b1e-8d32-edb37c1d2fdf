import React, { memo } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

interface ITableBulkActionsContent {
  icon: string;
  label: string;
  action: () => void;
  iconColor?: string;
}

interface ITableBulkActions {
  actions: ITableBulkActionsContent[];
}

const TableBulkActions = ({ actions = [] }: ITableBulkActions) => {
  const { t } = useTranslation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <Button variant={'outline'} className="flex gap-2 rounded-md">
          <p>{t('dnaConentPgae.bulkActions.title')}</p>
          <Icon icon="iconamoon:arrow-down-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="space-y-2">
        {actions.map((action, index) => (
          <DropdownMenuItem
            key={index}
            onClick={() => action.action()}
            className={`flex gap-3.5 cursor-pointer p-2 ${index !== actions?.length - 1 ? 'border-b' : ''}`}
          >
            <Icon icon={action.icon} width="20" className={`${action.iconColor} cursor-pointer`} />
            <p className="self-center">{action.label}</p>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default memo(TableBulkActions);
