import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { Button } from '@/components/ui/button';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Icon } from '@/index';
import ProgramPlanCourseLearningOutcomeDialog from './learning-outcome-dialog';
import { programPlanAtom } from '../../store';
import {
  useAddCourseLearningOutcome,
  useDeleteCourseLearningOutcome,
  useUpdateProgramPlanStep,
} from '../../apis/queries';

const MainGoalsStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  const [data] = useAtom(programPlanAtom);
  const [outcomeBeingEdited, setOutcomeBeingEdited] = useState({
    id: data.id,
    course_id: '',
    learning_outcome_id: '',
    outcome: '',
    learning_skill_type: '',
  });
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const { mutate, isPending } = useUpdateProgramPlanStep();
  const { mutate: add, isPending: isPendingAdd, variables: variablesAdd } = useAddCourseLearningOutcome();
  const { mutate: remove, isPending: isPendingDelete, variables: variablesDelete } = useDeleteCourseLearningOutcome();

  const handleSubmit = () => {
    mutate({ id: data.id, step: 4 }, { onSuccess: () => moveToNextStep() });
  };

  return (
    <div className="flex justify-center items-center mt-20">
      <div className="w-full max-w-[1500px]">
        <div className="bg-background  p-5 rounded-lg">
          {data.courses.map((course: any, index: any) => (
            <div className="mb-16">
              <p className="text-lg font-medium mb-3"> {course.title} </p>
              <table className="w-full bg-gray-200  rounded-lg  dark:bg-[#121317] dark:border-gray-600">
                <thead className="text-gray-600 dark:text-gray-300">
                  <tr>
                    <th className="p-4 w-[50px]">#</th>
                    <th className="p-4 text-start">{t('programPlan.steps.coursesInfo.learningOutcome')} </th>
                    <th className="p-4 text-start">{t('programPlan.steps.coursesInfo.type')} </th>
                    <th className="p-4 text-start">{t('topicContentPage.table.actions')}</th>
                  </tr>
                </thead>
                <tbody className="bg-background dark:bg-[#09090B] border ">
                  {course.learning_outcomes.map((outcome: any, index: any) => (
                    <tr key={index} className="hover:bg-gray-100 dark:hover:bg-muted transition-all border-b">
                      <td className="p-4">{index + 1}</td>
                      <td className="p-4">{outcome.outcome}</td>
                      <td className="p-4">{outcome.learning_skill_type}</td>
                      <td className="p-4 flex items-center">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                disabled={isPendingDelete && outcome.id === variablesDelete.learning_outcome_id}
                                loading={isPendingDelete && outcome.id === variablesDelete.learning_outcome_id}
                                size="icon"
                                variant="ghost"
                                onClick={() =>
                                  remove({ id: data.id, course_id: course.id, learning_outcome_id: outcome.id })
                                }
                              >
                                <Icon className="text-primary" icon="gg:trash" width={25} />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>{t('cousrePlanContentPage.delete')}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                disabled={isPendingDelete && outcome.id === variablesDelete.learning_outcome_id}
                                loading={isPendingDelete && outcome.id === variablesDelete.learning_outcome_id}
                                size="icon"
                                variant="ghost"
                                onClick={() => {
                                  setIsOpen(true);
                                  setOutcomeBeingEdited((prev: any) => {
                                    return {
                                      ...prev,
                                      course_id: course.id,
                                      learning_outcome_id: outcome.id,
                                      outcome: outcome.outcome,
                                      learning_skill_type: outcome.learning_skill_type,
                                    };
                                  }); // Edit existing goal
                                }}
                              >
                                <Icon className="text-primary" icon="basil:edit-outline" width={25} />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>{t('cousrePlanContentPage.edit')}</TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </td>
                    </tr>
                  ))}

                  <tr>
                    <td colSpan={4} className="p-4 border-b border-border">
                      <Button
                        loading={isPendingAdd && variablesAdd.course_id === course.id}
                        disabled={isPendingAdd && variablesAdd.course_id === course.id}
                        onClick={() => {
                          add({
                            course_id: course.id,
                            id: data.id,
                          });
                        }}
                        type="button"
                        className="border-primary rounded-full px-5 text-primary"
                        variant={'outline'}
                        size={'sm'}
                      >
                        + {t('CoursePlanCreationPage.addLearningOutcome')}
                      </Button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          ))}
        </div>
        <div className="flex justify-center">
          <Button loading={isPending} disabled={isPending} onClick={handleSubmit} className="mt-4">
            {t('table.pagination.next')}
          </Button>
        </div>

        {isOpen && (
          <ProgramPlanCourseLearningOutcomeDialog
            isOpen={isOpen}
            setIsOpen={() => setIsOpen(false)}
            outcomeBeingEdited={outcomeBeingEdited}
          />
        )}
      </div>
    </div>
  );
};

export default MainGoalsStep;
