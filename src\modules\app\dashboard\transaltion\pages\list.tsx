import { useFetchL<PERSON>, Icon, ProtectedComponent } from '@/index';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import NewKeyDialog from './create';
import EditKeyDialog from './edit';
import {
  useExportTranslations,
  useGetLanguages,
  useImportTranslations,
} from '@/modules/app/dashboard/transaltion/apis/queries';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { File } from 'lucide-react';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { useAdminTablesTabs } from '@/modules/app/content-tabs';
function TranslationKeysList() {
  // State
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [keyBeingEdited, setKeyBeingEdited] = useState({});
  const { mutate: exportTranslations, isPending: isExporting } = useExportTranslations();
  const { mutate: importTranslations, isPending: isImporting } = useImportTranslations();
  const [file, setFile] = useState<File | null>(null);
  const adminTableTabs = useAdminTablesTabs();

  // functions
  const handleEdit = (key: any) => {
    setKeyBeingEdited(key);
    setIsEditDialogOpen(true);
  };
  const handleFileUpload = (file: File) => {
    setFile(file);
  };
  // hooks
  const { t } = useTranslation();
  const { loading, list, count, search, filters, pagination } = useFetchList('/translations', 'translations', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
  });

  // Helpers
  const { data: languages } = useGetLanguages();
  let columns: ITableColumn<any>[] = languages?.map((lang) => ({ header: lang.name, accessorKey: lang.code })) || [];
  columns.unshift({ header: t('dashboardPage.keys.table.keys'), accessorKey: 'key' });
  columns.push({
    header: t('dashboardPage.table.actions'),
    accessorKey: 'actions',
    cell: ({ row }) => {
      return (
        <div className="flex justify-start">
          <Icon
            onClick={() => handleEdit(row)}
            icon="solar:pen-new-square-linear"
            width={23}
            className="text-primary cursor-pointer"
          />
        </div>
      );
    },
  });

  return (
    <>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={adminTableTabs} className="justify-between">
            <div className="flex gap-3 items-center">
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>

            <div className="flex items-center gap-2">
              <Popover>
                <PopoverTrigger>
                  <Button variant={'outline'} className="flex items-center gap-2">
                    <Icon icon="solar:cloud-upload-linear" width={20} />
                    {t('dashboardPage.keys.actions.Import')}
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  {file ? (
                    <div className="flex justify-between items-center flex-col gap-3 py-4">
                      <File className="h-8 w-8 mx-auto text-muted-foreground" />
                      <div className="text-lg font-semibold">{file.name}</div>
                      <div className="flex gap-2">
                        <Button
                          loading={isImporting}
                          disabled={isImporting}
                          className="px-6 h-9"
                          onClick={() => importTranslations({ excel: file }, { onSuccess: () => setFile(null) })}
                        >
                          {t('import')}
                        </Button>
                        <Button className="px-6 h-9" variant="secondary" onClick={() => setFile(null)}>
                          {t('remove')}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex justify-center gap-3 flex-col items-center text-center py-5">
                      <File className="h-8 w-8 mx-auto text-muted-foreground" />
                      <div className="space-y-2">
                        <p className="text-sm font-medium">{t('dashboardPage.keys.actions.import.clickToBrowse')}</p>
                        <p className="text-xs text-muted-foreground">Supports Excel file up to 10MB</p>
                        <label htmlFor="image-upload">
                          <Input
                            id="image-upload"
                            type="file"
                            accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                            className="hidden"
                            onChange={(e) => {
                              if (e.target.files?.[0]) {
                                handleFileUpload(e.target.files[0]);
                              }
                            }}
                          />
                          <Button
                            variant="secondary"
                            className="mx-auto mt-2"
                            size="sm"
                            type="button"
                            onClick={() => document.getElementById('image-upload')?.click()}
                          >
                            Browse Files
                          </Button>
                        </label>
                      </div>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
              <Button
                loading={isExporting}
                disabled={isExporting}
                className="flex items-center gap-2"
                variant="outline"
                onClick={() => exportTranslations()}
              >
                <Icon icon="solar:cloud-download-linear" width={20} />
                {t('export')}
              </Button>
              <ProtectedComponent requiredPermissions={'course_create'}>
                <Button onClick={() => setIsCreateDialogOpen(true)}>
                  <span className="flex items-center gap-2">
                    <Icon icon="ic:round-add" width={17} />
                    <span className="text-sm">{t('dashboardPage.keys.table.createNew')}</span>
                  </span>
                </Button>
              </ProtectedComponent>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {isCreateDialogOpen && <NewKeyDialog isOpen={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />}
      {isEditDialogOpen && (
        <EditKeyDialog isOpen={isEditDialogOpen} onOpenChange={setIsEditDialogOpen} keyBeingEdited={keyBeingEdited} />
      )}
    </>
  );
}

export default TranslationKeysList;
