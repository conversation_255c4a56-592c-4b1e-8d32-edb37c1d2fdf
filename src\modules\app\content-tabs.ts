import { user<PERSON>tom } from '@/modules/auth/store';
import { IUser } from '@/modules/auth/types';
import exp from 'constants';
import { useAtomValue } from 'jotai';

interface Tab {
  tab: string;
  route: string;
  requiredPermission: string;
}

export const useContentTablesTabs = (): Tab[] => {
  const user = useAtomValue<IUser | null>(userAtom);
  const permissions = user?.roles[0]?.permissions?.map((permission) => permission.name) || [];
  const allTabs: Tab[] = [
    { tab: 'dnaConentPgae.tabs.dnas', route: '/app/my-content/DNAs', requiredPermission: 'dna_index' },
    { tab: 'dnaConentPgae.tabs.topics', route: '/app/my-content/topics', requiredPermission: 'topic_index' },
    { tab: 'dnaConentPgae.tabs.courses', route: '/app/my-content/courses', requiredPermission: 'course_index' },
    {
      tab: 'dnaConentPgae.tabs.courseGroup',
      route: '/app/my-content/course-group',
      requiredPermission: 'course_group_index',
    },
    {
      tab: 'dnaConentPgae.tabs.content-need',
      route: '/app/my-content/content-need',
      requiredPermission: 'content_index',
    },
    {
      tab: 'dnaConentPgae.tabs.program-plan',
      route: '/app/my-content/program-plan',
      requiredPermission: 'program_plan_index',
    },
  ];

  return allTabs.filter((tab) => permissions.includes(tab.requiredPermission));
};
export const useTaskTablesTabs = (): Tab[] => {
  const user = useAtomValue<IUser | null>(userAtom);
  const permissions = user?.roles[0]?.permissions?.map((permission) => permission.name) || [];

  const tasksTabs: Tab[] = [
    { tab: 'breadcrumb.myContentPage.allTasks', route: '/app/all-tasks', requiredPermission: 'for_administration' },
    { tab: 'breadcrumb.myContentPage.myTasks', route: '/app/my-tasks', requiredPermission: 'task_index' },
  ];

  return tasksTabs.filter((tab) => permissions.includes(tab.requiredPermission));
};

export const useUsersTablesTabs = (): Tab[] => {
  const user = useAtomValue<IUser | null>(userAtom);
  const permissions = user?.roles[0]?.permissions?.map((permission) => permission.name) || [];

  const usersTabs: Tab[] = [
    { tab: 'userPage.tabs.users', route: '/app/users', requiredPermission: 'user_index' },
    { tab: 'userPage.tabs.roles', route: '/app/roles', requiredPermission: 'role_index' },
  ];
  return usersTabs.filter((tab) => permissions.includes(tab.requiredPermission));
};

export const useAdminTablesTabs = (): Tab[] => {
  const user = useAtomValue<IUser | null>(userAtom);
  const permissions = user?.roles[0]?.permissions?.map((permission) => permission.name) || [];

  const adminTabs: Tab[] = [
    { tab: 'breadcrumb.dashboard.keys', route: '/app/dashboard/keys', requiredPermission: 'for_administration' },
    {
      tab: 'breadcrumb.dashboard.metadata',
      route: '/app/dashboard/metadata',
      requiredPermission: 'for_administration',
    },
    {
      tab: 'breadcrumb.userPage.operation',
      route: '/app/dashboard/operations',
      requiredPermission: 'for_administration',
    },
    {
      tab: 'breadcrumb.userPage.modulesStatus',
      route: '/app/dashboard/modulesStatus',
      requiredPermission: 'for_administration',
    },
    {
      tab: 'breadcrumb.dashboard.systmeLogs',
      route: '/app/dashboard/systemLogs',
      requiredPermission: 'for_administration',
    },
  ];

  return adminTabs.filter((tab) => permissions.includes(tab.requiredPermission));
};
