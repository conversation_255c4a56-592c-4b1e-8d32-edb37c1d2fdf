import {
  Pagination,
  Pa<PERSON>ationContent,
  <PERSON><PERSON>ationN<PERSON><PERSON>,
  PaginationPrevious,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
} from '@/components/ui/pagination';

const generatePaginationLinks = (currentPage: number, totalPages: number, onPageChange: (page: number) => void) => {
  const pages: JSX.Element[] = [];

  if (totalPages <= 7) {
    // If total pages are 7 or less, show all page numbers
    for (let i = 1; i <= totalPages; i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink className="cursor-pointer" onClick={() => onPageChange(i)} isActive={i === currentPage}>
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
  } else {
    // Show the first two pages

    if (currentPage > 4) {
      pages.push(
        <PaginationItem>
          <PaginationLink className="cursor-pointer" onClick={() => onPageChange(1)} isActive={1 === currentPage}>
            {1}
          </PaginationLink>
        </PaginationItem>
      );
    } else {
      for (let i = 1; i <= 2; i++) {
        pages.push(
          <PaginationItem>
            <PaginationLink className="cursor-pointer" onClick={() => onPageChange(i)} isActive={i === currentPage}>
              {i}
            </PaginationLink>
          </PaginationItem>
        );
      }
    }

    // Show ellipsis if current page is further than 4 pages from the start
    if (currentPage > 4) {
      pages.push(<PaginationEllipsis key="start-ellipsis" />);
    }

    // Show the two pages before the current page, current page, and two pages after the current page
    for (let i = Math.max(3, currentPage - 2); i <= Math.min(currentPage + 2, totalPages - 2); i++) {
      pages.push(
        <PaginationItem key={i}>
          <PaginationLink className="cursor-pointer" onClick={() => onPageChange(i)} isActive={i === currentPage}>
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }

    // Show ellipsis if the current page is less than totalPages - 3
    if (currentPage < totalPages - 3) {
      pages.push(<PaginationEllipsis key="end-ellipsis" />);
    }

    // Show the last two pages

    pages.push(
      <PaginationItem key={totalPages}>
        <PaginationLink
          className="cursor-pointer"
          onClick={() => onPageChange(totalPages)}
          isActive={totalPages === currentPage}
        >
          {totalPages}
        </PaginationLink>
      </PaginationItem>
    );
  }

  return pages;
};

type PaginatorProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (pageNumber: number) => void;
  showPreviousNext: boolean;
};

export default function Paginator({ currentPage, totalPages, onPageChange, showPreviousNext }: PaginatorProps) {
  return (
    <div>
      <Pagination className="w-fit">
        <PaginationContent>
          {showPreviousNext && totalPages ? (
            <PaginationItem>
              <PaginationPrevious
                onClick={() => onPageChange(currentPage - 1)}
                className={`${currentPage - 1 < 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
              />
            </PaginationItem>
          ) : null}
          {generatePaginationLinks(currentPage, totalPages, onPageChange)}
          {showPreviousNext && totalPages ? (
            <PaginationItem>
              <PaginationNext
                onClick={() => onPageChange(currentPage + 1)}
                className={`${currentPage > totalPages - 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
              />
            </PaginationItem>
          ) : null}
        </PaginationContent>
      </Pagination>
    </div>
  );
}
