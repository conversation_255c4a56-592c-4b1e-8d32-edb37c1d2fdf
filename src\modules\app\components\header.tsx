import { userAtom } from '@/modules/auth/store';
import { useAtom } from 'jotai';
import { LangToggle } from '@/components/language-toggle';
import Usericon from '@/components/user-icon';
import { Separator } from '@/components/ui/separator';
import Notifications from './notifications/notification';

interface IProps {
  isDrawerVisible?: any;
  setIsDrawerVisible?: any;
  showMenuIcon?: any;
  showUserIcon?: any;
}

export const AppHeader = ({ isDrawerVisible, setIsDrawerVisible, showMenuIcon, showUserIcon = false }: IProps) => {
  const [user, setUser] = useAtom(userAtom);

  return (
    <nav className="px-6">
      <div className="flex flex-wrap justify-between items-center">
        <div className="flex items-center gap-4">
          <LangToggle />
          {user && <Notifications userId={user?.id} />}

          <Separator orientation="vertical" className="h-6" />
          {showUserIcon && <Usericon />}
        </div>
      </div>
    </nav>
  );
};
