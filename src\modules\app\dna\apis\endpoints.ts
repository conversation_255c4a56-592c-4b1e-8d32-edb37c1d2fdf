import {
  DnaCreation,
  DnaCreationData,
  IAnalysis,
  IDNA,
  IDnaSlide,
  IDnaStatus,
  ILogs,
  ISoundType,
  IStoryBoard,
  MultiActivity,
} from '../types';
import { Api, DELETE_DNA, GENERATE_DNA, GENERATE_DNA_FORM_TOPIC, STATISTICS_DNA, UPLOAD_DNA_SOURCE } from '@/services';
import { CHANGE_STATUS_DNA } from '@/services';
import { ILanguage } from '../../dashboard/transaltion/types';
import _ from 'lodash';

export const getDnaById = async (id?: number | string): Promise<IDNA> => {
  const { data } = await Api.get(`dna/get-dna/${id}`);
  return data.data || {};
};

export const getDnaTitleById = async (id?: number): Promise<IDNA> => {
  const { data } = await Api.get(`dna/get-dna-title/${id}`);
  return data || {};
};

export const getDnaStatistics = async () => {
  const { data } = await Api.get(STATISTICS_DNA);
  return data.data;
};

export const generateDnaData = async (form: DnaCreationData) => {
  const { data } = await Api.post(GENERATE_DNA, form);
  return data.data;
};
export const getDnaLogs = async (model: string, id: number): Promise<ILogs[]> => {
  const { data } = await Api.get(`/logs/${model}/${id}`);
  return data.data;
};

export const generateDna = async (form: DnaCreation | { id: number }) => {
  const { data } = await Api.put(GENERATE_DNA_FORM_TOPIC, form);
  return data.data;
};

export const generateDnaContent = async (id: number | string) => {
  const { data } = await Api.put('/dna/generate-dna-content', { id });
  return data.data;
};

export const saveDNA = async ({ id, dna }: { id: number; dna: string }) => {
  const { data } = await Api.put('dna/update-dna', { id, dna });
  return data.data;
};

export const generateMultiActivite = async (form: MultiActivity) => {
  const { data } = await Api.post('dna/multi-activity-dna', form);
  return data.data;
};

export const generateMagicDna = async ({ id, comment }: { id: number; comment: string }) => {
  const { data } = await Api.post(`/dna/ai-magic-edit/${id}`, { comment, _method: 'put' });
  return data.data;
};

export const uploadDnaFile = async (file: string) => {
  const formData = new FormData();
  formData.append('dna', file);
  const { data } = await Api.post(UPLOAD_DNA_SOURCE, formData);
  return data.data;
};

export const updateDnaStatus = async (payload: { id: string | number; status: IDnaStatus; report_reason?: string }) => {
  const { data } = await Api.post(`${CHANGE_STATUS_DNA}`, payload);
  return data;
};

export const changeDnaStatus = async (id: string | number) => {
  const { data } = await Api.post(`/dna/ready-for-review/${id}`);
  return data;
};

export const updateDnaBloomTax = async ({
  id,
  payload,
}: {
  id: number | undefined;
  payload: { bloom_tax_id?: number; title?: string; learning_objectives?: string };
}) => {
  const { data } = await Api.put(`/dna/update-dna-data/${id}`, payload);
  return data;
};

export const deleteDna = async (id: number) => {
  const { data } = await Api.delete(`${DELETE_DNA}/${id}`);
  return data.data;
};

export const deleteDnas = async (ids: number[]) => {
  const { data } = await Api.delete(`/dna/dnas/delete`, {
    ids,
  });
  return data.data;
};

export const reformatDna = async ({ id, payload }: { id: number | string; payload: any }) => {
  const { data } = await Api.post(`/reformat/dna/${id}`, payload);
  return data.data;
};

export const getDnaTranslations = async (id: number) => {
  const { data } = await Api.get(`/reviewer/tasks/dna/content-translation/${id}`);
  return data.data;
};

export const generateDnaTranslations = async ({
  id,
  taskId,
  language_id,
}: {
  id: number;
  taskId: string;
  language_id: number;
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${taskId}/dna/content-translation/${id}`, {
    language_id,
  });
  return data.data;
};

export const deleteTransaltion = async ({
  id,
  code,
  translation_ids,
}: {
  id: number;
  code: string;
  translation_ids: number[];
}) => {
  const { data } = await Api.post(`/reviewer/tasks/${code}/dna/content-translation/${id}`, {
    translation_ids,
    _method: 'delete',
  });
  return data.data;
};

export const updateTransaltion = async ({
  id,
  code,
  translation_id,
  title,
  content,
  learning_objective,
}: {
  id: number;
  code: string;
  translation_id: number;
  title: string;
  content: string;
  learning_objective: string;
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/dna/content-translation/${id}`, {
    translation_id,
    title,
    content,
    learning_objective,
    _method: 'put',
  });
  return data.data;
};

export const updateTransaltionStatus = async ({
  id,
  code,
  translation_id,
  status,
  content,
  title,
  learning_objective,
}: {
  id: number;
  code: string;
  translation_id: number;
  status: string;
  content: string;
  title: string;
  learning_objective: string;
}) => {
  const { data } = await Api.put(`/reviewer/tasks/${code}/dna/content-translation/${id}/change-status`, {
    translation_id,
    status,
    content,
    title,
    learning_objective,
    _method: 'put',
  });
  return data.data;
};

export const approveOrFeedbackDna = async ({ id, status }: { id: number; status: string }) => {
  const { data } = await Api.put(`/dna/approve-or-feedback/${id}`, { status });
  return data.data;
};

export const reorderDna = async ({
  id,
  new_topic_id,
  next_dna_id,
}: {
  id: number;
  new_topic_id: number;
  next_dna_id: number;
}) => {
  const { data } = await Api.put(`/dna/${id}/move-dna-to-topic`, { new_topic_id, next_dna_id });
  return data.data;
};

export const getDnaContributors = async (dnaId: string) => {
  const { data } = await Api.get(`/dna/get-dna/${dnaId}/contributors`);
  return data.data;
};

export const confirmDnaGeneratedByAiMagic = async ({ id, payload }: { id: number | string; payload: any }) => {
  const { data } = await Api.put(`/dna/confirm-ai-magic-edit/${id}`, payload);
  return data.data;
};

export const generateArticulateAnalysis = async ({
  id,
  model,
  language_id,
}: {
  id: number | string;
  model: string;
  language_id: string;
}) => {
  const { data } = await Api.post(`/dna/analysis/${id}`, {
    model,
    language_id,
  });
  return data.data;
};

export const getArticulateAnalysis = async (
  id: number | string | undefined
): Promise<{
  analysis: IAnalysis;
  storyboard: IStoryBoard[];
}> => {
  const { data } = await Api.get(`/dna/analysis/${id}`);
  return data.data;
};

// slideshow ---- // slideshow // slideshow ---- // slideshow // slideshow ---- // slideshow

export const getSlideshowPlay = async (id: number | string): Promise<{ items: any; additional_data: ILanguage[] }> => {
  const { data } = await Api.get(`/dna/slideshow/play/${id}`);
  return data.data;
};

export const generateDnaSlideshow = async ({
  id,
  slideshow_sound_type,
  slides_count,
  language_id,
  title,
  model,
}: {
  id: number;
  slideshow_sound_type: ISoundType;
  slides_count?: number;
  language_id?: number;
  title?: string;
  model?: string;
}) => {
  const { data } = await Api.post(`dna/slideshow/${id}`, {
    title,
    slideshow_sound_type,
    slides_count,
    language_id,
    model,
  });
  return data.data;
};

export const reGenerateDnaSlideshow = async ({
  id,
  slideshow_sound_type,
  slideshow_id,
}: {
  id: number;
  slideshow_sound_type?: ISoundType;
  slideshow_id: number;
}) => {
  const { data } = await Api.post(`dna/slideshow/${id}/regenerate`, {
    slideshow_sound_type,
    slideshow_id,
  });
  return data.data;
};

export const updateSlideContent = async ({
  dnaId,
  payload,
}: {
  dnaId: number;
  payload: { slideshow_id: number; slide_id: number; content: string };
}) => {
  const { data } = await Api.put(`/dna/slideshow/${dnaId}/slides/update-content`, { ...payload });
  return data.data;
};

export const regenerateSlide = async ({
  dnaId,
  slideshow_id,
  slide_id,
  prompt = '',
}: {
  dnaId: string | undefined;
  slideshow_id: number;
  slide_id: number;
  prompt?: string;
}) => {
  const { data } = await Api.put(`/dna/slideshow/${dnaId}/slides/regenerate-slide`, {
    slideshow_id,
    slide_id,
    prompt,
  });
  return data.data;
};

export const changeSlideTemplate = async ({
  dnaId,
  slideshow_id,
  slide_id,
  template,
}: {
  dnaId: string | undefined;
  slideshow_id: number;
  slide_id: number;
  template: string;
}) => {
  const { data } = await Api.put(`/dna/slideshow/${dnaId}/slides/change-slide-template`, {
    slideshow_id,
    slide_id,
    template,
    _method: 'put',
  });
  return data.data;
};

export const reorderSlide = async ({
  dnaId,
  slideshow_id,
  slide_id,
  next_slide_id,
}: {
  dnaId: number;
  slideshow_id: number;
  slide_id: number;
  next_slide_id: number;
}) => {
  const { data } = await Api.put(`/dna/slideshow/${dnaId}/slides/reorder-slide`, {
    slideshow_id,
    slide_id,
    next_slide_id,
  });
  return data.data;
};

export const generateVoiceOverSample = async ({
  voice_over,
  voice_over_sound,
}: {
  voice_over: string;
  voice_over_sound: string;
}) => {
  const { data } = await Api.post(`/dna/slideshow/slides/get-audio`, {
    voice_over,
    voice_over_sound,
  });
  const base64Audio = data.data.audio;
  const url = `data:audio/mpeg;base64,${base64Audio}`;
  return url;
};

export const updateVoiceOverTextAndAudio = async ({
  dnaId,
  slideshow_id,
  slide_id,
  voice_over,
  voice_over_sound,
  audio,
}: {
  slideshow_id: number;
  slide_id: number;
  dnaId: string | number | undefined;
  voice_over: string;
  voice_over_sound: string;
  audio: string;
}) => {
  const { data } = await Api.post(`/dna/slideshow/${dnaId}/slides/save-text-and-audio`, {
    voice_over,
    voice_over_sound,
    audio,
    slide_id,
    slideshow_id,
  });
  return data.data;
};

export const updateAnalysis = async ({
  dna_id,
  content,
  storyboard_id,
}: {
  dna_id: number;
  content: string;
  storyboard_id: number;
}) => {
  const { data } = await Api.put(`/dna/analysis/${dna_id}`, { content, storyboard_id, _method: 'put' });
  return data?.data || [];
};
