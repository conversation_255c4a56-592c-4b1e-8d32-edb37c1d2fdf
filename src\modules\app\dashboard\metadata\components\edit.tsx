import React, { useEffect } from 'react';
import { Form, Modal, useForm, TextInput, useValidate, Textarea, DnaEnums } from '@/index';
import { useUpdateMetadata } from '@/modules/app/dashboard/metadata/apis/queries'; // Use a mutation hook to update metadata
import { Button } from '@/components/ui/button';

import { useTranslation } from 'react-i18next';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useGetLanguages } from '@/modules/app/dashboard/transaltion/apis/queries';
interface IProps {
  isOpen: boolean;
  onOpenChange: any;
  metadata: any;
  refresh: any;
}

const EditMetadataDialog = ({ isOpen, onOpenChange, metadata, refresh }: IProps) => {
  const { data: languages } = useGetLanguages();
  const { mutate: updateMetadata, isPending } = useUpdateMetadata(); // Hook for updating metadata
  const { t } = useTranslation();
  const { isRequired } = useValidate();

  // Initialize the form with the existing metadata
  const initialFormState = languages?.reduce(
    (acc: any, lang: any) => {
      acc[lang.code] = metadata ? metadata[`name_${lang.code}`] : ''; // Load translations from existing metadata
      return acc;
    },
    { type: metadata?.type || DnaEnums.metadata[0].value, description: metadata?.description || '' }
  );

  const { form, setFieldValue, setFormValue } = useForm(initialFormState || {});

  // Reset form when the dialog is closed or metadata changes
  useEffect(() => {
    if (metadata && isOpen) {
      setFormValue(initialFormState);
    }
  }, [metadata, isOpen, languages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const payload: any = {
      type: form.type,
      description: form.description, // English description
    };

    languages?.forEach((lang: any) => {
      payload[`name_${lang.code}`] = form[lang.code];
    });

    updateMetadata(
      { id: metadata.id, data: payload },
      {
        onSuccess: () => {
          refresh();
          onOpenChange(false); // Close the dialog after successful update
        },
      }
    );
  };

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('metadataPage.editMetadataDialog.title')}>
      <Form onSubmit={handleSubmit} className="space-y-4">
        {/* Dropdown for type */}
        <ComboboxInput
          placeholder={t('selectType')}
          label={t('type')}
          options={DnaEnums.metadata}
          value={form.type}
          onChange={setFieldValue('type')}
          dropIcon
          validators={[isRequired()]}
        />

        {/* Title translations for all available languages */}
        {languages?.map((lang) => (
          <TextInput
            key={lang.id}
            name={lang.code}
            label={`${t('metadataPage.title')} (${lang.name})`}
            value={form[lang.code]}
            onChange={setFieldValue(lang.code)}
            validators={[isRequired()]}
          />
        ))}

        {/* Description */}
        <Textarea
          rows={5}
          label={t('metadataPage.description')}
          value={form.description}
          onChange={setFieldValue('description')}
          className="w-full p-2 border rounded"
          validators={[isRequired()]}
        />

        {/* Form buttons */}
        <div className="flex justify-end gap-3 mt-3">
          <Button onClick={() => onOpenChange(false)} variant={'outline'} className="min-w-[100px] mt-4">
            {t('cancel')}
          </Button>
          <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
            {t('save')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default EditMetadataDialog;
