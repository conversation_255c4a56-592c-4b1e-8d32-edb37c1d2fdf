import { ProtectedComponent } from '@/components';
import { GET_DNA_LIST, useFetchList, Icon, useNotify, useConfirmDialog } from '@/index';
import { useState, useMemo } from 'react';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
  TableBulkActions,
} from '@/components/theTable';
import {
  handelExportToExcel,
  convertStatusToFilterEnumByTitle,
  localizePresenter,
  getContentStatusStyle,
  formatUserByNameAndEmail,
  dateAndTimeFormat,
} from '@/utils/helpers';
import { useHasPermission } from '@/modules/auth/store';
import { generateEnum } from '@/utils/helpers';
import { useDeleteDna, useDeleteDnas, useGenarateDna } from '@/modules/app/dna/apis/queries';
import { IDNAListItem } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { ITableColumn } from '@/components/theTable/types';
import { Button } from '@/components/ui/button';
import LanguageLink from '@/components/language-link';
import EditDna from '../../components/edit-dna';
import { useGetModulesStatus } from '../../dashboard/modules-status/apis/queries';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';
import { useContentTablesTabs } from '../../content-tabs';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import { useGetSingleMetadata } from '../../dashboard/metadata/apis/queries';

function DnaList() {
  //  State
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [editDialog, setEditDialog] = useState<boolean>(false);
  const [selectedDna, setSelectedDna] = useState<any>(null);

  //  Hooks
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const navigate = useLanguageNavigate();
  const { t, i18n } = useTranslation();
  const { mutate: deleteDna, isPending: asdsa } = useDeleteDna();
  const { mutate: generate, isPending: isGeneratingDNA, variables } = useGenarateDna();
  const contentTablesTabs = useContentTablesTabs();

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { data: modulesStatus } = useGetModulesStatus();

  const { data: allmetaData } = useGetSingleMetadata('');
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];

  // Initialize the observer to keep the atom in sync
  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'DNA', i18n.language);

  const { loading, list, count, refresh, search, filters, pagination } = useFetchList(GET_DNA_LIST, 'dnas', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      status_id: {
        dynamicEnum: statusFilter,
        placeholder: 'dnaConentPgae.statusFilter',
      },
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'dnaConentPgae.userFIlter',
            },
          }
        : {}),
      language_id: {
        dynamicEnum: generateEnum(languageOptions, 'id', labelKey),
        placeholder: 'dnaConentPgae.languageFilter',
      },
    },
  });
  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeleteDna = async (dnaId: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        try {
          hideConfirm();
          deleteDna(dnaId);
          notify.success('DNA deleted successfully');
        } catch (error) {
          notify.error('An error occurred. Please try again.');
        }
      },
      loading: asdsa,
    });
  };

  const ExcelConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'vscode-icons:file-type-excel'} width={55} />
        </div>
        <p>{t('dnaConentPgae.bulkActions.exportExcelText')}</p>
      </div>
    );
  };

  const handleExport = () => {
    showConfirm(ExcelConfirmText(), {
      onConfirm() {
        hideConfirm();
        // Extract ids from selectedRows
        const ids = selectedRows.map((row: any) => row.id);
        handelExportToExcel(ids, list);
      },
    });
  };

  const ConfirmActivityText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>{t('dnaConentPgae.bulkActions.createActiviteText')}</p>
      </div>
    );
  };

  const ConfirmDeleteText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>Please confirm that you want to delete the selected DNAs.</p>
      </div>
    );
  };
  const { mutate: deleteDnas } = useDeleteDnas();

  const handleDeleteAllDnas = () => {
    const ids = selectedRows.map((row: any) => row.id);
    deleteDnas(ids, {
      onSuccess: () => {
        refresh();
        hideConfirm();
      },
    });
  };
  const showDeleteAllConfirm = async () => {
    showConfirm(ConfirmDeleteText(), { onConfirm: handleDeleteAllDnas });
  };

  // Computed values
  const bulkActions = useMemo(() => {
    return [
      {
        icon: 'vscode-icons:file-type-excel',
        iconColor: 'text-green-500',
        label: t('dnaConentPgae.bulkActions.exportExcel'),
        action: handleExport,
      },
      // {
      //   icon: 'solar:clipboard-add-outline',
      //   iconColor: 'text-blue-500',
      //   label: 'dnaConentPgae.bulkActions.createActivite',
      //   action: handleActivity,
      // },

      {
        icon: 'gg:trash',
        iconColor: 'text-[#f05252]',
        label: 'Delete',
        action: showDeleteAllConfirm,
      },
    ];
  }, [selectedRows]);

  const columns: ITableColumn<IDNAListItem>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '180px',
        cell: ({ row }) => getContentStatusStyle(row?.dna_status),
      },
      {
        accessorKey: 'title',
        header: t('dnaConentPgae.table.dnaTitle'),
        width: '250px',
        cell: ({ row }) => {
          const element = !row.dna_content ? (
            <span>{row.title}</span>
          ) : (
            <p
              className="text-primary underline cursor-pointer"
              onClick={() => navigate(`/app/my-content/DNAs/${row.id}`)}
            >
              {row.title}
            </p>
          );
          return <div className={`break-words rtl:text-right`}>{element}</div>;
        },
      },
      {
        accessorKey: 'course',
        header: t('breadcrumb.myContentPage.allTasks.course'),
        width: '180px',
      },
      {
        accessorKey: 'subject',
        header: t('dnaConentPgae.table.subject'),
        width: '100px',
        cell: ({ row }) => localizePresenter(row.subject, i18n.language),
      },
      {
        accessorKey: 'bloom_tax',
        header: t('dnaConentPgae.table.level'),
        width: '130px',
        cell: ({ row }) => localizePresenter(row.bloom_tax, i18n.language),
      },
      {
        accessorKey: 'audio_length',
        header: t('dnaConentPgae.table.length'),
        width: '100px',
      },
      {
        accessorKey: 'language',
        header: t('CoursePlanCreationPage.form.language'),
        width: '100px',
        cell: ({ row }) => localizePresenter(row.language, i18n.language),
      },
      {
        accessorKey: 'created_at',
        header: t('dnaConentPgae.table.date'),
        width: '120px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'user',
        header: t('dnaConentPgae.table.author'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.user);
        },
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '180px',
        cell: ({ row }) => {
          return (
            <div className="flex gap-2 justify-end items-center">
              <ConditionalComponent status={row.dna_status} wantedStatus={StatusClass.DNA.EDIT.NO_CONTENT}>
                <ProtectedComponent requiredPermissions={'dna_create'}>
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        {row.id === variables && isGeneratingDNA ? (
                          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary" width={25} />
                        ) : (
                          <Button
                            onClick={() => {
                              generate(row.id);
                            }}
                            type="button"
                            className="flex gap-2 items-center"
                          >
                            <Icon icon="mage:stars-a-fill" width={22} className="cursor-pointer" />
                            {t('generate')}
                          </Button>
                        )}
                      </TooltipTrigger>
                      <TooltipContent>{t('generate')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </ProtectedComponent>
              </ConditionalComponent>

              <div className="flex flex-row-reverse gap-2 items-center">
                <ConditionalComponent
                  status={row.course_status}
                  operator="not"
                  wantedStatus={StatusClass.COURSE.EDIT.RFR}
                >
                  <ConditionalComponent
                    status={row.dna_status}
                    wantedStatus={[
                      StatusClass.DNA.EDIT.NO_CONTENT,
                      StatusClass.DNA.EDIT.DRAFT,
                      StatusClass.DNA.EDIT.FEEDBACK,
                      StatusClass.DNA.EDIT.RFR,
                    ]}
                  >
                    <ProtectedComponent requiredPermissions={'dna_delete'}>
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Icon
                              onClick={() => handleDeleteDna(row.id)}
                              icon="gg:trash"
                              width="25"
                              className="text-red-500 cursor-pointer"
                            />
                          </TooltipTrigger>
                          <TooltipContent>{t('delete')}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </ProtectedComponent>
                  </ConditionalComponent>
                  <ConditionalComponent
                    status={row.dna_status}
                    wantedStatus={[StatusClass.DNA.EDIT.DRAFT, StatusClass.DNA.EDIT.FEEDBACK, StatusClass.DNA.EDIT.RFR]}
                  >
                    <ProtectedComponent requiredPermissions={'dna_edit'}>
                      <TooltipProvider delayDuration={100}>
                        <Tooltip>
                          <TooltipTrigger>
                            <Icon
                              onClick={() => {
                                setEditDialog(true), setSelectedDna(row);
                              }}
                              icon="basil:edit-outline"
                              width={25}
                              className="text-primary cursor-pointer"
                            />
                          </TooltipTrigger>
                          <TooltipContent>{t('edit')}</TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </ProtectedComponent>
                  </ConditionalComponent>
                </ConditionalComponent>
              </div>
            </div>
          );
        },
      },
    ];
  }, [variables, isLoading, selectedRows]);

  return (
    <>
      <Table
        rows={list}
        columns={columns}
        loading={loading}
        multipleSelect={{
          selectedRows,
          setSelectedRows,
        }}
      >
        <TableContent>
          <TableContentHeader tabs={contentTablesTabs}>
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('search.dnaList')} />
              <TableFilters filters={filters} />
            </div>
            <div className="ms-auto flex items-center gap-3">
              <ProtectedComponent requiredPermissions={'dna_create'}>
                <LanguageLink to={'/app/tools/dna-creation'}>
                  <Button>
                    <span className="flex items-center gap-2">
                      <Icon icon="ic:round-add" width={17} />
                      <span className="text-sm">{t('dnaCreationPage.form.CreateNewDna')}</span>
                    </span>
                  </Button>
                </LanguageLink>
              </ProtectedComponent>
              <div>{selectedRows.length > 0 && <TableBulkActions actions={bulkActions} />}</div>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
      {editDialog && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false), setSelectedDna(null);
          }}
          data={selectedDna}
        />
      )}
    </>
  );
}

export default DnaList;
