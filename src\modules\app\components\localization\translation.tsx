import { IDNA } from '@/modules/app/dna/types';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import React, { useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Combobox } from '@/components/combo-box';
import { generateEnum } from '@/utils/helpers';
import { useForm, useValidate } from '@/hooks';
import { Button } from '@/components/ui/button';
import { useGenerateDnaTranslations, useGetDnaTranslations } from '@/modules/app/dna/apis/queries';
import { ComboboxInput, Form, ProtectedComponent } from '@/index';
import EmptyLocalization from './empty-state';
import TranslationTable from './transaltion-table';
import { useParams } from 'react-router-dom';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';
import { ConditionalComponent } from '@/components/conditional-status-component';
import StatusClass from '@/utils/SystemStatusEnums';

const TranslationTab = ({ details }: { details: IDNA | any }) => {
  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { taskId } = useParams();
  const { isRequired, validateUrlRegex } = useValidate();

  const { form, setFieldValue } = useForm({
    id: details?.id,
    language_id: '',
    taskId: taskId,
  });

  useEffect(() => {
    if (taskId) {
      setFieldValue('taskId')(taskId);
    }
    if (details?.id) {
      setFieldValue('id')(details?.id);
    }
  }, [taskId, details]);

  const { data } = useGetDnaTranslations(details?.id);
  const { mutate: generateTranslation, isPending } = useGenerateDnaTranslations();

  const handleSubmit = () => {
    generateTranslation(form);
  };

  if (!details?.content_translations?.length) {
    return <EmptyLocalization contentId={details?.id} />;
  }

  return (
    <div className="space-y-4">
      <ConditionalComponent
        status={details?.dna_status}
        wantedStatus={[StatusClass.DNA.REVIEW.PRODUCTION]}
        operator="not"
      >
        <ProtectedTaskComponent requiredPermissions={'translation_create'}>
          <Form onSubmit={handleSubmit}>
            <div className="flex gap-3">
              <div className="w-[400px]">
                <ComboboxInput
                  placeholder={t('dnaCreationPage.form.language')}
                  options={generateEnum(data || [], 'id', labelKey)}
                  value={form.language_id}
                  onChange={setFieldValue('language_id')}
                  validators={[isRequired()]}
                />
              </div>
              <div>
                <div className="flex mt-2">
                  <Button
                    variant={'ghost'}
                    loading={isPending}
                    disabled={isPending}
                    className="min-w-[100px] border-primary border-2 flex gap-1 items-center"
                    type="submit"
                  >
                    {t('dnaCreationPage.translate')}
                  </Button>
                </div>
              </div>
            </div>
          </Form>
        </ProtectedTaskComponent>
      </ConditionalComponent>

      {details?.content_translations?.length > 0 && (
        <ProtectedTaskComponent requiredPermissions={'translation_show'}>
          <TranslationTable details={details} />
        </ProtectedTaskComponent>
      )}
    </div>
  );
};

export default TranslationTab;
