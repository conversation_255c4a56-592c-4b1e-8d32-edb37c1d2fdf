import React, { memo } from 'react';
import { SelectContent, SelectItem, SelectTrigger, SelectValue, Select } from '../ui/select';
import { useTranslation } from 'react-i18next';
import Pagenator from '../pagination';

interface TablePaginationProps {
  pagination: any;
  count: number;
}

const TablePagination: React.FC<TablePaginationProps> = ({
  pagination = {
    page: 1,
    size: 20,
  },
  count = 0,
}) => {
  // hooks
  const { t } = useTranslation();

  // Computed
  const { page_num, page_size } = pagination;
  const pagesCount = Math.max(Math.ceil(count / page_size), 1);
  const showingText = `${count ? page_num * page_size - page_size + 1 : count} - ${
    page_num * page_size > count ? count : page_num * page_size
  }`;
  const isPaginationActive = !!pagination.update;
  const paginationLimit = 10;
  return (
    <div className="overflow-y-auto">
      {/* Pagination  */}
      {isPaginationActive && (
        <nav className="flex flex-row justify-between items-center space-y-0 p-4" aria-label="Table navigation">
          <div className="flex gap-5 items-center">
            <span className="text-sm font-normal text-gray-500 dark:text-gray-400 flex gap-2">
              <span className="font-semibold text-gray-900 dark:text-white text-nowrap">{showingText}</span>{' '}
              {t('table.pagination.of')} <span className="font-semibold text-gray-900 dark:text-white">{count}</span>
            </span>

            <Select onValueChange={(value) => pagination.update({ page_size: value })} defaultValue={'30'}>
              <SelectTrigger className="px-3 flex gap-2 h-9">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {[30, 50, 100].map((item) => (
                  <SelectItem key={item} value={JSON.stringify(item)}>
                    {item}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {count > paginationLimit && (
            <Pagenator
              totalPages={pagesCount}
              showPreviousNext
              currentPage={page_num}
              onPageChange={(page_num) => pagination.update({ page_num })}
            />
          )}
        </nav>
      )}
    </div>
  );
};

export default memo(TablePagination);
