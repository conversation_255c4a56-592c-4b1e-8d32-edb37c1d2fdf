import { Form, TextInput, useForm, useValidate } from '@/index';
import { MultiSelect } from '@/components/form/inputs/multi-select-combo-box';
import { Button } from '@/components/ui/button';
import { useGetPermissions, useGetRoleById, useUpdateRole } from '../apis/queries';
import { useParams } from 'react-router-dom';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

const DnaCreation = () => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { mutate: updateRole, isPending } = useUpdateRole();
  const { data } = useGetPermissions();
  const { t } = useTranslation();
  const { id } = useParams<{ id: string | any }>();
  const { data: selectedRole } = useGetRoleById(id);
  const permissionList = data?.map((permission) => ({ value: permission.name, label: permission.name }));

  useEffect(() => {
    if (selectedRole) {
      setFieldValue('name')(selectedRole.name);
      setFieldValue('permissions')(selectedRole.permissions.map((permission: any) => permission.name));
    }
  }, [selectedRole?.id]);
  // State
  //Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    permissions: [],
  });

  // Functions
  const handleGenerate = async () => {
    updateRole({ id, payload: form });
  };
  return (
    <div>
      <Form onSubmit={handleGenerate}>
        <div className="justify-between">
          <div className="lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name"
              label={t('name')}
              placeholder={t('userPage.roleName')}
              value={form.name}
              onChange={setFieldValue('name')}
              isRequired
              validators={[isRequired()]}
            />
            <MultiSelect
              name="permissions"
              label={t('userPage.permissions')}
              key={selectedRole?.id}
              options={permissionList || []}
              onChange={setFieldValue('permissions')}
              value={form.permissions}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={50}
              validators={[isNotEmptyArray()]}
              disabled={data?.length === 27 && form.name === 'super_admin'}
            />
          </div>
          <div className="flex items-start gap-2 mt-[16px] justify-end">
            {form.name !== 'super_admin' && (
              <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
                {t('save')}
              </Button>
            )}
            {/* <Button disabled={isPending} className=" mt-4" variant={'outline'} onClick={handleClear}>
              Clear
            </Button> */}
          </div>
        </div>
      </Form>
    </div>
  );
};

export default DnaCreation;
