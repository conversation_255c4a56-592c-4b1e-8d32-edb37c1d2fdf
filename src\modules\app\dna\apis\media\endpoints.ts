import { IGetMediaResponse, IMedia } from '@/modules/app/common/types';
import { Api } from '@/services';
import { api } from '@/services/axios';

export const getMedia = async (id: string | number, modle: string): Promise<IGetMediaResponse> => {
  const { data } = await Api.get(`/media-manager/list?modle=${modle}&id=${id}`);
  return data?.data || [];
};

export const uploadImage = async ({
  dna_id,
  url,
  file,
  media_name,
  audience_id,
  language_id,
  subject_id,
  prompt,
  drawing_style_id,
  quality,
  dimensions,
  model,
}: {
  dna_id: string;
  url?: string;
  file?: File;
  media_name: string;
  audience_id?: string;
  language_id?: string;
  subject_id?: string;
  prompt?: string;
  drawing_style_id?: number | string;
  quality?: number | string;
  dimensions?: string;
  model?: string;
}): Promise<string> => {
  const { data } = await api.post(
    `/media-manager/dna/upload`,
    {
      dna_id,
      file,
      url,
      media_name,
      properties: { audience_id, language_id, subject_id, prompt, drawing_style_id, quality, dimensions, model },
    },
    { headers: { 'Content-Type': 'multipart/form-data' } }
  );
  return data?.data;
};

export const replaceImage = async ({
  dna_id,
  url,
  new_file,
  media_uuid,
  prompt,
  model,
  drawing_style_id,
  dimensions,
  quality,
  language_id,
  subject_id,
  audience_id,
  media_name,
}: {
  dna_id: string | number;
  url?: string;
  new_file?: File;
  media_uuid: string;
  prompt?: string;
  model?: string;
  media_name?: string;
  audience_id?: string;
  language_id?: string;
  subject_id?: string;
  drawing_style_id: number | string;
  dimensions: string;
  quality: number | string;
}): Promise<string> => {
  let payload: any = {
    dna_id,
    new_file,
    url,
    media_uuid,
    media_name,
  };
  if (prompt) {
    payload = {
      ...payload,
      properties: {
        prompt,
        drawing_style_id,
        quality,
        dimensions,
        model,
        audience_id,
        language_id,
        subject_id,
      },
    };
  }
  const { data } = await api.post(`/media-manager/dna/replacement`, payload, {
    headers: { 'Content-Type': 'multipart/form-data' },
  });
  return data?.data;
};

export const deleteMedia = async ({
  dna_id,
  media_name,
  media_uuid,
}: {
  dna_id: number;
  media_name: string;
  media_uuid: string;
}) => {
  const { data } = await Api.post(`/media-manager/dna/delete`, { dna_id, media_name, media_uuid, _method: 'delete' });
  return data?.data || [];
};

export const generateImage = async ({
  prompt,
  drawing_style_id,
  quality,
  dimensions,
  model,
}: {
  prompt: string;
  drawing_style_id?: number | string;
  quality?: number | string;
  dimensions?: string;
  model?: string;
}): Promise<string> => {
  const { data } = await Api.post(`/generate-ai-image`, { prompt, drawing_style_id, quality, dimensions, model });
  return data?.data?.image_url;
};

export const editImage = async ({
  prompt,
  drawing_style_id,
  image_url,
  image_uuid,
}: {
  prompt: string;
  drawing_style_id?: number | string;
  image_url?: string;
  image_uuid?: string;
}): Promise<string> => {
  const { data } = await Api.post(`/enhance-ai-image`, { prompt, drawing_style_id, image_url, image_uuid });
  return data?.data?.image_url;
};

export const confrimAImage = async ({
  id,
  storyboard_id,
  image_url,
}: {
  id: number;
  storyboard_id: number;
  image_url: string;
}): Promise<string> => {
  const { data } = await Api.post(`/dna/analysis/${id}/upload-image`, { storyboard_id, image_url });
  return data?.data?.image_url;
};

export const renameImage = async ({
  dna_id,
  media_name,
  media_uuid,
}: {
  dna_id: number;
  media_name: string;
  media_uuid: string;
}) => {
  const { data } = await Api.put(`/media-manager/dna/update`, { dna_id, media_name, media_uuid, _method: 'put' });
  return data?.data || [];
};
