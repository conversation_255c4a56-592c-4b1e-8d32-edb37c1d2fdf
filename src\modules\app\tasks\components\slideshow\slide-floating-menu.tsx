import { Icon } from '@/components';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { useChangeSlideTemplate, useDeleteSlide, useRegenerateSlide } from '../../apis/queries';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { SLIDE_TEMPLATES } from './sildeshow-helpers';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import VoiceOverDialog from './voice-over-edit';
import { t } from 'i18next';
import { ISlide } from '@/modules/app/dna/apis/slideshow/types';
import { useParams } from 'react-router-dom';
import { ProtectedTaskComponent } from '../protected-task-component';
const SlideEdit = ({
  slideshowId,
  dnaId,
  slide,
  template,
}: {
  slideshowId: number;
  dnaId: string | any;
  slide: ISlide;
  template: string;
}) => {
  const [vioceOver, setVioceOver] = useState(false);
  const [isVoiceOverDialogOpen, setIsVoiceOverDialogOpen] = useState(false);
  const { mutate: deleteSlide, isPending } = useDeleteSlide();
  const { mutate: regenerateSlide, isPending: isRegenerating } = useRegenerateSlide();
  const { mutate: changeTemplate, isPending: isChangingTemplate } = useChangeSlideTemplate();
  const { confirm } = useConfirmation();
  const selectedTemplate = SLIDE_TEMPLATES.find((t) => t.value === template);
  const { taskId } = useParams();
  const handleRegenerate = () => {
    regenerateSlide({
      taskCode: taskId || '',
      dnaId,
      slideshow_id: slideshowId,
      slide_id: slide.id,
      prompt: '',
    });
  };

  // function to handle template change after confirming the action
  const handleTemplateChange = (template: string) => {
    confirm({
      variant: 'info',
      title: t('slideshow.changeTemplate.title'),
      description: t('slideshow.changeTemplate.description'),
      onConfirm: () => {
        changeTemplate({
          taskCode: taskId || '',
          dnaId,
          slideshow_id: slideshowId,
          slide_id: slide.id,
          template,
        });
      },
    });
  };
  return (
    <div className="absolute z-50 top-3 left-3 px-3 transform shadow-sm border-border border  bg-white rounded-md dark:bg-background p-1">
      <div className="flex gap-1 items-center">
        <ProtectedTaskComponent requiredPermissions={'dna_slideshow_delete'}>
          <Button
            className="size-8"
            variant={'ghost'}
            size={'icon'}
            loading={isPending}
            onClick={() =>
              deleteSlide({
                taskCode: taskId || '',
                dnaId,
                payload: {
                  slideshow_id: slideshowId,
                  slide_id: slide.id,
                },
              })
            }
          >
            <Icon icon="gg:trash" width={22} />
          </Button>
        </ProtectedTaskComponent>
        <ProtectedTaskComponent requiredPermissions={'dna_slideshow_create'}>
          <Button
            onClick={handleRegenerate}
            loading={isRegenerating}
            disabled={isRegenerating}
            className="size-8"
            variant={'ghost'}
            size={'icon'}
          >
            <Icon icon="mdi:autorenew" width={22} />
          </Button>
        </ProtectedTaskComponent>
        <ProtectedTaskComponent requiredPermissions={'dna_slideshow_edit'}>
          <Button onClick={() => setIsVoiceOverDialogOpen(true)} className="size-8" variant={'ghost'} size={'icon'}>
            <Icon icon="mingcute:mic-ai-line" width={22} />
          </Button>
        </ProtectedTaskComponent>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {selectedTemplate ? (
              <Button
                loading={isChangingTemplate}
                disabled={isChangingTemplate}
                className="size-8"
                variant={'ghost'}
                size={'icon'}
              >
                <img
                  src={selectedTemplate.image}
                  alt={selectedTemplate.label}
                  className="w-10 min-w-10 min-h-10 h-10 object-contain rounded-sm"
                />
              </Button>
            ) : (
              <Button className="size-8" variant={'ghost'} size={'icon'}>
                <Icon icon="bx:align-middle" width={22} />
              </Button>
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" side="right" alignOffset={-5} sideOffset={13}>
            <ProtectedTaskComponent requiredPermissions={'dna_slideshow_edit'}>
              {SLIDE_TEMPLATES.map((template) => (
                <DropdownMenuItem
                  className="flex gap-2 items-center"
                  key={template.value}
                  onClick={() => handleTemplateChange(template.value)}
                >
                  <img
                    src={template.image}
                    alt={template.label}
                    className="w-10 min-w-10 min-h-10 h-10 object-contain rounded-sm"
                  />
                  {t(template.label)}
                </DropdownMenuItem>
              ))}
            </ProtectedTaskComponent>
          </DropdownMenuContent>
        </DropdownMenu>

        {isVoiceOverDialogOpen && (
          <VoiceOverDialog
            open={isVoiceOverDialogOpen}
            setOpen={setIsVoiceOverDialogOpen}
            dnaId={dnaId}
            slideshow_id={slideshowId}
            slide={slide}
          />
        )}
      </div>
    </div>
  );
};

export default SlideEdit;
