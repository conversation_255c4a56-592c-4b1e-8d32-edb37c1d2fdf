import React from 'react';
import { useHasPermission, userAtom } from '@/modules/auth/store';
import { useAtomValue } from 'jotai';
import { Navigate } from 'react-router-dom';
import { IUser } from '@/modules/auth/types';

interface Permission {
  name: string;
}

const PAGE_REDIRECTS = {
  tools: [
    'dna_create',
    'topic_create',
    'course_create',
    'file_analyzer',
    'content_create',
    'program_plan_create',
    'content_dna_reformat',
  ],
  users: ['user_index', 'role_index'],
  myContent: ['dna_index'],
  tasks: ['dna_index'],
  administration: ['for_administration'],
};

const hasAnyPermission = (userRoles: Permission[], permissions: string[]): boolean =>
  permissions.some((permission) => userRoles.some((role) => role.name === permission));

interface RoleBasedRedirectProps {
  children: React.ReactNode;
}

export const RoleBasedRedirect: React.FC<RoleBasedRedirectProps> = ({ children }) => {
  const user = useAtomValue<IUser | null>(userAtom);
  const userRoles = user?.roles[0]?.permissions || [];

  if (hasAnyPermission(userRoles, PAGE_REDIRECTS.tools)) {
    return <Navigate to="/app/tools" replace />;
  } else if (hasAnyPermission(userRoles, PAGE_REDIRECTS.users)) {
    return <Navigate to={useHasPermission('user_index') ? '/app/users' : '/app/roles'} replace />;
  } else if (hasAnyPermission(userRoles, PAGE_REDIRECTS.myContent)) {
    return <Navigate to="/app/my-content/DNAs" replace />;
  } else if (hasAnyPermission(userRoles, PAGE_REDIRECTS.tasks)) {
    return <Navigate to="/app/my-tasks" replace />;
  } else if (hasAnyPermission(userRoles, PAGE_REDIRECTS.administration)) {
    return <Navigate to="/app/dashboard" replace />;
  } else {
    return <>{children}</>;
  }
};
