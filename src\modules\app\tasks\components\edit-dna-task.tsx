import { useState } from 'react';

import { Modal, useNotify, Icon, useConfirmDialog } from '@/index';
import { Editor } from '@/components/CKEditor';
import { Button } from '@/components/ui/button';
import { saveAs } from 'file-saver';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { marked } from 'marked';
import TurndownService from 'turndown';
import { useTranslation } from 'react-i18next';
import { useEditDnaInsideTask } from '@/modules/app/tasks/apis/queries';
import { ITask } from '@/modules/app/tasks/types';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
interface IProps {
  onOpen: any;
  onOpenChange: any;
  data: any;
  course: ITask;
}

const EditDna = ({ onOpen, onOpenChange, data, course }: IProps) => {
  //  State
  const [dnaText, setDnaText] = useState(data.dna_content);
  const { mutate, isPending } = useEditDnaInsideTask();
  // Hooks
  const { notify } = useNotify();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { t } = useTranslation();
  const { confirm } = useConfirmation();

  const handelEdit = () => {
    confirm({
      variant: 'info',
      title: t('dna.edit.confirmation.title'),
      description: t('dna.edit.confirmation.description'),
      onConfirm: () =>
        mutate({
          code: course.code,
          payload: {
            course_id: course.content.tool_id,
            dna_content: dnaText,
            dna_id: data.id,
            topic_id: data.topic_id,
          },
        }),
    });
  };

  // Download Dna
  const parseMarkdownToDocx = (markdownContent: any) => {
    const docElements: any = [];

    const lines = markdownContent.split('\n');

    lines.forEach((line: any) => {
      if (line.startsWith('**')) {
        const regex = /\*\*(.*?)\*\*/;

        const match = line.match(regex);
        docElements.push(
          new Paragraph({
            children: [
              new TextRun({
                text: match[1],
                bold: true,
                size: 30,
              }),
              new TextRun({
                text: line.replace(regex, ''),
                size: 30,
              }),
            ],
          })
        );
      } else if (line.startsWith('\\---')) {
        docElements.push(new Paragraph({}));
        docElements.push(new Paragraph({}));
        docElements.push(new Paragraph({}));
      } else {
        const tokens = marked.lexer(line);
        tokens.forEach((token) => {
          switch (token.type) {
            case 'heading':
              docElements.push(
                new Paragraph({
                  text: token.text,
                  heading: `heading${token.depth}` as any,
                })
              );
              break;
            case 'paragraph':
              docElements.push(
                new Paragraph({
                  children: [
                    new TextRun({
                      text: token.text,
                      size: 30,
                    }),
                  ],
                })
              );
              docElements.push(new Paragraph({}));
              break;
            case 'list':
              token.items.forEach((item: any) => {
                docElements.push(
                  new Paragraph({
                    text: item,
                    bullet: {
                      level: 0,
                    },
                  })
                );
              });
              break;
          }
        });
      }
    });

    return docElements;
  };

  const downloadWordDocument = (editorContent: any) => {
    const turndownService = new TurndownService();

    // Convert HTML to Markdown
    const markdownContent = turndownService.turndown(editorContent);

    // Parse Markdown to docx elements
    const docElements = parseMarkdownToDocx(markdownContent);

    // Create a new Word document
    const doc = new Document({
      sections: [
        {
          properties: {},
          children: docElements,
        },
      ],
    });

    // Convert the document to a Blob and save it
    Packer.toBlob(doc).then((blob) => {
      saveAs(blob, 'DNA.docx');
      notify.success('DNA Downloaded Successfully!');
    });
  };

  // Edit Dna

  const ConfirmSaveText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>Changing the DNA will update the current status to 'Draft.' Are you sure you want to proceed?</p>
      </div>
    );
  };

  const handleEditWithConfirm = async () => {
    showConfirm(ConfirmSaveText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        mutate(
          {
            code: course.code,
            payload: {
              course_id: course.content.tool_id,
              dna_content: dnaText,
              dna_id: data.id,
              topic_id: data.topic_id,
            },
          },
          {
            onSuccess: () => {
              onOpenChange(false);
            },
          }
        );
      },
    });
  };

  const countMin = (count: number) => {
    if (count >= 630) {
      return '7 min';
    } else if (count >= 540) {
      return '6 min';
    } else if (count >= 450) {
      return '5 min';
    } else if (count >= 360) {
      return '4 min';
    } else if (count >= 270) {
      return '3 min';
    } else if (count >= 180) {
      return '2 min';
    } else if (count >= 90) {
      return '1 min';
    } else {
      return 'Less than 1 min';
    }
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationEdit')}</p>
      </div>
    );
  };

  const handleClose = async (dnaId: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        onOpenChange();
      },
    });
  };

  return (
    <Modal
      width={1200}
      open={onOpen}
      onOpenChange={dnaText === data.dna_content ? onOpenChange : handleClose}
      modalHeader={t('editDnaModal.title')}
      isModal={false}
    >
      <div>
        <div className="mb-4">
          <div className="flex justify-between">
            <div className="flex gap-2 self-center">
              <p className="font-bold text-xl">{data.title}</p>
            </div>
            <div className="flex gap-2">
              <div className="text-[#4B5563] text-sm self-center">
                <p>
                  {data.word_count} {t('editDnaModal.word')}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div>
          <Editor editorContent={dnaText} setEditorContent={setDnaText} language={data?.language} />
          <div className="mt-3 flex justify-end">
            <Button
              variant={'outline'}
              onClick={
                data?.dna_status?.phase_with_status === 'Review: ready_for_production'
                  ? () => handleEditWithConfirm()
                  : () => handelEdit()
              }
              loading={isPending}
              disabled={isPending || dnaText === data.dna_content}
            >
              {t('editDnaModal.saveDna')}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default EditDna;
