import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useEditLearningOutcome } from '../../../apis/queries';

const ProgramEditDialog = ({
  isOpen,
  setIsOpen,
  rowBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  rowBeingEdited: { learning_outcome_id: number; outcome: string; learning_skill_type: number; program_id: number };
}) => {
  const { t } = useTranslation();
  const { form, setFieldValue } = useForm({
    outcome: rowBeingEdited?.outcome || '',
    learning_outcome_id: rowBeingEdited?.learning_outcome_id || 0,
    learning_skill_type: rowBeingEdited?.learning_skill_type || '',
  });
  const { mutate: editOutcom, isPending: isEditing } = useEditLearningOutcome();

  const handleSubmit = () => {
    editOutcom(
      {
        id: rowBeingEdited.program_id,
        form,
      },
      {
        onSuccess: () => {
          setIsOpen(false);
        },
      }
    );
  };

  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('editOutcomeTitle.title')}>
      <Form onSubmit={handleSubmit} className="space-y-2">
        <TextInput
          name="outcome"
          label={t('editOutcome.learningOutcome')}
          value={form.outcome}
          onChange={setFieldValue('outcome')}
        />
        <TextInput
          name="learning_skill_type"
          label={t('editOutcome.type')}
          value={form.learning_skill_type}
          onChange={setFieldValue('learning_skill_type')}
        />
        <div className="flex justify-end">
          <Button loading={isEditing} disabled={isEditing} type="submit" className="mt-4">
            {t('save')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ProgramEditDialog;
