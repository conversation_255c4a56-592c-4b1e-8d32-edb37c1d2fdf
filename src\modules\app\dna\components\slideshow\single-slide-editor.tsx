import './styles.css';
import { CKEditor } from '@ckeditor/ckeditor5-react';
import { Editor } from 'src/components/CKEditor';
import 'ckeditor5/ckeditor5.css';
import 'ckeditor5-premium-features/ckeditor5-premium-features.css';
import SlideEdit from './slide-floating-menu';
import { useEffect, useState } from 'react';
import { ISlide } from '../../apis/slideshow/types';
import { t } from 'i18next';
import _ from 'lodash';
import { useUpdateSlideContent } from '../../apis/queries';
import { cn } from '@/lib/utils';

const aiIcon = `
  <svg xmlns="http://www.w3.org/2000/svg" width="25" height="25" viewBox="2 2 25 25">
    <path
      fill="currentColor"
      d="m3.501 6.683l.003.067V9h5.918c.331 0 .649-.132.883-.366L12.19 6.75l-1.883-1.884a1.25 1.25 0 0 0-.884-.366H5.75a2.25 2.25 0 0 0-2.249 2.183M2.004 9.826L2 9.75v-3A3.75 3.75 0 0 1 5.75 3h3.672c.729 0 1.428.29 1.944.805L13.561 6h8.69A3.75 3.75 0 0 1 26 9.75v4.652a7.5 7.5 0 0 0-1.5-1.247V9.75a2.25 2.25 0 0 0-2.25-2.25h-8.69l-2.195 2.195a2.75 2.75 0 0 1-1.944.805H3.504v9.75a2.25 2.25 0 0 0 2.25 2.25h7.87c.234.536.529 1.038.875 1.5H5.755a3.75 3.75 0 0 1-3.75-3.75zM27 19.5a6.5 6.5 0 1 1-13 0a6.5 6.5 0 0 1 13 0m-6-4a.5.5 0 0 0-1 0V19h-3.5a.5.5 0 0 0 0 1H20v3.5a.5.5 0 0 0 1 0V20h3.5a.5.5 0 0 0 0-1H21z"
    />
  </svg>
  `;

export default function SlideEditor({
  slide,
  slideshowId,
  nextSlideId,
  dnaId,
  slideNumber,
  ...props
}: {
  slide: ISlide;
  slideshowId: number;
  nextSlideId: number | null;
  dnaId: number;
  slideNumber: number;
}) {
  const [isFloatingMenuVisible, setIsFloatingMenuVisible] = useState(false);
  const [content, setContent] = useState(slide.content);
  const { mutate: updateSlideContent, isPending: isUpdatingSlideContent } = useUpdateSlideContent();
  const [editor, setEditor] = useState<CKEditor<any> | null>(null);

  function normalizeHTMLUsingEditor(editor: any, html: any) {
    const viewFragment = editor.data.processor.toView(html);
    return editor.data.processor.toData(viewFragment);
  }

  const slidesHaveChanged =
    editor && normalizeHTMLUsingEditor(editor, slide.content) !== normalizeHTMLUsingEditor(editor, content);

  useEffect(() => {
    setContent(slide.content);
  }, [slide.content]);

  const handleUpdateSlideContent = () => {
    updateSlideContent({ dnaId: dnaId, payload: { slideshow_id: slideshowId, slide_id: slide.id, content } });
  };

  return (
    <div>
      <h5 className="mb-2">
        {t('slideshow.slide', { count: slideNumber })} - {slide?.title}
      </h5>
      <div
        // onMouseEnter={() => setIsFloatingMenuVisible(true)}
        // onMouseLeave={() => setIsFloatingMenuVisible(false)}
        className="[&_.ck-content]:space-y-7 [&_.ck-content]:flex [&_.ck-content]:items-center [&_.ck-content]:justify-center border-2 border-gray-200 rounded-md shadow-m custom-slideshow-container w-full aspect-video"
      >
        <div className="relative">
          <div className={cn('hidden', isFloatingMenuVisible && 'flex')}>
            <SlideEdit slideshowId={slideshowId} dnaId={dnaId} slide={slide} template={slide.template} />
          </div>
          <Editor
            height="auto"
            editorContent={content}
            setEditorContent={setContent}
            type="balloon"
            setEditor={setEditor}
            defaultPrompt={slide.voice_over}
            readOnly
          />

          {/* {slidesHaveChanged && (
            <Button
              loading={isUpdatingSlideContent}
              disabled={isUpdatingSlideContent}
              onClick={handleUpdateSlideContent}
              className="absolute top-4 right-4 px-6"
              size={'sm'}
            >
              {t('save')}
            </Button>
          )} */}

          {/* Modal for selecting an image */}

          {/* {isFloatingMenuVisible && (
            <Button
              onClick={() => setIsAddingDialogOpen(true)}
              variant={'outline'}
              size={'icon'}
              className="absolute -bottom-4  left-[50%] translate-x-[-50%] px-6"
            >
              <Icon icon="fluent:add-16-filled" className="text-primary" width={18} />
            </Button>
          )} */}

          {/* {isAddingDialogOpen && (
            <AddSlideDialog
              slideshowId={slideshowId}
              next_slide_id={nextSlideId}
              open={isAddingDialogOpen}
              onClose={() => setIsAddingDialogOpen(false)}
            />
          )} */}
        </div>
      </div>
    </div>
  );
}
