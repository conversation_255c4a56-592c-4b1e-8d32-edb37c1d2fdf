import { Form, TextInput, useForm, useValidate, Jumbotron, Api, Textarea } from '@/index';
import { MultiSelect } from '@/components/form/inputs/multi-select-combo-box';
import { Button } from '@/components/ui/button';
import { useGetPermissions } from '@/modules/app/roles/apis/queries';
import { useParams } from 'react-router-dom';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useGetOperationById, useUpdateOperation } from '@/modules/app/dashboard/operation/apis/queries';

const OperationEdit = () => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { mutate: updateOperation, isPending } = useUpdateOperation();
  const { data } = useGetPermissions();
  const { t } = useTranslation();
  const { id } = useParams<{ id: string | any }>();
  const { data: selectedOperation } = useGetOperationById(id);
  const permissionList = data?.map((permission) => ({ value: permission.id, label: permission.name }));

  useEffect(() => {
    if (selectedOperation) {
      setFieldValue('name')(selectedOperation.name);
      setFieldValue('description')(selectedOperation.description);
      setFieldValue('permissions')(selectedOperation.permissions.map((permission) => permission.id));
    }
  }, [selectedOperation?.id]);
  // State
  //Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    description: '',
    permissions: [],
  });

  const handleClear = () => {
    setFormValue({
      permissions: null,
      name: '',
    });
  };
  // Functions
  const handleGenerate = async () => {
    updateOperation({ id, payload: form });
  };
  return (
    <div>
      <Form onSubmit={handleGenerate}>
        <div className="justify-between">
          <div className="lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name"
              label={t('name')}
              placeholder={t('userPage.operationName')}
              value={form.name}
              onChange={setFieldValue('name')}
              isRequired
              validators={[isRequired()]}
            />
            <Textarea
              name="description"
              label={t('description')}
              value={form.description}
              onChange={setFieldValue('description')}
              isRequired
              validators={[isRequired()]}
            />
            <MultiSelect
              name="permissions"
              label={t('userPage.permissions')}
              key={selectedOperation?.id}
              options={permissionList || []}
              onChange={setFieldValue('permissions')}
              value={form.permissions}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={50}
              validators={[isNotEmptyArray()]}
              disabled={data?.length === 27 && form.name === 'super_admin'}
            />
          </div>
          <div className="flex items-start gap-2 mt-[16px] justify-end">
            {form.name !== 'super_admin' && (
              <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
                {t('save')}
              </Button>
            )}
          </div>
        </div>
      </Form>
    </div>
  );
};

export default OperationEdit;
