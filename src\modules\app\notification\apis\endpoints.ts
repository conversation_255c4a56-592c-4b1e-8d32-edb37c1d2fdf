import { Api } from '@/services';
import { INotification } from '../types';

export const getUnreadNotificationCount = async () => {
  const { data } = await Api.get(`/user/notifications/count`);
  return data.data.count;
};

export const getInitialNotifications = async (): Promise<INotification[]> => {
  const { data } = await Api.get(`/user/notifications?page_num=1&page_size=30`);
  return data?.data?.items;
};

export const changeNotificationReadStatus = async (id: string | number) => {
  const { data } = await Api.put(`/user/notifications/change-status/${id}`);
  return data.data;
};

export const markAllNotificationsAsRead = async () => {
  const { data } = await Api.put(`/user/notifications/mark-all-as-read`);
  return data.data;
};

export const deleteNotification = async (id: string | number) => {
  const { data } = await Api.delete(`/user/notifications/delete/${id}`);
  return data.data;
};
