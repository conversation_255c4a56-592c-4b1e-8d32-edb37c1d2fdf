import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ChunkErrorBoundary } from '../chunk-error-boundary';

// Mock useTranslation
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string, fallback?: string) => fallback || key,
  }),
}));

// Mock Icon component
jest.mock('../icon', () => ({
  Icon: ({ icon, className, width }: any) => (
    <div data-testid="icon" data-icon={icon} className={className} style={{ width }}>
      Icon
    </div>
  ),
}));

// Mock Button component
jest.mock('../ui/button', () => ({
  Button: ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} {...props}>
      {children}
    </button>
  ),
}));

// Component that throws a chunk error
const ChunkErrorComponent = () => {
  throw new Error('Failed to fetch dynamically imported module: https://example.com/chunk.js');
};

// Component that throws a non-chunk error
const NonChunkErrorComponent = () => {
  throw new Error('Some other error');
};

// Normal component
const NormalComponent = () => <div>Normal Component</div>;

describe('ChunkErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });
  afterAll(() => {
    console.error = originalError;
  });

  beforeEach(() => {
    // Reset location.reload mock
    delete (window as any).location;
    (window as any).location = { reload: jest.fn() };
  });

  it('renders children normally when there are no errors', () => {
    render(
      <ChunkErrorBoundary>
        <NormalComponent />
      </ChunkErrorBoundary>
    );

    expect(screen.getByText('Normal Component')).toBeInTheDocument();
  });

  it('catches and displays chunk loading errors', () => {
    render(
      <ChunkErrorBoundary>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    expect(screen.getByText('Failed to load component')).toBeInTheDocument();
    expect(screen.getByText(/There was an issue loading part of the application/)).toBeInTheDocument();
    expect(screen.getByText('Try Again')).toBeInTheDocument();
    expect(screen.getByText('Refresh Page')).toBeInTheDocument();
  });

  it('does not catch non-chunk errors', () => {
    // This should throw and not be caught by our boundary
    expect(() => {
      render(
        <ChunkErrorBoundary>
          <NonChunkErrorComponent />
        </ChunkErrorBoundary>
      );
    }).toThrow('Some other error');
  });

  it('handles retry functionality', async () => {
    const { rerender } = render(
      <ChunkErrorBoundary maxRetries={2}>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    // Should show error UI
    expect(screen.getByText('Failed to load component')).toBeInTheDocument();

    // Click retry
    fireEvent.click(screen.getByText('Try Again'));

    // Should attempt to render again (and fail again in this test)
    await waitFor(() => {
      expect(screen.getByText('Failed to load component')).toBeInTheDocument();
    });
  });

  it('refreshes page when refresh button is clicked', () => {
    render(
      <ChunkErrorBoundary>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    fireEvent.click(screen.getByText('Refresh Page'));

    expect(window.location.reload).toHaveBeenCalled();
  });

  it('shows technical details in development mode', () => {
    const originalEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'development';

    render(
      <ChunkErrorBoundary>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    expect(screen.getByText('Technical Details')).toBeInTheDocument();

    process.env.NODE_ENV = originalEnv;
  });

  it('renders custom fallback when provided', () => {
    const CustomFallback = () => <div>Custom Error Fallback</div>;

    render(
      <ChunkErrorBoundary fallback={<CustomFallback />}>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    expect(screen.getByText('Custom Error Fallback')).toBeInTheDocument();
    expect(screen.queryByText('Failed to load component')).not.toBeInTheDocument();
  });

  it('calls onError callback when error occurs', () => {
    const onError = jest.fn();

    render(
      <ChunkErrorBoundary onError={onError}>
        <ChunkErrorComponent />
      </ChunkErrorBoundary>
    );

    expect(onError).toHaveBeenCalledWith(
      expect.objectContaining({
        message: expect.stringContaining('Failed to fetch dynamically imported module'),
      }),
      expect.any(Object)
    );
  });
});
