import { defineEnum } from '../utils/defineEnum';

export const DnaEnums = Object.freeze({
  subject: defineEnum([
    {
      value: 'Education',
      label: 'Education',
    },
    {
      value: 'Tourism',
      label: 'Tourism',
    },
    {
      value: 'Energy',
      label: 'Energy',
    },
    {
      value: 'Mining and minerals',
      label: 'Mining and minerals',
    },
    {
      value: 'Industry and manufacturing',
      label: 'Industry and manufacturing',
    },
    {
      value: 'Transportation and logistics services',
      label: 'Transportation and logistics services',
    },
    {
      value: 'Financial services',
      label: 'Financial services',
    },
    {
      value: 'Health care',
      label: 'Health care',
    },
    {
      value: 'Sports',
      label: 'Sports',
    },
    {
      value: 'Law',
      label: 'Law',
    },
    {
      value: 'Entertainment',
      label: 'Entertainment',
    },
    {
      value: 'Art',
      label: 'Art',
    },
    {
      value: 'Design',
      label: 'Design',
    },
    {
      value: 'Real estate',
      label: 'Real estate',
    },
    {
      value: 'Human capital',
      label: 'Human capital',
    },
    {
      value: 'innovation',
      label: 'innovation',
    },
    {
      value: 'Environmental services',
      label: 'Environmental services',
    },
    {
      value: 'Chemicals',
      label: 'Chemicals',
    },
    {
      value: 'Pharmaceuticals and biotechnology',
      label: 'Pharmaceuticals and biotechnology',
    },
    {
      value: 'Agriculture',
      label: 'Agriculture',
    },
    {
      value: 'Food industries',
      label: 'Food industries',
    },
    {
      value: 'Aviation and defence',
      label: 'Aviation and defence',
    },
    {
      value: 'Information and Communication Technology',
      label: 'Information and Communication Technology',
    },
    {
      value: 'Business Management',
      label: 'Business Management',
    },
    {
      value: 'Retail and wholesale trade',
      label: 'Retail and wholesale trade',
    },
    {
      value: 'Other (write a subject)',
      label: 'Other (write a subject)',
    },
  ]),

  audience: defineEnum([
    {
      value: 'Early childhood education',
      label: 'Early childhood education',
      prompt:
        'Early childhood education, all of your sentences must be suitable for 4 to 5-year-olds. Simple, short, direct sentences as these sentences contain approximately 3-4 words, ,in an interesting style.',
    },
    {
      value: 'Primary education',
      label: 'Primary education',
      prompt:
        'Use short and simple sentences, but with complete meaning, as these sentences contain approximately 5-7 words.',
    },
    {
      value: 'Intermediate education',
      label: 'Intermediate education',
      prompt:
        'Use an educational structure of sentence that suitable for Intermediate students in terms of tone and language.',
    },
    {
      value: 'Secondary education',
      label: 'Secondary education',
      prompt:
        'Use an educational structure of sentence that suitable for Secondary students in terms of tone and language.',
    },
    {
      value: 'Undergraduate',
      label: 'Undergraduate',
      prompt:
        'use academic language Tone Scholarly and Intellectual: Maintain a scholarly tone that respects the advanced level of the students. Use discipline-specific terminology and assume a basic understanding of foundational concepts.Professional Development: Integrate elements that support professional growth, such as networking opportunities, guest lectures from industry professionals, or practical skills workshops relevant to the field.',
    },
    {
      value: 'Postgraduate',
      label: 'Postgraduate',
      prompt:
        'use academic language Tone Professional and Scholarly: The tone should be formal and professional, respecting the advanced level of the audience. Use technical language appropriate to the field and avoid overly simplistic explanations. Analytical and Critical: Encourage critical thinking and analysis. Pose challenging questions that prompt reflection on complex issues or the application of theories in real-world scenarios. engage with advanced-level students in an academic environment, employing a scholarly tone and discipline-specific terminology to enhance the educational interaction. It aims to respect the advanced knowledge base of the students, providing insights and explanations that align with their academic pursuits. The GPT will focus on maintaining a formal and respectful demeanor, using precise language appropriate for a higher education context.',
    },
    {
      value: 'Employees',
      label: 'Professionals',
      prompt:
        'use professional language Tone Professional and Practical: The tone should be professional, clear, and direct, focusing on practical applications of the concepts being taught. Use language that relates directly to their day-to-day work to make the material more relevant and engaging. Relevant Content: Ensure that all content is highly relevant to the employees’ roles. Include examples, case studies, and scenarios that they are likely to encounter in their work.',
    },
  ]),

  'bloom"s_taxonomy': defineEnum([
    {
      value: 'Remembering',
      label: 'Remembering',
    },
    {
      value: 'Understanding',
      label: 'Understanding',
    },
    {
      value: 'Applying',
      label: 'Applying',
    },
    {
      value: 'Analyzing',
      label: 'Analyzing',
    },
  ]),

  is_approved: defineEnum([
    { value: 0, label: 'No DNA' },
    { value: 1, label: 'Draft' },
    { value: 2, label: 'Approved' },
    { value: 3, label: 'Reported' },
  ]),
  dna_status: defineEnum([
    {
      value: 'approved',
      label: 'Approved',
    },
    {
      value: 'draft',
      label: 'Draft',
    },
    {
      value: 'no_dna',
      label: 'No DNA',
    },
    {
      value: 'reported',
      label: 'Reported',
    },
  ]),
  voice_type: defineEnum([
    {
      value: 'Man',
      label: 'Man',
    },
    {
      value: 'gpt-4o',
      label: 'gpt-4o',
    },
    {
      value: 'gemini',
      label: 'gemini',
    },
    {
      value: 'Woman',
      label: 'Woman',
    },
    {
      value: 'Child',
      label: 'Child',
    },
  ]),

  topic_status: defineEnum([
    {
      value: 'approved',
      label: 'Approved',
    },
    {
      value: 'draft',
      label: 'Draft',
    },
    {
      value: 'no_dna',
      label: 'No DNA',
    },
  ]),
  account_status: defineEnum([
    {
      value: 'pending',
      label: 'Pending',
    },
    {
      value: 'active',
      label: 'Active',
    },
    {
      value: 'blocked',
      label: 'Blocked',
    },
  ]),
  language: defineEnum([
    {
      value: 'Arabic',
      label: 'العربية',
    },
    {
      value: 'English',
      label: 'English',
    },
  ]),

  ai_base_model: defineEnum([
    {
      value: 'gpt-4o',
      label: 'gpt-4o',
    },
    // {
    //   value: 'gpt-4.5-preview',
    //   label: 'gpt-4.5-preview',
    // },
    // {
    //   value: 'o3-mini',
    //   label: 'o3-mini',
    // },
    {
      value: 'gemini-2.0-pro-exp',
      label: 'gemini-2.0-pro-exp',
    },
    {
      value: 'gpt-4.1',
      label: 'gpt-4.1',
    },
  ]),

  content_length: defineEnum([
    {
      value: 'default',
      label: 'default',
    },
    {
      value: '1 min',
      label: '1 min (90 words)',
    },
    {
      value: '2 min',
      label: '2 min (180 words)',
    },
    {
      value: '3 min',
      label: '3 min (270 words)',
    },
    {
      value: '4 min',
      label: '4 min (360 words)',
    },
    {
      value: '5 min',
      label: '5 min (450 words)',
    },
    {
      value: '6 min',
      label: '6 min (540 words)',
    },
    {
      value: '7 min',
      label: '7 min (630 words)',
    },
  ]),

  difficulty_level: defineEnum([
    {
      value: 'beginner',
      label: 'beginner',
    },
    {
      value: 'intermediate',
      label: 'intermediate',
    },
    {
      value: 'advanced',
      label: 'advanced',
    },
  ]),

  metadata: defineEnum([
    {
      value: 'subject',
      label: 'Subject',
    },
    {
      value: 'audience',
      label: 'Audience',
    },
    {
      value: 'bloom"s_taxonomy',
      label: 'Blooms Taxonomy',
    },
    {
      value: 'skills_level',
      label: 'Skills level',
    },
    {
      value: 'learning_strategy',
      label: 'learning_strategy',
    },
    {
      value: 'learning_type',
      label: 'learning_type',
    },
    {
      value: 'difficulty_level',
      label: 'Difficulty level',
    },
    {
      value: 'language',
      label: 'Language',
    },
    { value: 'credit_hours', label: 'Credit hours' },
    {
      value: 'program_level',
      label: 'Program level',
    },
    {
      value: 'credit_hours',
      label: 'Credit hours',
    },
    {
      value: 'pre_knowledge',
      label: 'Pre knowledge',
    },
    {
      value: 'style',
      label: 'Style',
    },
    {
      value: 'location',
      label: 'Location',
    },
    {
      value: 'educations',
      label: 'Educations',
    },
    {
      value: 'universities',
      label: 'Universities',
    },
    {
      value: 'nationalities',
      label: 'Nationalities',
    },
    {
      value: 'owner_dna_checklist',
      label: 'Owner DNA Checklist',
    },
    {
      value: 'owner_course_checklist',
      label: 'Owner Course Checklist',
    },
    {
      value: 'reviewer_dna_checklist',
      label: 'Reviewer DNA Checklist',
    },
    {
      value: 'reviewer_course_checklist',
      label: 'Reviewer Course Checklist',
    },
    {
      value: 'drawing_style',
      label: 'Drawing style',
    },
    {
      value: 'tags',
      label: 'Tags',
    },
    {
      value: 'dall-e-3_quality_options',
      label: 'dall-e-3 Quality options',
    },
    {
      value: 'gpt-image_quality_options',
      label: 'gpt-image Quality options',
    },
  ]),
  TaskStatus: defineEnum([
    {
      value: 'Pending',
      label: 'Pending',
    },
    {
      value: 'In Progress',
      label: 'In Progress',
    },
    {
      value: 'Completed',
      label: 'Completed',
    },
    {
      value: 'On Hold',
      label: 'On Hold',
    },
    {
      value: 'Overdue',
      label: 'Overdue',
    },
  ]),
  TaskPriorityStatus: defineEnum([
    {
      value: 'Low',
      label: 'Low',
    },
    {
      value: 'Medium',
      label: 'Medium',
    },
    {
      value: 'High',
      label: 'High',
    },
  ]),
  logsActions: defineEnum([
    {
      value: 'updated',
      label: 'Updated',
    },
    {
      value: 'deleted',
      label: 'Deleted',
    },
    {
      value: 'created',
      label: 'Created',
    },
  ]),
  logsModules: defineEnum([
    {
      value: 'Course',
      label: 'Course',
    },
    {
      value: 'Topic',
      label: 'Topic',
    },
    {
      value: 'Dna',
      label: 'DNA',
    },
  ]),
  aiImageModels: defineEnum([
    {
      value: 'gpt-image-1',
      label: 'gpt-image-1',
    },
    {
      value: 'dall-e-3',
      label: 'dall-e-3',
    },
  ]),
  gptImageDimensions: defineEnum([
    {
      value: '1024x1024',
      label: 'square (1024x1024)',
    },
    {
      value: '1536x1024',
      label: 'landscape (1536x1024)',
    },
    {
      value: '1024x1536',
      label: 'portrait (1024x1536)',
    },
    {
      value: 'auto',
      label: 'default (auto)',
    },
  ]),
  gptImageQualityOptions: defineEnum([
    {
      value: 'low',
      label: 'low',
    },
    {
      value: 'medium',
      label: 'medium',
    },
    {
      value: 'high',
      label: 'high',
    },
  ]),
  dallImageDimensions: defineEnum([
    {
      value: '1024x1024',
      label: '1024x1024',
    },
    {
      value: '1024x1792',
      label: '1024x1792',
    },
    {
      value: '1792x1024',
      label: '1792x1024',
    },
  ]),
  dallImageQualityOptionsEnglish: defineEnum([
    {
      value: 'standard',
      label: 'standard',
    },
    {
      value: 'hd',
      label: 'hd',
    },
  ]),
  dallImageQualityOptionsArabic: defineEnum([
    {
      value: 'standard',
      label: 'العاديه',
    },
    {
      value: 'hd',
      label: 'جودة عالية',
    },
  ]),
});
