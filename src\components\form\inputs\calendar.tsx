import { asField } from '../hocs/field';
import { Calendar } from '@/components/ui/calendar';
import { Label } from '@/components/ui/label';
interface CalendarProps extends React.TextareaHTMLAttributes<HTMLInputElement> {
  label?: string;
  errorMessage?: string;
  value?: string | any;
  onChange?: any;
  name: string;
}
export const CalendarInput = asField(
  ({ value, name, label = '', errorMessage, onChange = () => {}, ...props }: CalendarProps) => {
    return (
      <div>
        {label && (
          <div className="mb-2">
            <Label htmlFor={name}>{label}</Label>
          </div>
        )}

        <Calendar
          mode="single"
          selected={value}
          onSelect={onChange}
          className="rounded-md border shadow-sm"
          id={name}
          {...props}
        />

        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);
