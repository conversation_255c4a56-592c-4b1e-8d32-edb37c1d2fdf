import { useForm, TextInput, useValidate, Form } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useUpdateCourse } from '../apis/queries';
import { ICourse } from '../types';
interface IProps {
  course: ICourse | null;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}
const EditCourseDialog = ({ course, open, setOpen }: IProps) => {
  // hooks
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    title: course?.title || '',
  });
  const { mutate: updateCourse, isPending } = useUpdateCourse();
  const { t } = useTranslation();
  const handleSubmit = (e: Event) => {
    e.preventDefault();
    updateCourse(
      { id: course?.id, payload: form },
      {
        onSuccess: () => {
          setOpen(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('updateCourse')}</DialogTitle>
          <DialogDescription>{t('updateCourseDescription')}</DialogDescription>
        </DialogHeader>

        <Form onSubmit={handleSubmit} className={`pt-5 flex flex-col`}>
          <TextInput
            name="title"
            label={t('title')}
            value={form.title}
            onChange={setFieldValue('title')}
            isRequired
            validators={[isRequired()]}
          />
          <Button type="submit" className="mt-4 ms-auto" loading={isPending} disabled={isPending}>
            {t('CoursePlanCreationPage.submit')}
          </Button>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export { EditCourseDialog };
