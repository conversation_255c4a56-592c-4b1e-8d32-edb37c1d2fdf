# React Project with Vite

## Getting Started

To get started with this project, you'll need to have [Node.js](https://nodejs.org/) installed on your machine. It's recommended to use a version of Node.js that aligns with the project's requirements.

### Prerequisites

- Node.js (version 14 or higher recommended)
- npm or yarn (latest version recommended)

### Installation

1. _Install dependencies:_

   Using npm:

   bash
   npm install

## Scripts

The following scripts are available for use with this project:

- _Start Development Server:_

  To start the development server on localhost:5173 (or a custom port if configured):

  Using npm:

  ```bash
  npm run dev
  ```
###Front end 
- sudo npm install -g pm2
- pm2 start npm --name "my-app-dev" -- run dev
- pm2 startup
- pm2 save

###Manage the process:
- You can now use PM2 to manage your process:
	•	Check the status of the process:
- pm2 status
- pm2 logs my-app-dev
- pm2 stop my-app-dev