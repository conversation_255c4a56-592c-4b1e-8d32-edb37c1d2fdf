import { useMemo, useState } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { contentNeedsAtom, currentStepAtom } from '../../tools/store/contnet-need';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';
import {
  useAddContentNeedJob,
  useAddContentNeedRole,
  useAnalyzeJobsAndRoles,
  useDeleteContentNeedJob,
  useDeleteContentNeedRole,
} from '@/modules/app/content-need/apis/queries';
import ContentNeedEditJobDialog from './content-need-edit-job-dialog';
import ContentNeedEditJobRoleDialog from './content-need-edit-job-role-dialog';
import { Button } from '@/components/ui/button';
const JopsStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  //  State
  const [currentStep] = useAtom<number>(currentStepAtom);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [data] = useAtom(contentNeedsAtom);
  const [isEditingJobDialogOpen, setIsEditingJobDialogOpen] = useState(false);
  const [isEditingJobRoleDialogOpen, setIsEditingJobRoleDialogOpen] = useState(false);
  const [jobBeingEdited, setJobBeingEdited] = useState<{
    id: number | any;
    title: string;
    content_id: number | any;
  }>({
    id: null,
    title: '',
    content_id: null,
  });
  const [roleBeingEdited, setRoleBeingEdited] = useState<{
    id: number | any;
    title: string | any;
  }>({
    id: null,
    title: null,
  });

  //  Hooks
  const { t } = useTranslation();
  const { mutate: deleteJob, isPending: isDeleting, variables } = useDeleteContentNeedJob();
  const { mutate: deleteRole, isPending: isDeletingRole, variables: variablesRole } = useDeleteContentNeedRole();
  const { mutate, isPending } = useAnalyzeJobsAndRoles();
  const { mutate: addJob, isPending: isPendingJob } = useAddContentNeedJob();
  const { mutate: addRole, isPending: isPendingRole, variables: addRoleVariables } = useAddContentNeedRole();

  // Methods
  const handleSubmit = () => {
    if (currentStep < data.step) {
      moveToNextStep();
      return;
    }
    mutate(data.id, {
      onSuccess: moveToNextStep,
    });
  };

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'title',
        header: t('contentNeed.table.job_title'),
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="rtl:text-right font-medium">
                  <div>{row.title}</div>
                </div>
              );
            case 1:
              return <div>{row.title}</div>;
          }
        },
      },
      {
        accessorKey: 'roles',
        header: t('contentNeed.table.roles'),
        width: '400px',
        cell: ({ row }) => {
          return <div className={`break-words`}>{row.roles?.length}</div>;
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '200px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex">
                  <Button
                    type="button"
                    loading={isPendingRole && addRoleVariables.job_id === row.id}
                    disabled={isPendingRole && addRoleVariables.job_id === row.id}
                    onClick={() => addRole({ id: row.content_id, job_id: row.id })}
                    size={'icon'}
                    variant={'ghost'}
                  >
                    <Icon icon="ep:circle-plus" width={25} />
                  </Button>
                  <Button
                    size="icon"
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setJobBeingEdited({ id: row.id, content_id: row.content_id, title: row.title });
                      setIsEditingJobDialogOpen(true);
                    }}
                    className="border-primary"
                    variant={'ghost'}
                  >
                    <Icon icon="basil:edit-outline" width={25} className="text-primary cursor-pointer" />
                  </Button>
                  <Button
                    size="icon"
                    loading={isDeleting && variables === row.id}
                    disabled={isDeleting && variables === row.id}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteJob(row.id);
                    }}
                    variant={'ghost'}
                  >
                    <Icon icon="gg:trash" width="25" className="text-red-500 cursor-pointer" />
                  </Button>
                </div>
              );
            case 1:
              return (
                <div className="flex gap-2">
                  <Button
                    size="icon"
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      setRoleBeingEdited({ id: row.id, title: row.title });
                      setIsEditingJobRoleDialogOpen(true);
                    }}
                    className="border-primary"
                    variant={'ghost'}
                  >
                    <Icon icon="basil:edit-outline" width={25} className="text-primary cursor-pointer" />
                  </Button>

                  <Button
                    size="icon"
                    loading={isDeletingRole && variablesRole === row.id}
                    disabled={isDeletingRole && variablesRole === row.id}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteRole(row.id);
                    }}
                    variant={'ghost'}
                  >
                    <Icon icon="gg:trash" width="25" className="text-red-500 cursor-pointer" />
                  </Button>
                </div>
              );
          }
        },
      },
    ];
  }, [isDeleting, isDeletingRole, variablesRole, variables, addRoleVariables, isPendingRole]);

  return (
    <>
      <div className="flex items-center justify-center mt-20">
        <div className="max-w-[1500px] w-full bg-background">
          <p className="text-lg font-medium mb-2">{t('contentNeed.steps.jops.title')}</p>
          <Table
            rows={data.jobs || []}
            columns={columns}
            nestedConfig={{
              enabled: true,
              childProperties: ['roles'],
            }}
          >
            <TableContent>
              <TableContentHeader className="flex justify-end">
                <Button
                  type="button"
                  loading={isPendingJob}
                  disabled={isPendingJob || currentStep < data.step}
                  onClick={() => addJob(data.id)}
                  size={'sm'}
                >
                  + {t('CoursePlanCreationPage.addJob')}
                </Button>
              </TableContentHeader>
              <TableContentBody />
            </TableContent>
          </Table>
          {isEditingJobDialogOpen && (
            <ContentNeedEditJobDialog
              isOpen={isEditingJobDialogOpen}
              setIsOpen={() => setIsEditingJobDialogOpen(false)}
              jobBeingEdited={jobBeingEdited}
            />
          )}
          {isEditingJobRoleDialogOpen && (
            <ContentNeedEditJobRoleDialog
              isOpen={isEditingJobRoleDialogOpen}
              setIsOpen={() => setIsEditingJobRoleDialogOpen(false)}
              roleBeingEdited={roleBeingEdited}
            />
          )}
        </div>
      </div>
      <div className="flex justify-center items-center my-5">
        <Button loading={isPending} disabled={isPending} onClick={handleSubmit} className="text-white mt-3">
          {t('table.pagination.next')}
        </Button>
      </div>
    </>
  );
};

export default JopsStep;
