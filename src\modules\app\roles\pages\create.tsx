import { Form, TextInput, useForm, useValidate } from '@/index';
import { MultiSelect } from '@/components/form/inputs/multi-select-combo-box';
import { Button } from '@/components/ui/button';
import { useCreateRole, useGetPermissions } from '../apis/queries';
import { useTranslation } from 'react-i18next';

const DnaCreation = () => {
  // Hooks
  const { isRequired, isNotEmptyArray } = useValidate();
  const { mutate: createRole, isPending } = useCreateRole();
  const { data } = useGetPermissions();
  const { t } = useTranslation();
  const permissionList = data?.map((permission) => ({ value: permission.name, label: permission.name }));

  //Form
  const { form, setFieldValue, setFormValue } = useForm({
    name: '',
    permissions: null,
  });

  const handleClear = () => {
    setFormValue({
      permissions: null,
      name: '',
    });
  };
  // Functions
  const handleGenerate = async () => {
    createRole(form);
  };
  return (
    <div>
      <Form onSubmit={handleGenerate}>
        <div className="justify-between grid grid-cols-4 gap-6 ">
          <div className="col-span-3 lg:w-full  gap-5 lg:space-y-2">
            <TextInput
              name="name"
              label={t('name')}
              placeholder={t('userPage.roleName')}
              value={form.name}
              onChange={setFieldValue('name')}
              isRequired
              validators={[isRequired()]}
            />
            <MultiSelect
              name="permissions"
              label={t('userPage.permissions')}
              options={permissionList || []}
              onChange={setFieldValue('permissions')}
              value={form.permissions || []}
              placeholder={t('userPage.permissionsName')}
              variant="secondary"
              maxCount={50}
              validators={[isNotEmptyArray()]}
            />
          </div>
          <div className=" col-span-1 flex items-start gap-2 mt-[16px]">
            <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
              {t('create')}
            </Button>
            {/* <Button disabled={isPending} className=" mt-4" variant={'outline'} onClick={handleClear}>
              Clear
            </Button> */}
          </div>
        </div>
      </Form>
    </div>
  );
};

export default DnaCreation;
