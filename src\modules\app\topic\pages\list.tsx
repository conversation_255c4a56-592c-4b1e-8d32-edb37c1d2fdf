import { useMemo, useState } from 'react';
import { useFetchList, Icon, useConfirmDialog, ProtectedComponent } from '@/index';
import {
  Table,
  TableContentBody,
  TableContent,
  TableFilters,
  TableContentHeader,
  TablePagination,
  TableSearch,
  TableBulkActions,
} from '@/components/theTable';
import {
  convertStatusToFilterEnumByTitle,
  dateAndTimeFormat,
  formatUserByNameAndEmail,
  getAllNames,
  getTaskStatusStyle,
} from '@/utils/helpers';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { ITableColumn } from '@/components/theTable/types';
import { Button } from '@/components/ui/button';
import LanguageLink from '@/components/language-link';
import { ITopic } from '../types';
import { Badge } from '@/components/ui/badge';
import { useDeleteTopic, useDeleteTopics } from '../apis/queries';
import { useGetModulesStatus } from '../../dashboard/modules-status/apis/queries';
import { useContentTablesTabs } from '../../content-tabs';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import PromptViewerDialog from '../../components/prompt-viewer-dialog';

const TopicList = () => {
  //  State
  const [selectedRows, setSelectedRows] = useState<any>([]);
  //  Hooks
  const { mutate: deleteTopic, isPending } = useDeleteTopic();
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { t, i18n } = useTranslation();
  const { data: modulesStatus } = useGetModulesStatus();
  const contentTablesTabs = useContentTablesTabs();

  const statusFilter = convertStatusToFilterEnumByTitle(modulesStatus, 'Topic', i18n.language);

  const { loading, list, count, search, filters, pagination } = useFetchList('/topic/all-topics', 'topic', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      status_id: {
        dynamicEnum: statusFilter,
        placeholder: 'dnaConentPgae.statusFilter',
      },
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'dnaConentPgae.userFIlter',
            },
          }
        : {}),
    },
  });

  const labelKey = i18n.dir() === 'ltr';
  const TopicConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('topicCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeletTopic = async (dnaId: any) => {
    showConfirm(TopicConfirmText(), {
      danger: true,
      async onConfirm() {
        hideConfirm();
        deleteTopic(dnaId);
      },
    });
  };

  const ConfirmDeleteText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon="fluent:info-28-regular" width={55} />
        </div>
        <p>Please confirm that you want to delete the selected Topics.</p>
      </div>
    );
  };
  const { mutate: deleteTopics } = useDeleteTopics();

  const handledeleteTopics = () => {
    const ids = selectedRows.map((row: any) => row.id);
    deleteTopics(ids, {
      onSuccess: () => {
        hideConfirm();
      },
    });
  };
  const showDeleteAllConfirm = async () => {
    showConfirm(ConfirmDeleteText(), { onConfirm: handledeleteTopics });
  };

  // Computed values
  const bulkActions = useMemo(() => {
    return [
      {
        icon: 'uil:file-check',
        iconColor: 'text-green-500',
        label: 'Approve Topic',
        action: () => {},
      },
      {
        icon: 'gg:trash',
        iconColor: 'text-[#f05252]',
        label: 'Delete',
        action: showDeleteAllConfirm,
      },
    ];
  }, []);

  const columns: ITableColumn<ITopic>[] = useMemo(() => {
    return [
      {
        accessorKey: 'status',
        header: t('topicContentPage.table.status'),
        width: '150px',
        cell: ({ row }) => getTaskStatusStyle(row?.topic_status),
      },
      {
        accessorKey: 'title',
        header: t('topicContentPage.table.title'),
        width: '200px',
        cell: ({ row }) => (
          <div className="w-full justify-between flex rtl:text-right">
            <p className="font-medium">{row.title}</p>
            <Badge variant={'secondary'} className="h-6 w-6 flex items-center justify-center">
              {row?.dnas_counts}
            </Badge>
          </div>
        ),
      },
      {
        accessorKey: 'subject',
        header: t('topicContentPage.table.subject'),
        width: '180px',
        cell: ({ row }) => getAllNames(row.subject, i18n.language),
      },
      {
        accessorKey: 'audience',
        header: t('topicContentPage.table.audience'),
        width: '200px',
        cell: ({ row }) => getAllNames(row.audience, i18n.language),
      },
      {
        accessorKey: 'language',
        header: t('topicContentPage.table.language'),
        width: '100px',
        cell: ({ row }) => getAllNames(row.language, i18n.language),
      },
      {
        accessorKey: 'source',
        header: t('topicContentPage.table.source'),
        width: '100px',
        cell: ({ row }) => {
          let sourceKey;
          if (row.url) {
            sourceKey = 'Url';
          } else if (row.file_path) {
            sourceKey = 'File';
          } else if (row.txt) {
            sourceKey = 'Text';
          } else {
            sourceKey = 'Web';
          }
          return <p className="rtl:text-right">{t(sourceKey)}</p>;
        },
      },
      {
        accessorKey: 'date',
        header: t('topicContentPage.table.date'),
        width: '110px',
        cell: ({ row }) => {
          return dateAndTimeFormat(row.created_at, i18n.language);
        },
      },
      {
        accessorKey: 'user',
        header: t('topicContentPage.table.author'),
        width: '150px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.user);
        },
      },
      {
        accessorKey: 'prompt',
        header: t('prompt'),
        width: '120px',
        cell: ({ row }) => (row.prompt ? <PromptViewerDialog model={row.model} prompt={row.prompt} /> : '_'),
      },
      {
        accessorKey: 'actions',
        header: t('topicContentPage.table.actions'),
        width: '100px',
        cell: ({ row }) => (
          <div className="flex justify-end">
            <ProtectedComponent requiredPermissions={'topic_delete'}>
              <TooltipProvider delayDuration={100}>
                <Tooltip>
                  <TooltipTrigger>
                    <Icon
                      onClick={() => handleDeletTopic(row.id)}
                      icon="gg:trash"
                      width="25"
                      className="text-red-500 cursor-pointer"
                    />
                  </TooltipTrigger>
                  <TooltipContent>{t('delete')}</TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </ProtectedComponent>
          </div>
        ),
      },
    ];
  }, []);

  return (
    <>
      <Table
        rows={list}
        columns={columns}
        loading={loading}
        multipleSelect={{
          selectedRows,
          setSelectedRows,
        }}
      >
        <TableContent>
          <TableContentHeader tabs={contentTablesTabs}>
            <div className="flex gap-3 items-center">
              <TableColumsExpander />
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="ms-auto flex gap-3">
              <div>{selectedRows.length > 0 && <TableBulkActions actions={bulkActions} />}</div>
              <ProtectedComponent requiredPermissions={'topic_create'}>
                <LanguageLink to={'/app/tools/topic-creation'}>
                  <Button>
                    <span className="flex items-center gap-2">
                      <Icon icon="ic:round-add" width={17} />
                      <span className="text-sm">{t('topicContentPage.createNewtopic')}</span>
                    </span>
                  </Button>
                </LanguageLink>
              </ProtectedComponent>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
    </>
  );
};

export default TopicList;
