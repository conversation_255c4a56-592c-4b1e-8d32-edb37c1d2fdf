import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { t } from 'i18next';
import { IActivityQuestion } from '@/modules/app/tasks/types';
import { Label } from '@/components/ui/label';
import { useUpdateActivity } from '../../tasks/apis/queries';
import { useParams } from 'react-router-dom';

interface EditQuestionDialogProps {
  question: IActivityQuestion;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

const EditQuestionDialog: React.FC<EditQuestionDialogProps> = ({ isOpen, onOpenChange, question }) => {
  const [questionText, setQuestionText] = useState(question.question);
  const [optionValues, setOptionValues] = useState(question.options);
  const [selectedAnswer, setSelectedAnswer] = useState(question.answer);
  const { mutate, isPending } = useUpdateActivity();
  const { taskId } = useParams();
  const handleOptionChange = (key: string, value: string) => {
    setOptionValues((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleSave = () => {
    mutate(
      {
        activity_id: question.id,
        code: taskId || '',
        payload: {
          question: questionText,
          options: optionValues,
          answer: selectedAnswer,
          feedback: question.feedback,
        },
      },
      {
        onSuccess: () => {
          onOpenChange(false);
        },
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline">{t('activity.editQuestion')}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>{t('activity.editQuestion')}</DialogTitle>
        <div className="space-y-4 pt-4">
          {/* Question Text */}
          <div className="flex flex-col gap-2">
            <Label className="font-medium">{t('activity.question')}</Label>
            <Textarea
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              placeholder={t('activity.enterTheQuestion')}
              className="w-full"
            />
          </div>

          {/* Options */}
          <div className="flex flex-col gap-2">
            <Label className="font-medium">{t('activity.options')}</Label>
            <div className="flex flex-col gap-2">
              {Object.entries(optionValues).map(([key, value]) => (
                <div key={key} className="flex items-center gap-2">
                  <Input
                    value={value}
                    onChange={(e) => handleOptionChange(key, e.target.value)}
                    placeholder={t(`activity.option${key}`)}
                    className="w-full"
                  />
                  <Input
                    className="size-5"
                    type="radio"
                    name="correctAnswer"
                    value={key}
                    checked={selectedAnswer === key}
                    onChange={(e) => setSelectedAnswer(e.target.value)}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Footer Buttons */}
        <DialogFooter>
          <Button loading={isPending} onClick={handleSave} className="px-6">
            {t('activity.save')}
          </Button>
          <Button onClick={() => onOpenChange(false)} variant="outline">
            {t('activity.cancel')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditQuestionDialog;
