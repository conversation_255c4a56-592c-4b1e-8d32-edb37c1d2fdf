import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { Button } from '@/components/ui/button';
import { Icon } from '@/index';
import { contentNeedsAtom, currentStepAtom } from '../../tools/store/contnet-need';
import ContentNeedEditGoalDialog from './content-need-edit-goal-dialog';
import { useAnalyzeGoals, useDeleteContentNeedGoal } from '@/modules/app/content-need/apis/queries';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';

const MainGoalsStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  const [currentStep] = useAtom<number>(currentStepAtom);
  const { t } = useTranslation();
  const [data] = useAtom(contentNeedsAtom);
  const [isOpen, setIsOpen] = useState(false);
  const { mutate, isPending } = useAnalyzeGoals();
  const { mutate: deleteGoal, isPending: deleteGoalIsPending, variables } = useDeleteContentNeedGoal();
  const [goalBeingEdited, setGoalBeingEdited] = useState<{ id: string | null; value: string }>({
    id: null,
    value: '',
  }); // Default to null for new goal
  const handleSubmit = () => {
    if (currentStep < data.step) {
      moveToNextStep();
      return;
    }
    mutate(data.id, {
      onSuccess: moveToNextStep,
    });
  };

  const numberedGoals = useMemo(() => {
    return (data?.goals || []).map((goal, index) => ({
      ...goal,
      number: index + 1,
    }));
  }, [data?.goals]);

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'number',
        header: t('cousrePlanContentPage.table.status'),
        width: '100px',
      },
      {
        accessorKey: 'title',
        header: t('contentNeed.steps.mainGoals.goal'),
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '100px',
        cell: ({ row }) => (
          <div className="flex  gap-1 ">
            <Icon
              onClick={() => {
                setIsOpen(true);
                setGoalBeingEdited({ id: JSON.stringify(row.id), value: row.title });
              }}
              icon="basil:edit-outline"
              width={25}
              className="text-primary cursor-pointer"
            />
            <Button
              loading={deleteGoalIsPending && variables === row.id}
              disabled={deleteGoalIsPending && variables === row.id}
              variant={'ghost'}
              size={'icon'}
              onClick={() => deleteGoal(row.id)}
              className="text-red-500"
            >
              <Icon icon="gg:trash" width="25" className=" cursor-pointer" />
            </Button>
          </div>
        ),
      },
    ];
  }, [variables, deleteGoalIsPending]);

  return (
    <div className="flex justify-center items-center mt-20">
      <div className="w-full max-w-[1500px] space-y-2">
        <p className="text-lg font-medium "> {t('contentNeed.steps.mainGoals.title')} </p>
        <Table rows={numberedGoals} columns={columns}>
          <TableContent>
            <TableContentHeader className="flex justify-end">
              <Button
                type="button"
                size={'sm'}
                onClick={() => {
                  setIsOpen(true);
                  setGoalBeingEdited({ id: null, value: '' });
                }}
              >
                + {t('CoursePlanCreationPage.addGoal')}
              </Button>
            </TableContentHeader>
            <TableContentBody />
          </TableContent>
        </Table>
        <div className="flex justify-center">
          <Button loading={isPending} disabled={isPending} onClick={handleSubmit} className="mt-4">
            {t('table.pagination.next')}
          </Button>
        </div>

        {isOpen && (
          <ContentNeedEditGoalDialog
            isOpen={isOpen}
            setIsOpen={() => setIsOpen(false)}
            goalBeingEdited={goalBeingEdited}
          />
        )}
      </div>
    </div>
  );
};

export default MainGoalsStep;
