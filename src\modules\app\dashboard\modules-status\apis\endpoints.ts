import { Api } from '@/index';
import { IModuleStatusPayload, IPhasePayload } from '../types';
export const getModulesStatus = async () => {
  const { data } = await Api.get('/modules/status');
  return data.data;
};

export const updateModulePhase = async (payload: IPhasePayload) => {
  const { data } = await Api.put(`/modules/status/phase-update`, payload);
  return data.data;
};
export const updateModuleStatus = async ({ module_id, ...payload }: IModuleStatusPayload) => {
  const { data } = await Api.put(`/modules/status/${module_id}/status-update`, payload);
  return data.data;
};

export const getModuleStatusByTitle = async (title: string) => {
  const { data } = await Api.get(`/modules/status/${title}`);
  return data.data;
};
