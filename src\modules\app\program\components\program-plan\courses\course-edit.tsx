import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useEditCourse, useEditSkill } from '@/modules/app/program/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';

const ProgramEditDialog = ({
  isOpen,
  setIsOpen,
  rowBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  rowBeingEdited: {
    course_id: number;
    title: string;
    difficulty_level_id: number;
    learning_hours: number;
    program_id: number;
  };
}) => {
  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { form, setFieldValue } = useForm({
    title: rowBeingEdited?.title || '',
    difficulty_level_id: rowBeingEdited?.difficulty_level_id || 0,
    learning_hours: rowBeingEdited.learning_hours || 0,
    course_id: rowBeingEdited.course_id || 0,
  });
  const { mutate: editCourse, isPending: isEditing, variables: editJobVariables } = useEditCourse();
  const { data: difficulty_levelTypeOptions } = useGetSingleMetadata('difficulty_level');

  const handleSubmit = () => {
    editCourse(
      { id: rowBeingEdited.program_id, form },
      {
        onSuccess: () => {
          setIsOpen(false);
        },
      }
    );
  };

  return (
    <Modal open={isOpen} onOpenChange={setIsOpen} modalHeader={t('editCourse.title')}>
      <Form onSubmit={handleSubmit} className="space-y-3">
        <TextInput name="info" label={t('Course Title')} value={form.title} onChange={setFieldValue('title')} />
        <ComboboxInput
          name="difficulty_level_id"
          placeholder={t('cousrePlanContentPage.table.level')}
          label={t('cousrePlanContentPage.table.level')}
          options={generateEnum(difficulty_levelTypeOptions || [], 'id', labelKey)}
          value={form.difficulty_level_id}
          dropIcon
          onChange={setFieldValue('difficulty_level_id')}
        />
        <TextInput
          name="learning_hours"
          label={t('Learning Hours')}
          value={form.learning_hours}
          onChange={setFieldValue('learning_hours')}
        />
        <div className="flex justify-end">
          <Button loading={isEditing} disabled={isEditing} type="submit" className="mt-4 ml-auto w-fit">
            {t('save')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ProgramEditDialog;
