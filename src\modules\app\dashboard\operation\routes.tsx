import { lazy, Suspense } from 'react';
import { Icon, ProtectedRoute } from '@/components';

const OperationsCreate = lazy(() => import('./pages/create'));
const OperationsEditPage = lazy(() => import('./pages/edit'));
const OperationsListPage = lazy(() => import('./pages/list'));

const LoadingFallback = () => (
  <div>
    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
  </div>
);

export default [
  {
    path: 'operations-newList',
    element: (
      <Suspense fallback={<LoadingFallback />}>
        <OperationsListPage />
      </Suspense>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.operation',
        title: 'breadcrumb.userPage.operation',
      };
    },
  },
  {
    path: 'operations',
    element: (
      <ProtectedRoute requiredPermissions={'role_show'}>
        <Suspense fallback={<LoadingFallback />}>
          <OperationsListPage />
        </Suspense>
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.operation',
        title: 'breadcrumb.userPage.operation',
      };
    },
  },
  {
    path: 'operations/create',
    element: (
      <ProtectedRoute requiredPermissions={'role_create'}>
        <Suspense fallback={<LoadingFallback />}>
          <OperationsCreate />
        </Suspense>
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.operation',
        title: 'breadcrumb.userPage.operation',
      };
    },
  },
  {
    path: 'operations/edit/:id',
    element: (
      <ProtectedRoute requiredPermissions={'role_edit'}>
        <Suspense fallback={<LoadingFallback />}>
          <OperationsEditPage />
        </Suspense>
      </ProtectedRoute>
    ),
    loader() {
      return {
        label: 'breadcrumb.userPage.operation',
        title: 'breadcrumb.userPage.operation',
      };
    },
  },
];
