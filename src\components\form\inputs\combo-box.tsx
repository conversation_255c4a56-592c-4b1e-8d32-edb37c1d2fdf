import { useEffect, useState } from 'react';
import { Check, ChevronDown, ChevronsUpDown } from 'lucide-react';
import { asField } from '@/components/form/hocs/field';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';

export const ComboboxInput = asField(
  ({
    name,
    label,
    customLable = false,
    placeholder,
    options,
    value,
    disabled,
    onChange,
    optionLabelKey = 'label',
    optionValueKey = 'value',
    errorMessage,
    validatorsScroll,
    transalatedLableKey = '',
  }: any) => {
    const [open, setOpen] = useState(false);
    useEffect(() => {
      if (validatorsScroll && errorMessage) {
        (document.getElementById(name) as any).scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }, [validatorsScroll, errorMessage]);
    const { t } = useTranslation();
    return (
      <div>
        {!customLable && (
          <div className="mb-2 truncate overflow-hidden max-w-full">
            <Label htmlFor={name}>{label}</Label>
          </div>
        )}
        <Popover open={open} onOpenChange={setOpen}>
          <div className={`${customLable && 'grid grid-cols-3 gap-2'}`}>
            {customLable && (
              <div className="mb-2 self-center col-span-1 opacity-[0.5]">
                <p>{label}</p>
              </div>
            )}
            <PopoverTrigger disabled={disabled} asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className={`${
                  errorMessage ? 'border-red-500' : ''
                } w-full rounded-md justify-between col-span-2 flex  items-center gap-2`}
                type="button"
              >
                {value ? (
                  <span className={'max-w-full truncate'}>
                    {transalatedLableKey
                      ? t(
                          `${transalatedLableKey}.${
                            options.find((option: any) => option[optionValueKey] === value)?.[optionLabelKey]
                          }`
                        )
                      : options.find((option: any) => option[optionValueKey] === value)?.[optionLabelKey]}
                  </span>
                ) : (
                  <div className="text-muted-foreground">{placeholder}</div>
                )}

                <ChevronDown className="ms-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
          </div>
          <PopoverContent
            align="start"
            className="p-0  w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height]"
          >
            <Command className="w-full">
              <CommandInput placeholder={placeholder} />
              <CommandList>
                <CommandEmpty>{t('comboBox.search')}</CommandEmpty>
                {options.length > 0 && (
                  <ScrollArea
                    style={options.length <= 5 ? { height: options.length * 40, minHeight: 100 } : { height: 175 }}
                  >
                    <CommandGroup>
                      {options.map((option: any) => (
                        <CommandItem
                          key={option[optionValueKey]}
                          value={option[optionValueKey]}
                          onSelect={() => {
                            onChange(option[optionValueKey] === value ? '' : option[optionValueKey]);
                            setOpen(false);
                          }}
                        >
                          <Check
                            className={cn(
                              'me-2 h-4 w-4  min-w-4',
                              value === option[optionLabelKey] ? 'opacity-100' : 'opacity-0'
                            )}
                          />
                          {transalatedLableKey
                            ? t(`${transalatedLableKey}.${option[optionLabelKey]}`)
                            : option[optionLabelKey]}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </ScrollArea>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);
