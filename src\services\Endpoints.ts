const VITE_API_BASE_URL = (import.meta as any).env.VITE_API_BASE_URL;

// DNA APIs
export const GENERATE_DNA = '/dna/generate';
export const GENERATE_DNA_FORM_TOPIC = '/dna/generate-dna-content';
export const APPROVE_DNA = '/dna/approve-dna';
export const CHANGE_STATUS_DNA = '/dna/change-status';
export const GET_DNA_LIST = '/dna/all-dnas';
export const GENERATE_AUDIO = '/dna/listen-paragraph';
export const GENERATE_ACTIVITY = '/dna/activity-dna';
export const GENERATE_ACTIVITY_XML = '/dna/generate-dna-activity-xml';
export const GET_DNA = '/dna/get-dna';
export const UPDATE_DNA = '/dna/update-dna';
export const REPORT_DNA = '/dna/report-dna';
export const DELETE_DNA = '/dna/delete-dna';
export const UPLOAD_DNA_SOURCE = '/dna/upload-dna-source';
export const STATISTICS_DNA = 'dna/dna-statistics';

// Topics APIs
export const GENERATE_TOPICS = '/topic/generate';
export const REGENERATE_ROW = '/topic/row-regenerate';
export const ADD_TITLE = '/topic/generate-topic-dna-title';
export const GET_TOPIC_LIST = '/topic/all-topics';
export const DELETE_TOPICS = '/topic';

//User APIs
export const GET_USERS_LIST = '/user';
export const CREATE_USER = '/user';
export const CHANGE_STATUS_USER = '/user/change-status';
export const FILE_PATH = `${VITE_API_BASE_URL}`;
export const FILTER_USERS = '/user/lookup';
export const CHANGE_PASSWORD = '/user/change-password';
export const FILE_ANALUZER = '/analyzer';

//Roles APIs
export const ROLES = '/roles';

// Course Apis
export const COURSES = '/courses';

// Permissions Apis
export const GET_PERMISSIONS = '/permissions';

export const GENERATE_SLIDESHOW = '/slideshow';
