import { useMemo, useState } from 'react';
import {
  dateAndTimeFormat,
  formatDateByYearAndHour,
  formatUserByNameAndEmail,
  getTaskStatusStyle,
} from '@/utils/helpers';
import { useTranslation } from 'react-i18next';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';

import AssignTaskDialog from '@/modules/app/course/components/assign-task-dialog';
import { ITask } from '@/modules/app/tasks/types';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import { ICourse } from '../types';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Icon, ProtectedComponent } from '@/components';
import LanguageLink from '@/components/language-link';
import { useHasPermission } from '@/modules/auth/store';
import { useGetTaskByCourseId } from '../apis/queries';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useReOpenTask } from '../../tasks/apis/queries';
import UpdateTaskDialog from '../components/update-task-dialog';
import ListExpander from '../../components/list-expander';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
function Tasks({
  course,
  canCreateNewTask,
  canCreateChildTask,
}: {
  course: ICourse;
  canCreateNewTask: boolean;
  canCreateChildTask: boolean;
}) {
  const { data, isLoading } = useGetTaskByCourseId(course.id);
  const { mutate: reOpenTask, isPending: isReOpening } = useReOpenTask();
  const { confirm } = useConfirmation();

  //  State
  const [isTaskDialogOpen, setIsTaskDialogOpen] = useState(false);
  const [isChildTaskDialogOpen, setIsChildTaskDialogOpen] = useState(false);
  const isAdmin = useHasPermission('for_administration');
  const [isUpdateTaskDialogOpen, setIsUpdateTaskDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ITask | null>(null);
  const [searchValue, setSearchValue] = useState('');

  const { t, i18n } = useTranslation();
  const handleReOpenTask = (taskCode: string) => {
    confirm({
      variant: 'info',
      title: t('tasks.reOpen.confirmation.title'),
      description: t('tasks.reOpen.confirmation.description'),
      onConfirm: () => {
        reOpenTask(taskCode);
      },
    });
  };

  const columns: ITableColumn<ITask>[] = useMemo(() => {
    return [
      {
        accessorKey: 'code',
        header: t('assignedTask.table.code'),
        width: '240px',
        cell: ({ row }) =>
          isAdmin ? (
            <ProtectedComponent requiredPermissions={'for_administration'}>
              <LanguageLink to={`app/all-tasks/course/${row.code}`}>
                <Button variant={'link'}>{row.code}</Button>
              </LanguageLink>
            </ProtectedComponent>
          ) : (
            row.code
          ),
      },
      {
        accessorKey: 'description',
        header: t('assignedTask.table.description'),
        tooltip: true,
        width: '100px',
      },
      {
        accessorKey: 'assigned_to.name',
        header: t('assignedTask.table.assigned_to'),
        width: '200px',
        cell: ({ row }) => {
          return formatUserByNameAndEmail(row.assigned_to);
        },
      },
      {
        accessorKey: 'priority',
        header: t('assignedTask.table.priority'),
        width: '100px',
        cell: ({ row }) => t(row.priority),
      },
      {
        accessorKey: 'created_at',
        header: t('assignedTask.table.created_at'),
        width: '130px',
        cell: ({ row }) => {
          return <div className="flex gap-x-1 relative">{dateAndTimeFormat(row.created_at, i18n.language)}</div>;
        },
      },
      {
        accessorKey: 'due_date',
        header: t('assignedTask.table.due_date'),
        width: '130px',
        cell: ({ row }) => {
          return <div className="flex gap-x-1 relative">{dateAndTimeFormat(row.due_date, i18n.language)}</div>;
        },
      },

      {
        accessorKey: 'invitation',
        header: t('assignedTask.table.invitationStatus'),
        width: '150px',
        cell: ({ row }) =>
          `${t(row.invitation?.status) || t('invitation.noAction')} |  ${
            row.invitation?.response_at
              ? formatDateByYearAndHour(row.invitation?.response_at, i18n.language)
              : t('invitation.noDate')
          }`,
      },
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '170px',
        cell: ({ row }) => getTaskStatusStyle(row?.task_status),
      },
      {
        accessorKey: 'operations',
        header: t('assignedTask.table.operations'),
        width: '250px',
        cell: ({ row }) => <ListExpander initialVisiableItems={1} list={row.operations.map((data) => data.name)} />,
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '100px',
        cell: ({ row }) => {
          return (
            <div className="flex gap-2">
              <ProtectedComponent requiredPermissions={'task_edit'}>
                {row.can_update_task && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          size="icon"
                          variant="outline"
                          onClick={() => {
                            setIsUpdateTaskDialogOpen(true);
                            setSelectedTask(row);
                          }}
                        >
                          <Icon className="text-primary" icon="hugeicons:task-edit-01" width={27} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.updateTask')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
                {row.can_reopen_task && (
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          loading={isReOpening}
                          size="icon"
                          variant="outline"
                          onClick={() => handleReOpenTask(row?.code)}
                        >
                          <Icon
                            className="text-primary"
                            icon="material-symbols-light:assignment-return-rounded"
                            width={25}
                          />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>{t('cousrePlanContentPage.reopen')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
              </ProtectedComponent>
            </div>
          );
        },
      },
    ];
  }, []);
  return (
    <>
      <Table rows={data?.tasks?.data || []} columns={columns} loading={isLoading}>
        <TableContent>
          <TableContentHeader>
            <div className="flex gap-3 items-center justify-between w-full">
              <div className="flex gap-5 items-center">
                <TableColumsExpander />
                <Input
                  placeholder={t('search')}
                  onChange={(e: any) => setSearchValue(e.target?.value)}
                  value={searchValue}
                />
              </div>
              {canCreateNewTask && <Button onClick={() => setIsTaskDialogOpen(true)}>{t('assignTask')}</Button>}
              {canCreateChildTask && <Button onClick={() => setIsTaskDialogOpen(true)}>{t('assignChildTask')}</Button>}
            </div>
          </TableContentHeader>
          <TableContentBody />
        </TableContent>
      </Table>
      {isTaskDialogOpen && (
        <AssignTaskDialog
          onOpen={isTaskDialogOpen}
          onClose={() => setIsTaskDialogOpen(false)}
          course={course}
          canCreateChildTask={canCreateChildTask}
        />
      )}

      {isUpdateTaskDialogOpen && selectedTask && (
        <UpdateTaskDialog
          open={isUpdateTaskDialogOpen}
          onOpenChange={() => {
            setIsUpdateTaskDialogOpen(false);
          }}
          task={selectedTask}
        />
      )}
    </>
  );
}

export default Tasks;
