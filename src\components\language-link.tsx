import React from 'react';
import { Link, useParams } from 'react-router-dom';

const LanguageLink = ({ to, ...props }: { to: any } | any) => {
  const { lng } = useParams();

  let path;
  if (typeof to === 'string') {
    // Prepend the language code to the path
    path = `/${lng}${to.startsWith('/') ? '' : '/'}${to}`;
  } else if (typeof to === 'object' && to.pathname) {
    // Handle 'to' as an object with 'pathname' and 'search' properties
    path = {
      ...to,
      pathname: `/${lng}${to.pathname.startsWith('/') ? '' : '/'}${to.pathname}`,
    };
  } else {
    return null;
  }

  return <Link to={path} {...props} />;
};

export default LanguageLink;
