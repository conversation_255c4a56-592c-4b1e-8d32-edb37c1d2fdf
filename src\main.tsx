import React, { Suspense } from 'react';
import ReactDOM from 'react-dom/client';
import { AppRouter } from './AppRouter';
import './styles/App.css';
import './index.css';
import i18n from '@/utils/i18n';
import { PageLoader } from '@/components';

document.documentElement.setAttribute('lang', i18n.language);

ReactDOM.createRoot(document.getElementById('root') as any).render(
  <React.StrictMode>
    <Suspense fallback={<PageLoader fullScreen />}>
      <AppRouter />
    </Suspense>
  </React.StrictMode>
);
