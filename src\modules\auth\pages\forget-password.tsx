import 'react-toastify/dist/ReactToastify.css';
import { Form, TextInput, useForm, useValidate, Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';
import { useForgetPassword } from '../apis/queries';
import { useState, useEffect } from 'react';

export const ForgetPassword = () => {
  // Hooks
  const navigate = useLanguageNavigate();
  const { isRequired, isEmail } = useValidate();
  const { t, i18n } = useTranslation();
  const { mutate: forgetPassword, isPending } = useForgetPassword();
  const { form, setFieldValue } = useForm({
    email: '',
  });

  const getInitialCountdown = () => {
    const storedData = localStorage.getItem('passwordResetTimer');
    if (storedData) {
      const { expiry } = JSON.parse(storedData);
      const remainingTime = Math.ceil((expiry - Date.now()) / 1000);
      return remainingTime > 0 ? remainingTime : 0;
    }
    return 0;
  };

  const [countdown, setCountdown] = useState(getInitialCountdown);
  const [canResend, setCanResend] = useState(getInitialCountdown() === 0);

  useEffect(() => {
    if (countdown > 0) {
      const expiry = Date.now() + countdown * 1000;
      localStorage.setItem('passwordResetTimer', JSON.stringify({ expiry }));

      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);

      return () => clearTimeout(timer);
    } else if (countdown === 0 && !canResend) {
      setCanResend(true);
      localStorage.removeItem('passwordResetTimer');
    }
  }, [countdown, canResend]);

  const handleSubmit = async () => {
    forgetPassword(form, {
      onSuccess: () => {
        setCanResend(false);
        setCountdown(60);
      },
    });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-gray-50">
      <section className="bg-gray-50 dark:bg-background h-screen">
        <div className="flex flex-col items-center justify-center px-6 py-8 mx-auto h-full lg:py-0">
          <a
            onClick={() => navigate('/')}
            className="flex items-center justify-start mb-6 text-2xl font-semibold cursor-pointer"
          >
            {t('login.title')}
          </a>
          <div className="w-full bg-card rounded-xl shadow border border-border md:mt-0 mb-20 sm:max-w-md xl:p-0">
            <div className="p-6 space-y-4 md:space-y-6 sm:p-8">
              <div className="space-y-2">
                <h1 className="text-xl font-bold leading-tight tracking-tight">{t('forgetPassword.cardTitle')}</h1>
                <p className="text-gray-500 leading-tight tracking-tight">{t('forgetPassword.cardDescription')}</p>
              </div>
              <Form className="flex max-w-md flex-col gap-5" onSubmit={handleSubmit}>
                <TextInput
                  name="email"
                  label={t('email')}
                  placeholder={t('email')}
                  value={form.email}
                  onChange={setFieldValue('email')}
                  validators={[isRequired(), isEmail()]}
                />
                <Button
                  loading={isPending}
                  type="submit"
                  className="mt-4 flex gap-2 items-center"
                  disabled={!canResend && countdown > 0}
                >
                  <Icon className={`mt-1 ${i18n.dir() === 'rtl' ? 'rotate-180' : ''}`} icon="mdi:send" width={20} />
                  <p>
                    {canResend
                      ? t('forgetPassword.button')
                      : `${t('forgetPassword.resendIn')} ${formatTime(countdown)}`}
                  </p>
                </Button>
              </Form>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};
