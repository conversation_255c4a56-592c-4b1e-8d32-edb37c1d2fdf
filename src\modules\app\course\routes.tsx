import { lazy } from 'react';

const CoursesList = lazy(() => import('./pages/list'));
const SingleCoursePage = lazy(() => import('./pages/single'));
const SingleDna = lazy(() => import('../dna/pages/single'));

export default [
  {
    path: 'courses',
    element: <CoursesList />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.course',
        title: 'breadcrumb.myContentPage.course',
      };
    },
  },
  {
    path: 'courses/:id',
    element: <SingleCoursePage />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.course',
        title: 'breadcrumb.myContentPage.course',
      };
    },
  },
  {
    path: 'courses/:id/:dnaId',
    element: <SingleDna />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.dna',
      };
    },
  },
];
