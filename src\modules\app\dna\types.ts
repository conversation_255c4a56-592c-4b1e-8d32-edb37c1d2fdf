import { IStatus } from '../dashboard/modules-status/types';
import { ILanguage } from '../dashboard/transaltion/types';
import { IActivityQuestion } from '../tasks/types';

export type IDnaStatus = 'approved' | 'draft' | 'no_dna' | 'reported' | 'ready for review';
export type ISoundType = 'gpt-4o' | 'Man' | 'Woman' | 'Child';
type IResource = string | { url: string; description?: string };
export interface IDnaSlide {
  id_slide: string;
  type: string;
  content: IDnaSlideContent | any;
  slide_number: number;
  header?: string; // Optional, as not all slides have a header
  voice_over: string;
  audio_file_path?: string;
}

export interface DnaCreationData {
  version: string;
  topic: string;
  subject_id: string;
  other_subject: string;
  audience_id: string;
  bloom_tax_id: string;
  language_id: string;
  model: string;
  long_text: string;
  old_dna: string;
  file_path: string;
  url: string;
  txt: string;
  audio_length: string;
  method: number;
}

export interface DnaCreation {
  learning_obj: string;
  title: string;
  id: string | number;
  method: number;
}

export interface MultiActivity {
  case_study: number;
  fill_in_blank: number;
  ids: string[] | number[]; // Adjust type based on the actual `ids` values
  language: string;
  mcq: number;
  t_f: number;
}
interface IDnaSlideContent {
  title?: string;
  introduction?: string;
  bullet_points?: string[];
  closing_statement?: string;
  summary_of_key_takeaways?: string[];
  voice_over?: string;
}
export interface IDNAListItem {
  id: number;
  title: string;
  dna_content: string;
  status: string;
  dna_status: IStatus;
  course_status: IStatus;
  subject: IMetadata;
  bloom_tax: IMetadata;
  user: IUser;
  word_count?: number;
  audio_length: string;
  topic: string;
  created_at: string;
  language: IMetadata;
  learning_objectives: string;
}
export interface ITranslation {
  content: string;
  created_at: string;
  created_by: number;
  creator: { id: number; email: string };
  dna_id: number;
  editor: null;
  id: number;
  language: ILanguage;
  language_id: number;
  status: string;
  title: string;
  updated_at: string;
  updated_by: null | string;
  words_count: number;
}
export interface IDNA {
  id: number;
  title: string;
  dna_content: string;
  status: string;
  dna_status: IStatus;
  course_status: IStatus;
  subject: IMetadata;
  bloom_tax: IMetadata;
  user: IUser;
  word_count?: number;
  audio_length: string;
  created_at: string;
  content_style: string | null;
  file_path: string;
  intro_type: string;
  language: IMetadata;
  long_text: string;
  audience: IMetadata;
  model: string;
  dna: string;
  old_dna: string;
  topic: ITopic;
  other_subject: string;
  txt: string;
  url: string;
  version: string;
  learning_objectives: string;
  updated_at: string;
  audio_file_path: string | null;
  topic_id: number;
  is_deleted: number;
  text: string | null;
  dna_id: number | null;
  user_id: number;
  description: string | null;
  resources: (string | { url: string; description?: string })[] | null;
  reporting_reason: string | null;
  approved_by: { id: number; name: string };
  audio_generated_at: string | null;
  audio_generated_by: string | null;
  approved_at: string | null;
  deleted_at: string | null;
  slides: IDnaSlide[] | null;
  slides_show: IDnaSlide[] | null;
  has_activities: boolean;
  activities_count: number;
  activities: {
    case_study: IActivityQuestion[];
    fill_in_the_blank: IActivityQuestion[];
    mcq: IActivityQuestion[];
    true_false: IActivityQuestion[];
  };
  tags: ITag[];
  prompt: string;
  content_translations: ITranslation[];
  reviewer_notes: string;
}

interface ITopic {
  id: number;
  title: string;
  topic_status: IStatus;
  course: ICourse;
  all_dnas_ids: number[];
}

interface ICourse {
  id: number;
  title: string;
  course_status: IStatus;
  all_dnas_ids: number[];
}

interface IUser {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  account_status: string;
  created_at: string;
  updated_at: string;
}

interface ITag {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  pivot: IPivot;
}

interface IPivot {
  dna_id: number;
  dna_tag_id: number;
  created_at: string;
  updated_at: string;
}

interface ICaseStudy {
  id: number;
  created_at: string;
  updated_at: string;
  question: string;
  feedback: string;
  options: IOptions;
  answer: string;
  scenario: string;
  dna_id: number;
}

interface IOptions {
  a: string;
  b: string;
  c: string;
  d: string;
}

interface IMcq {
  id: number;
  created_at: string;
  updated_at: string;
  question: string;
  options: IMcqOptions;
  answer: string;
  dna_id: number;
}

interface IMcqOptions {
  a: string;
  'feedback of a': string;
  b: string;
  'feedback of b': string;
  c: string;
  'feedback of c': string;
  d: string;
  'feedback of d': string;
}

interface ITrueFalse {
  id: number;
  created_at: string;
  updated_at: string;
  question: string;
  feedback: string;
  options: ITrueFalseOptions;
  answer: string;
  dna_id: number;
}

interface ITrueFalseOptions {
  a: string;
  b: string;
}

interface IFillInTheBlank {
  id: number;
  created_at: string;
  updated_at: string;
  question: string;
  answer: string;
  feedback: string;
  dna_id: number;
}
interface User {
  id: number;
  name: string;
  email: string;
}

interface LogEntry {
  old: string | null;
  new: string;
}

interface Logs {
  'dna status': LogEntry;
  description: LogEntry;
  'dna content': LogEntry;
}
export interface ILogs {
  id: number;
  log_name: string;
  action: string;
  description: string;
  model_type: string;
  model_id: number;
  user_type: string;
  user: User;
  logs: Logs;
  created_at: string;
}

export interface IContributor {
  id: number;
  name: string;
  email: string;
  roles: string;
  contribute_in: string[];
  last_interaction: string;
}

export interface IStoryBoard {
  block_type: string;
  block_title: string;
  block_number: number;
  suggested_media: any;
  additional_notes: string;
  full_block_content: string;
  media: string;
  id: number;
}

export interface IAnalysis {
  created_at: string;
  dna_id: number;
  id: number;
  model: string;
  prompt: string;
}

export interface ILogsData {
  attribute?: string;
  old?: string | null;
  new?: string | null;
}
