import { useInfiniteQuery } from '@tanstack/react-query';
import { useImmer } from 'use-immer';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Api, useDebounce, DnaEnums } from '@/index';
import { useSearchParams } from 'react-router-dom';
import queryString from 'query-string';
import useLanguageNavigate from './use-lang-navigation';

interface FilterOption {
  enum?: string;
  api?: string;
  placeholder?: string;
  dynamicEnum?: { label: string; value: any }[];
}

interface Pagination {
  page_num: number;
  page_size: number;
}

interface Options {
  filters?: Record<string, FilterOption>;
  search?: string;
  pagination?: Pagination;
}

interface Filter {
  label: string;
  key: string;
  placeholder: string;
  options: Array<{ label: string; value: any }>;
  selectedValue: any;
  onChange: (value: any) => void;
  reset: () => void;
}

interface InfiniteListReturn<T> {
  isLoading: boolean;
  isFetching: boolean;
  isReady: boolean;
  additionalData: any | null;
  data: T[];
  count: number;
  refresh: () => void;
  search: {
    value: string;
    update: (newSearch: string) => void;
  };
  pagination: Pagination & {
    update: (updates: Partial<Pagination>) => void;
    hasNextPage: boolean;
    fetchNextPage: () => void;
  };
  filters: Filter[];
  setSearch: React.Dispatch<React.SetStateAction<string>>;
}

export const useInfiniteList = <T>(endpoint: string, options: Options = {}): InfiniteListReturn<T> => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useLanguageNavigate();
  const didMount = useRef(false);

  // Search State
  const initialSearchQuery = searchParams.get('search') || options.search || '';
  const [search, setSearch] = useState<string>(initialSearchQuery);
  const debouncedSearch = useDebounce(search, 500);

  // Pagination State
  const initialPageSize = options.pagination?.page_size || 10;
  const [pagination, setPagination] = useImmer<Pagination>({
    page_num: 1,
    page_size: initialPageSize,
  });

  // Filters State
  const generateInitialFilters = () => {
    const initialFilters: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      initialFilters[key] = value;
    });
    return initialFilters;
  };

  const [filters, setFilters] = useImmer<Record<string, string>>(generateInitialFilters());
  const [filterOptions, setFilterOptions] = useState<Record<string, { label: string; value: any }[]>>({});

  // React Query infinite fetch
  const { data, fetchNextPage, hasNextPage, isLoading, isFetching, refetch } = useInfiniteQuery({
    queryKey: ['infinite-list', endpoint, debouncedSearch, filters],
    queryFn: async ({ pageParam = 1 }) => {
      const query: Record<string, any> = {
        ...filters,
        search: debouncedSearch,
        page_num: pageParam,
        page_size: pagination.page_size,
      };

      const finalQuery = endpoint.includes('?')
        ? `&${queryString.stringify(query)}`
        : `?${queryString.stringify(query)}`;

      const response = await Api.get(`${endpoint}${finalQuery}`);
      const responseData = response.data.data;

      // Sync search params to URL
      setSearchParams(query);

      return {
        items: responseData.items || responseData.data,
        total: responseData.total || responseData?.meta?.total,
        additionalData: responseData.additionalData || null,
      };
    },
    getNextPageParam: (lastPage, pages) => {
      const totalPages = Math.ceil(lastPage.total / pagination.page_size);
      const nextPage = pages.length + 1;
      return nextPage <= totalPages ? nextPage : undefined;
    },
    initialPageParam: 1,
  });

  // Handle filter changes
  const handleFilterChange = (key: string, value: any) => {
    setFilters((draft) => {
      draft[key] = value;
    });
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({});
    setSearch('');
    setPagination((draft) => {
      draft.page_num = 1;
    });
  };

  // Fetch filter options from API if necessary
  const fetchFilterOptions = useCallback(
    async (filter: FilterOption, key: string) => {
      if (filter.api && !filterOptions[key]) {
        try {
          const response = await Api.get(filter.api);
          const options = response.data.data.items.map((item: any) => ({
            label: item.name === 'super_admin' ? item.name : item.name.charAt(0).toUpperCase() + item.name.slice(1),
            value: item.id,
          }));
          setFilterOptions((prev) => ({
            ...prev,
            [key]: options,
          }));
        } catch (error) {}
      }
    },
    [filterOptions]
  );

  // Generate filters
  const generateFilters = useCallback((): Filter[] => {
    if (!options.filters) return [];

    return Object.entries(options.filters)
      .map(([key, filter]) => {
        if (filter.enum) {
          return {
            label: filter.placeholder || key,
            placeholder: filter.placeholder || key,
            key,
            options: (DnaEnums as any)[filter.enum].map((opt: any) => ({ label: opt.label, value: opt.value })),
            selectedValue: filters[key],
            onChange: (value: any) => handleFilterChange(key, value),
            reset: resetFilters,
          };
        } else if (filter.api) {
          useEffect(() => {
            fetchFilterOptions(filter, key);
          }, [filter.api]);

          return {
            label: filter.placeholder || key,
            placeholder: filter.placeholder || key,
            key,
            options: filterOptions[key] || [],
            selectedValue: filters[key],
            onChange: (value: any) => handleFilterChange(key, value),
            reset: resetFilters,
          };
        }
        return null;
      })
      .filter((filter): filter is Filter => !!filter);
  }, [filters, filterOptions, fetchFilterOptions]);

  // Reset to first page when search changes
  useEffect(() => {
    if (!didMount.current) {
      didMount.current = true;
    } else {
      setPagination((draft) => {
        draft.page_num = 1;
      });
    }
  }, [debouncedSearch]);

  // Flatten data and compute total
  const flattenedData = data?.pages.flatMap((page) => page.items) ?? [];
  const totalCount = data?.pages[0]?.total ?? 0;

  return {
    isLoading,
    isFetching,
    isReady: !!data,
    additionalData: data?.pages[0]?.additionalData ?? null,
    data: flattenedData,
    count: totalCount,
    refresh: refetch,
    search: {
      value: search,
      update: setSearch,
    },
    pagination: {
      ...pagination,
      update: (updates: Partial<Pagination>) => setPagination((draft) => ({ ...draft, ...updates })),
      hasNextPage: !!hasNextPage,
      fetchNextPage,
    },
    filters: generateFilters(),
    setSearch,
  };
};
