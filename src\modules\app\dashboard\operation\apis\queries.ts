import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createOperation, deleteOperation, getOperationById, getOperations, updateOperation } from './endpoints';
import { IPermission } from '@/modules/app/roles/types';
import { useNotify } from '@/index';
import { IOperation } from '../types';
import { getPermissions } from '@/modules/app/roles/apis/endpoints';
import useLanguageNavigate from '@/hooks/use-lang-navigation';

// hook to get all Operations
export const useGetOperations = () => {
  return useQuery<IOperation[]>({
    queryKey: ['operations'],
    queryFn: getOperations,
  });
};

// Hook to get Operation by ID
export const useGetOperationById = (id: string | undefined) => {
  return useQuery<IOperation | null>({
    queryKey: ['operation', id],
    queryFn: () => (id ? getOperationById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null
  });
};

// Hook to update a Operation
export const useUpdateOperation = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  const navigate = useLanguageNavigate();

  return useMutation({
    mutationFn: ({ id, payload }: { id: string; payload: { name: string; permissions: string[] } }) =>
      updateOperation(id, payload),
    onSuccess: () => {
      // Invalidate the specific query for the Operation that was updated
      queryClient.invalidateQueries({ queryKey: ['operation'] });
      notify.success('Operation updated successfully');
      navigate('/app/dashboard/operations');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
// Hook to create a Operation
export const useCreateOperation = () => {
  const queryClient = useQueryClient();
  const navigate = useLanguageNavigate();
  const { notify } = useNotify();
  return useMutation<IOperation, Error, { name: string; permissions: IPermission }>({
    mutationFn: createOperation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['operation'] });
      navigate('/app/dashboard/operations');
      notify.success('Operation created successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};

// Hook to get permissions
export const useGetPermissions = () => {
  return useQuery<IPermission[], Error>({
    queryKey: ['permissions'],
    queryFn: getPermissions,
  });
};

// Hook to delete a role
export const useDeleteOperation = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: deleteOperation,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['operation'] });
      notify.success('Operation deleted successfully');
    },
  });
};
