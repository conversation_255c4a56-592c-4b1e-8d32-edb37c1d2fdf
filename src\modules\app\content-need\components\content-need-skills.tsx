import { useMemo } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { contentNeedsAtom, currentStepAtom } from '../../tools/store/contnet-need';
import {
  useAnalyzeJobsAndSkills,
  useDeleteContentNeedSkill,
  useEditContentNeedSkill,
} from '@/modules/app/content-need/apis/queries';
import { Button } from '@/components/ui/button';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { Combobox } from '@/components/combo-box';
import { IContentNeedSkill } from '@/modules/app/content-need/types';
import { Table, TableContentBody, TableContent } from '@/components/theTable';

const SkillsStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  // State
  const [currentStep] = useAtom<number>(currentStepAtom);
  const [data] = useAtom(contentNeedsAtom);

  //  Hooks
  const { mutate: deleteSkill, isPending: isDeleting, variables } = useDeleteContentNeedSkill();
  const { mutate: editSkill, isPending: isEditingLevel, variables: isEditingVariables } = useEditContentNeedSkill();
  const { t, i18n } = useTranslation();
  const { mutate, isPending } = useAnalyzeJobsAndSkills();
  const { data: difficultyLevelOptions } = useGetSingleMetadata('skills_level');
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const handleSubmit = () => {
    if (currentStep < data.step) {
      moveToNextStep();
      return;
    }
    mutate(data.id, {
      onSuccess: moveToNextStep,
    });
  };

  const handleLevelChange = (newLevel: number, data: IContentNeedSkill) => {
    editSkill({ ...data, level_id: newLevel });
  };

  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'skill',
        header: t('contentNeed.steps.skills.table.jobOrkills'),
        width: '170px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="rtl:text-right font-medium">
                  <div>{row.title}</div>
                </div>
              );
            case 1:
              return <div>{row.title}</div>;
          }
        },
      },
      {
        accessorKey: 'skillCount',
        header: t('contentNeed.steps.skills.table.skillcount'),
        width: '50px',
        cell: ({ row }) => {
          return <div className={`break-words`}>{row.skills?.length}</div>;
        },
      },
      {
        accessorKey: 'description',
        header: t('contentNeed.steps.skills.table.skill'),
        width: '150px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return <div>{row.description}</div>;
          }
        },
      },
      {
        accessorKey: 'type',
        header: t('contentNeed.steps.skills.table.type'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return <div>{row.type}</div>;
          }
        },
      },
      {
        accessorKey: 'levels',
        header: t('contentNeed.steps.skills.table.levels'),
        width: '150px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return (
                <div className="rtl:text-right">
                  {isEditingLevel && row.id === isEditingVariables.id ? (
                    <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />
                  ) : (
                    <Combobox
                      name="subject"
                      placeholder={t('dnaCreationPage.form.subject')}
                      options={generateEnum(difficultyLevelOptions || [], 'id', labelKey)}
                      onChange={(value: any) => handleLevelChange(value, row)}
                      value={row.level_id}
                    />
                  )}
                </div>
              );
          }
        },
      },
      {
        accessorKey: 'correlation',
        header: t('contentNeed.steps.skills.table.correlation'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return <div>{row.correlation}</div>;
          }
        },
      },
      {
        accessorKey: 'actions',
        header: t('cousrePlanContentPage.table.actions'),
        width: '50px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return '';
            case 1:
              return (
                <div className="flex gap-2">
                  <Button
                    size="icon"
                    loading={isDeleting && variables === row.id}
                    disabled={isDeleting && variables === row.id}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteSkill(row.id);
                    }}
                    variant={'ghost'}
                  >
                    <Icon icon="gg:trash" width="25" className="text-red-500 cursor-pointer" />
                  </Button>
                </div>
              );
          }
        },
      },
    ];
  }, [isEditingVariables, isEditingLevel, isDeleting, variables, difficultyLevelOptions]);

  return (
    <>
      <div className="mt-[-1.75rem] flex items-center justify-center">
        <div className="max-w-[1500px] w-full bg-background  p-6">
          <p className="text-lg font-medium mb-2">{t('contentNeed.steps.skills.title')}</p>
          <Table
            rows={data.jobs || []}
            columns={columns}
            nestedConfig={{
              enabled: true,
              childProperties: ['skills'],
            }}
          >
            <TableContent>
              <TableContentBody />
            </TableContent>
          </Table>
        </div>
      </div>
      <div className="flex justify-center items-center my-5">
        <Button onClick={handleSubmit} loading={isPending} disabled={isPending} className="text-white mt-3">
          {t('table.pagination.next')}
        </Button>
      </div>
    </>
  );
};

export default SkillsStep;
