import { Icon } from '@/components';
import { Button } from '@/components/ui/button';
import { useState } from 'react';
import { t } from 'i18next';
import { useGetSharedSlideshow } from '../../dna/apis/slideshow/queries';
import SingleSlideEditor from './single-slide-editor';
import SwiperPresentation from './presentation';
import { useParams } from 'react-router-dom';

const SingleSlideShow = () => {
  const [isPlayerOpen, setIsPlayerOpen] = useState(false);
  const { id } = useParams();
  const { data } = useGetSharedSlideshow(id);

  if (!data) return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />;
  return (
    <div className="relative">
      <div className="flex justify-between">
        <div className="flex gap-3">
          <Button onClick={() => setIsPlayerOpen(true)} variant={'outline'}>
            {t('slideshow.playPresentation')}
          </Button>
        </div>
      </div>
      <div className="space-y-7 mx-[10%] mt-7">
        {data &&
          data?.slides?.map((slide: any, index: number) => {
            return <SingleSlideEditor key={index} slide={slide} slideNumber={index + 1} />;
          })}
      </div>
      {isPlayerOpen && (
        <SwiperPresentation
          onOpenChange={() => setIsPlayerOpen(false)}
          isOpen={isPlayerOpen}
          slides={data?.slides || []}
        />
      )}
    </div>
  );
};

export default SingleSlideShow;
