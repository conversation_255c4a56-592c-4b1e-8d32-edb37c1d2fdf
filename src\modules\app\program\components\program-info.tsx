import { useEffect } from 'react';
import { Form, useForm, useValidate, Textarea, TextInput } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { programPlan<PERSON>tom, currentStepAtom } from '../store';
import { useSearchParams } from 'react-router-dom';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { generateEnum } from '@/utils/helpers';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { useProgramInfoStart } from '@/modules/app/program/apis/queries';

const ProgramInfoStep = ({ moveToNextStep }: { moveToNextStep: () => void }) => {
  const [currentStep] = useAtom<number>(currentStepAtom);
  const { isRequired } = useValidate();
  const [data] = useAtom(programPlan<PERSON>tom);
  const { mutate, isPending } = useProgramInfoStart();
  const { t, i18n } = useTranslation();
  const { data: subjectOptions } = useGetSingleMetadata('subject');
  const { data: audienceOptions } = useGetSingleMetadata('audience');
  const { data: languageOptions } = useGetSingleMetadata('language');
  const { data: programLevelOptions } = useGetSingleMetadata('program_level');
  const { data: creditHoursOptions } = useGetSingleMetadata('credit_hours');
  const { data: learningStrategyOptions } = useGetSingleMetadata('learning_strategy');
  const { data: learningTypeOptions } = useGetSingleMetadata('learning_type');

  const [searchParams, setSearchParams] = useSearchParams();

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { form, setFieldValue, setFormValue } = useForm({
    title: '',
    description: '',
    subject_id: '',
    audience_id: '',
    language_id: '',
    credit_hours_id: '',
    program_level_id: '',
    learning_strategy_id: '',
    learning_type_id: '',
  });

  useEffect(() => {
    if (searchParams.size === 1 && data) {
      setFormValue({
        title: data.title || '',
        description: data.description || '',
        subject_id: data.subject_id || '',
        audience_id: data.audience_id || '',
        language_id: data.language_id || '',
        credit_hours_id: data.credit_hours_id || '',
        program_level_id: data.program_level_id || '',
        learning_strategy_id: data.learning_strategy_id,
        learning_type_id: data.learning_type_id,
      });
    }
  }, [data]);

  const handleSubmit = () => {
    if (currentStep < data.step) {
      moveToNextStep();
      return;
    }
    mutate(form, {
      onSuccess: (data) => {
        setSearchParams({ programId: JSON.stringify(data.id) }, { replace: true });
        moveToNextStep();
      },
    });
  };

  return (
    <div className="flex justify-center items-center mt-[100px]">
      <Form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-7">
          <TextInput
            name="title"
            label={t('Program Title')}
            placeholder={t('dnaCreationPage.form.title')}
            value={form.title}
            onChange={setFieldValue('title')}
            isRequired
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="subject"
            label={t('dnaCreationPage.form.subject')}
            placeholder={t('dnaCreationPage.form.subject')}
            options={generateEnum(subjectOptions || [], 'id', labelKey)}
            value={form.subject_id}
            onChange={setFieldValue('subject_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="audience"
            label={t('dnaCreationPage.form.audience')}
            placeholder={t('dnaCreationPage.form.audience')}
            options={generateEnum(audienceOptions || [], 'id', labelKey)}
            value={form.audience_id}
            onChange={setFieldValue('audience_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            placeholder={t('CoursePlanCreationPage.form.learningType')}
            name="learningType_id"
            label={t('CoursePlanCreationPage.form.learningType')}
            options={generateEnum(learningTypeOptions || [], 'id', labelKey)}
            value={form.learning_type_id}
            onChange={setFieldValue('learning_type_id')}
            validators={[isRequired()]}
            dropIcon
          />
          <ComboboxInput
            placeholder={t('CoursePlanCreationPage.form.learningStrategy')}
            name="learningStrategy"
            label={t('CoursePlanCreationPage.form.learningStrategy')}
            options={generateEnum(learningStrategyOptions || [], 'id', labelKey)}
            value={form.learning_strategy_id}
            onChange={setFieldValue('learning_strategy_id')}
            validators={[isRequired()]}
            dropIcon
          />
          <ComboboxInput
            name="language"
            label={t('dnaCreationPage.form.language')}
            placeholder={t('dnaCreationPage.form.language')}
            options={generateEnum(languageOptions || [], 'id', labelKey)}
            value={form.language_id}
            onChange={setFieldValue('language_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="credit_id"
            label={t('Credit Hours')}
            placeholder={t('Credit Hours placeholder')}
            options={generateEnum(creditHoursOptions || [], 'id', labelKey)}
            value={form.credit_hours_id}
            onChange={setFieldValue('credit_hours_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <ComboboxInput
            name="program_level_id"
            label={t('program level')}
            placeholder={t('program level placeholder')}
            options={generateEnum(programLevelOptions || [], 'id', labelKey)}
            value={form.program_level_id}
            onChange={setFieldValue('program_level_id')}
            dropIcon
            validators={[isRequired()]}
          />
          <Textarea
            name="description"
            label={t('Give a brief description of the program')}
            placeholder={t('programOutline Placeholder')}
            rows={6}
            value={form.description}
            onChange={setFieldValue('description')}
            isRequired
            validators={[isRequired()]}
          />
        </div>

        <div className="flex justify-end pt-12">
          <Button type="submit" loading={isPending}>
            {t('Start')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default ProgramInfoStep;
