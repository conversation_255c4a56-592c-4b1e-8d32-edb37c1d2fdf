import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Button } from './ui/button';
import LanguageLink from './language-link';
import { Icon } from './icon';
import { useLogout } from '@/modules/auth/apis/queries';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { userAtom } from '@/modules/auth/store';
import { ModeToggle } from './theme-toggle';
import { Separator } from './ui/separator';

const Usericon = () => {
  const [user, setUser] = useAtom(userAtom);

  const { mutate: logout } = useLogout();

  const { t, i18n } = useTranslation();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={'ghost'} size={'icon'} className="w-9 h-9 flex items-center justify-center p-0">
          {user?.image ? (
            <img src={user.image} alt="user" className="w-9 h-9 rounded-full" />
          ) : (
            <Icon width="20" icon="mdi:user" />
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="min-w-[250px]" align={i18n.language === 'ar' ? 'start' : 'end'}>
        <DropdownMenuLabel className="p-2 w-full font-medium">
          <div className="flex items-center justify-between">
            <p className="text-gray-500 font-medium">{t('theme')}</p>
            <ModeToggle />
          </div>
          <Separator className="my-3" />
          <div
            className="flex 
            items-center gap-2"
          >
            {user?.image ? (
              <img src={user.image} alt="user" className="w-9 h-9 rounded-full" />
            ) : (
              <Icon width="20" icon="mdi:user" />
            )}
            <div className="space-y-1">
              <p className="font-bold capitalize">{user?.name}</p>
              <p className="text-gray-500 text-sm">{user?.email}</p>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuItem className="">
          <LanguageLink to="/app/profile" className="flex gap-3 items-center">
            <div className="flex items-center justify-center rounded-full">
              <Icon icon="hugeicons:user-circle-02" width={25} />
            </div>
            <span className="">{t('Profile')}</span>
          </LanguageLink>
        </DropdownMenuItem>
        <Separator className="my-3" />
        <DropdownMenuItem className="cursor-pointer" onClick={() => logout()}>
          <div className="flex items-center gap-3 text-destructive">
            <Icon width="23" icon="mynaui:logout" />
            <span className="mb-1">{t('userIcon.signOut')}</span>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default Usericon;
