import { Icon, Modal } from '@/components';
import { useParams } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useCallback, useState } from 'react';
import ImagesActionToolbar from './images-action-toolbar';
import MediaGallery from './media-gallery';
import { IMedia } from '@/modules/app/common/types';
import { useGetMedia } from '../../modules/app/dna/apis/media/queries';
import ImageUploaderDialog from './image-uploader-dialog';
import ImageGenerationDialog from './image-generation-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const FileManagerDialog = ({
  open,
  onOpenChange,
  handleImageSelect,
  defaultPrompt,
}: {
  open: boolean;
  onOpenChange: any;
  handleImageSelect: (url: string) => void;
  defaultPrompt?: string;
}) => {
  // State
  const [selectedImage, setSelectedImage] = useState<IMedia | null>(null);
  const [isImageUploadDialogOpen, setIsImageUploadDialogOpen] = useState<boolean>(false);
  const [isImageGenerationDialogOpen, setIsImageGenerationDialogOpen] = useState<boolean>(false);
  // Hooks
  const { dnaId } = useParams();
  const { t } = useTranslation();
  const { data, isLoading } = useGetMedia(dnaId, 'dna');

  // Functions
  const handleCheckboxChange = useCallback((item: IMedia) => {
    setSelectedImage((current) => (current?.uuid === item.uuid ? null : item));
  }, []);

  const handlePickMedia = useCallback(() => {
    if (selectedImage) {
      handleImageSelect(selectedImage.url);
      onOpenChange(false);
    }
  }, [handleImageSelect, onOpenChange, selectedImage]);

  return (
    <Modal open={open} onOpenChange={onOpenChange} width={1250} hasPadding={false} modalHeader={t('media.title')}>
      <div>
        <Tabs value="img" className="rtl:text-right w-full h-[640px] rounded-md flex">
          <TabsList className="relative h-full justify-start p-0 flex flex-col gap-2 bg-transparent border border-input rounded-none">
            <TabsTrigger
              className="w-full flex items-center border-b border-input rounded-none h-14 data-[state=active]:bg-semi-primary data-[state=active]:text-primary data-[state=active]:border-none py-3"
              value="img"
            >
              <span className="truncate w-[160px] flex items-center justify-center">{t('mediaManager.tabs.img')}</span>
            </TabsTrigger>
            <TabsTrigger
              className="w-full flex items-center border-b border-input rounded-none h-14 data-[state=active]:bg-semi-primary data-[state=active]:text-primary data-[state=active]:border-none py-3"
              value="videos"
            >
              <span className="truncate w-[160px] flex items-center justify-center">
                {t('mediaManager.tabs.videos')}
              </span>
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-col w-full">
            <div className="px-2 border-border shadow-sm border-b h-14 min-h-14 flex items-center justify-between">
              {selectedImage && (
                <ImagesActionToolbar selectedImage={selectedImage} setSelectedImage={setSelectedImage} />
              )}
              <DropdownMenu>
                <DropdownMenuTrigger className="ms-auto">
                  <Button size={'sm'} className="min-w-28 flex gap-2 items-center">
                    {t('media.addMedia')}
                    <Icon icon="iconamoon:arrow-down-2-fill" width="18" className="mt-1" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    className="min-w-24 flex gap-2 items-center "
                    onClick={() => setIsImageUploadDialogOpen(true)}
                  >
                    <Icon icon="solar:upload-minimalistic-bold" width={19} />
                    {t('media.upload')}
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className="min-w-24 flex gap-2 items-center "
                    onClick={() => setIsImageGenerationDialogOpen(true)}
                  >
                    <Icon icon="hugeicons:ai-magic" width="22" />
                    {t('media.useAi')}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {isImageUploadDialogOpen && (
                <ImageUploaderDialog open={isImageUploadDialogOpen} onOpenChange={setIsImageUploadDialogOpen} />
              )}
              {isImageGenerationDialogOpen && (
                <ImageGenerationDialog
                  open={isImageGenerationDialogOpen}
                  onOpenChange={setIsImageGenerationDialogOpen}
                  defaultPrompt={defaultPrompt}
                />
              )}
            </div>
            <TabsContent value="img" className="mt-0 p-4 flex-grow">
              <MediaGallery
                media={data?.media || []}
                isLoading={isLoading}
                selectedImage={selectedImage}
                onCheckboxChange={handleCheckboxChange}
              />
            </TabsContent>
            <div className="px-2 border-border border-t h-14 flex items-center justify-end">
              <Button
                size={'sm'}
                className="min-w-32 flex gap-1 items-center"
                onClick={handlePickMedia}
                disabled={!selectedImage}
              >
                {t('media.choose')}
              </Button>
            </div>
          </div>
        </Tabs>
      </div>
    </Modal>
  );
};

export default FileManagerDialog;
