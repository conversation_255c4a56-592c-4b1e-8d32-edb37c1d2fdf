import { useForm, TextInput, useValidate, Form } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { ITopic } from '../../topic/types';
import { useUpdateTopic } from '../../topic/apis/queries';
interface IProps {
  topic: ITopic | null;
  onOpen: boolean;
  onOpenChange: React.Dispatch<React.SetStateAction<boolean>>;
}
const EditTopicDialog = ({ topic, onOpen, onOpenChange }: IProps) => {
  // hooks
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    title: topic?.title || '',
    learning_objectives: topic?.learning_objectives || '',
  });
  const { mutate: updateTopic, isPending } = useUpdateTopic();
  const { t } = useTranslation();
  const handleSubmit = (e: Event) => {
    e.preventDefault();
    updateTopic(
      { id: topic?.id, payload: form },
      {
        onSuccess: () => {
          onOpenChange(false);
        },
      }
    );
  };

  return (
    <Dialog open={onOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('updateTopic')}</DialogTitle>
          <DialogDescription>{t('updateTopicDescription')}</DialogDescription>
        </DialogHeader>

        <Form onSubmit={handleSubmit} className={`pt-5 flex flex-col`}>
          <div className="flex flex-col gap-2">
            <TextInput
              name="title"
              label={t('title')}
              value={form.title}
              onChange={setFieldValue('title')}
              isRequired
              validators={[isRequired()]}
            />
            <TextInput
              label={t('learningObjectives')}
              name="learning_objectives"
              value={form.learning_objectives}
              onChange={setFieldValue('learning_objectives')}
              isRequired
              validators={[isRequired()]}
            />
          </div>
          <Button type="submit" className="mt-4 ms-auto" loading={isPending} disabled={isPending}>
            {t('CoursePlanCreationPage.submit')}
          </Button>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export { EditTopicDialog };
