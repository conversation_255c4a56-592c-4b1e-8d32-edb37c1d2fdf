import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  addNotes,
  addSlide,
  addTransaltionSlideshow,
  approveActivity,
  approveMultipleActivity,
  approveOrFeedbackTaskDna,
  assignTask,
  changeSlideTemplate,
  changeTaskStatus,
  chnageDnaStatusInsideTask,
  deleteActivity,
  deleteSlide,
  deleteSlideshow,
  editDnaInsideTask,
  generateActivity,
  generateActivityXml,
  generateDnaSlideshow,
  generateVoiceOverSample,
  getAllowedLanguages,
  getAllUsers,
  getSlideshowPlay,
  getTaskByCode,
  getTaskByCodeAdmin,
  getTasks,
  getTranslatedSlideshowAllowedLanguages,
  listSlideshow,
  markAsComplete,
  reAssignedTask,
  regenerateActivity,
  regenerateActivityById,
  reGenerateDnaSlideshow,
  regenerateSlide,
  removeTask,
  reOpenTask,
  reorderSlide,
  singleSlideShow,
  updateActivity,
  updateCourseTitle,
  updateDnaData,
  updateSlideContent,
  updateSlideshow,
  updateVoiceOverTextAndAudio,
} from './endpoints';
import { useNotify } from '@/index';
import { useTranslation } from 'react-i18next';
import { t } from 'i18next';

export const useGetUsers = () => {
  return useQuery({
    queryKey: ['tasksUsers'],
    queryFn: getAllUsers,
  });
};
export const useGetTasks = () => {
  return useQuery({
    queryKey: ['tasks'],
    queryFn: getTasks,
  });
};
export const useAssignTask = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: assignTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['TaskCode'] });
      notify.success('Task Assigned successfully');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useGetTaskByCode = (code: string) => {
  return useQuery({
    queryKey: ['task', 'reviwer', code],
    queryFn: () => getTaskByCode(code),
    enabled: !!code,
  });
};
export const useGetTaskByCodeAdmin = (code: string) => {
  return useQuery({
    queryKey: ['task', 'admin', code],
    queryFn: () => getTaskByCodeAdmin(code),
    enabled: !!code,
  });
};
export const useEditDnaInsideTask = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: editDnaInsideTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
      queryClient.invalidateQueries({ queryKey: ['reviwer'] });

      notify.success('DNA Edited successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useUpdateCourseTitle = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: updateCourseTitle,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
      queryClient.invalidateQueries({ queryKey: ['reviwer'] });
      notify.success('Course Edited successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useUpdateDnaData = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: updateDnaData,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['dna-translations'] });
      queryClient.invalidateQueries({ queryKey: ['reviwer'] });
      notify.success('DNA Edited successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useChangeDnaStatusInsideTask = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: chnageDnaStatusInsideTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      notify.success('DNA Edited successfully');
    },
    onError: (error) => {
      notify.error(
        error.message === 'Make sure all related models are created and reviewed' ? t('spicealError') : error.message
      );
    },
  });
};
export const useAddNotes = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: addNotes,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      notify.success('Note Added successfully');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useChangeTaskStatus = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: changeTaskStatus,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      notify.success(t('taskStatus,success'));
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};
export const useMarkAsCopmlete = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: markAsComplete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      notify.success('Course Marked as Complete!');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useReAssigneTask = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: reAssignedTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      notify.success(t('taskReAssigned.success'));
    },
    onError: (error) => {
      notify.error(t('taskReAssigned.error'));
    },
  });
};
export const useRemoveTask = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: removeTask,

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      notify.success('Task asd Successfully!');
    },
    onError: () => {
      notify.error('An error occurred. Please try again.');
    },
  });
};
export const useApproveOrFeedbackTaskDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveOrFeedbackTaskDna,
    onSuccess: () => {
      notify.success('DNA Approved Successfully!');

      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useReOpenTask = () => {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: reOpenTask,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      notify.success(t('taskReOpened.success'));
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};
export const useGenerateActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateActivity,
    onSuccess: () => {
      notify.success('activity.generatedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useRegenerateActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: regenerateActivity,
    onSuccess: () => {
      notify.success('activity.regeneratedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useApproveActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveActivity,
    onSuccess: () => {
      notify.success('activity.approvedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useApproveMultipleActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: approveMultipleActivity,
    onSuccess: () => {
      notify.success('activity.approvedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useDeleteActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteActivity,
    onSuccess: () => {
      notify.success('activity.deletedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useRegenerateActivityById = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: regenerateActivityById,
    onSuccess: () => {
      notify.success('activity.regeneratedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useGenerateActivityXml = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateActivityXml,
    onSuccess: () => {
      notify.success('activity.generatedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useUpdateActivity = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateActivity,
    onSuccess: () => {
      notify.success('activity.updatedSuccessfully');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// Slideshow API's   ------------

export const useListSlideshow = ({ taskCode, dnaId }: { taskCode: string | number; dnaId: number | string }) => {
  return useQuery({
    queryKey: ['slideshow', dnaId, taskCode],
    queryFn: () => listSlideshow({ taskCode, dnaId }),
    enabled: !!taskCode && !!dnaId,
  });
};
export const useGetSlideShowById = ({
  taskCode,
  dnaId,
  slideshowId,
}: {
  taskCode: string | number;
  dnaId: number | string;
  slideshowId: number | string;
}) => {
  return useQuery({
    queryKey: ['slideshow', dnaId, taskCode, slideshowId],
    queryFn: () => singleSlideShow({ taskCode, dnaId, slideshowId }),
    enabled: !!taskCode && !!dnaId && !!slideshowId,
  });
};
export const useGenerateSlideshow = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: generateDnaSlideshow,
    onSuccess: () => {
      notify.success('Slideshow Generated Successfully!');
      queryClient.invalidateQueries();
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useReGenerateDnaSlideshow = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reGenerateDnaSlideshow,
    onSuccess: () => {
      notify.success('Slideshow reGenerated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useUpdateSlideshowData = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: updateSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideUpdated'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useDleteSlideshow = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideDeleted'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useGetSlideshowPlay = ({
  taskCode,
  dnaId,
  slideshowId,
}: {
  taskCode: string;
  dnaId: number;
  slideshowId: number;
}) => {
  return useQuery({
    queryKey: ['slideshow-play', dnaId, taskCode, , slideshowId],
    queryFn: () => getSlideshowPlay({ taskCode, dnaId, slideshowId }),
    enabled: !!taskCode && !!dnaId && !!slideshowId,
  });
};
export const useGetAllowedLanguages = ({ taskCode, dnaId }: { taskCode: string | number; dnaId: number | string }) => {
  return useQuery({
    queryKey: ['allowed-languages', dnaId, taskCode],
    queryFn: () => getAllowedLanguages({ taskCode, dnaId }),
    enabled: !!taskCode && !!dnaId,
  });
};

// single slide apis
export const useReorderSlide = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: reorderSlide,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Reordered Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useDeleteSlide = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteSlide,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideDeleted'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useRegenerateSlide = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: regenerateSlide,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Regenerated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useAddNewSlide = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: addSlide,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
      notify.success(t('dna.slideshow.slideAddedSuccessfully'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideAdditionFailed'));
    },
  });
};
export const useChangeSlideTemplate = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: changeSlideTemplate,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Template Changed Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useUpdateSlideContent = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateSlideContent,
    onSuccess: (_, { dnaId }) => {
      notify.success('Slide Content Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useGenerateVoiceOverSample = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateVoiceOverSample,
    onSuccess: (_) => {
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      notify.success('Voice Over Sample Generated Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
export const useUpdateVoiceOverTextAndAudio = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateVoiceOverTextAndAudio,
    onSuccess: (_, { dnaId }) => {
      queryClient.invalidateQueries({ queryKey: ['slideshow', dnaId] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['slideshow-play'] });
      notify.success('Voice Over Generated Successfully!');
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

// unrefactored

export const useAddTransaltionSlideshow = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: addTransaltionSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideUpdated'));
    },
    onError: (error) => {
      notify.error(error.message);
    },
  });
};
export const useGetTranslatedSlideshowAllowedLanguages = ({
  dnaId,
  slideshowId,
  taskCode,
}: {
  dnaId: number | string;
  slideshowId: number | string;
  taskCode: string | number;
}) => {
  return useQuery({
    queryKey: ['translated-slideshow-allowed-languages', dnaId, slideshowId, taskCode],
    queryFn: () => getTranslatedSlideshowAllowedLanguages({ dnaId, slideshowId, taskCode }),
    enabled: !!dnaId && !!slideshowId,
  });
};
