import { useMemo, useState } from 'react';
import { useDleteSlideshow, useListSlideshow } from '../../apis/queries';
import { useParams } from 'react-router-dom';
import EmptySlideshow from './empty-state';
import { Table, TableContentBody, TableContent } from '@/components/theTable';
import { ISingleSlideShow, ISlideShow } from '@/modules/app/dna/apis/slideshow/types';
import { useTranslation } from 'react-i18next';
import TableContentHeader from '@/components/theTable/table-content-header';
import { getSlideStatusStyle } from './sildeshow-helpers';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components';
import SlideShowEditDialog from './slideshow-edit-dialog';
import SingleSlideShow from './slideshow';
import PromptViewerDialog from '@/modules/app/components/prompt-viewer-dialog';
import SlideshowGenerationDialog from './slideshow-generation-dialog';
import { ProtectedTaskComponent } from '../protected-task-component';

const SlideshowList = () => {
  const [slideShowEdit, setSlideShowEdit] = useState(false);
  const [slideShowData, setSlideShowData] = useState<ISlideShow | null>(null);
  const [selectedSlide, setSelectedSlide] = useState<ISlideShow | null>(null);
  const [slideshowGenerationDialogIsOpen, setSlideshowGenerationDialogIsOpen] = useState(false);
  const { dnaId, taskId } = useParams();
  const { data } = useListSlideshow({ dnaId: dnaId || '', taskCode: taskId || '' });
  const { i18n, t } = useTranslation();
  const { mutate: deleteSlide, variables, isPending } = useDleteSlideshow();

  const currentLanguageLabel = useMemo(() => {
    return i18n.language ? `name_${i18n.language}` : 'name_en';
  }, [i18n.language]);

  const columns: ITableColumn<ISlideShow>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('dnaConentPgae.table.title'),
        width: '150px',
        cell: ({ row }) => {
          return (
            <p
              className="text-primary cursor-pointer font-medium hover:underline"
              onClick={() => setSelectedSlide(row)}
            >
              {row.title}
            </p>
          );
        },
      },
      {
        accessorKey: 'language',
        header: t('CoursePlanCreationPage.form.language'),
        width: '80px',
        cell: ({ row }) => {
          return <div>{row.language[currentLanguageLabel]}</div>;
        },
      },
      {
        accessorKey: 'slides_count',
        header: t('dnaConentPgae.table.count'),
        width: '50px',
      },
      {
        accessorKey: 'status',
        header: t('dnaConentPgae.table.status'),
        width: '70px',
        cell: ({ row }) => {
          return (
            <div className={`px-4 text-center py-1.5 rounded-md whitespace-nowrap ${getSlideStatusStyle(row?.status)}`}>
              {row.status}
            </div>
          );
        },
      },
      {
        accessorKey: 'prompt',
        header: t('prompt'),
        width: '100px',
        cell: ({ row }) => (row.prompt ? <PromptViewerDialog prompt={row.prompt} model={row.model} /> : '_'),
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '50px',
        cell: ({ row }) => {
          return (
            <div className="flex items-center">
              <ProtectedTaskComponent requireAll={false} requiredPermissions="dna_slideshow_edit">
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => {
                    setSlideShowEdit(true);
                    setSlideShowData(row);
                  }}
                >
                  <Icon icon="solar:pen-new-square-linear" className="text-primary" width={22} />
                </Button>
              </ProtectedTaskComponent>
              <ProtectedTaskComponent requireAll={false} requiredPermissions="dna_slideshow_delete">
                <Button
                  onClick={() =>
                    deleteSlide({
                      taskCode: taskId || '',
                      dnaId: row.dna_id,
                      slideshow_id: row.id,
                    })
                  }
                  loading={isPending && row.id === variables?.slideshow_id}
                  disabled={isPending && row.id === variables?.slideshow_id}
                  size="icon"
                  variant="ghost"
                >
                  <Icon icon="gg:trash" width={25} className="text-red-500" />
                </Button>
              </ProtectedTaskComponent>
            </div>
          );
        },
      },
    ];
  }, [t, i18n.language, isPending]);

  if (data?.length === 0) return <EmptySlideshow />;

  if (selectedSlide) {
    return <SingleSlideShow singleSlideshow={selectedSlide} setSelectedSlide={setSelectedSlide} />;
  }

  return (
    <div>
      <Table
        nestedConfig={{
          enabled: true,
          childProperties: ['translations'],
        }}
        columns={columns}
        rows={data || []}
      >
        <TableContent>
          <TableContentHeader>
            <div className="flex justify-end w-full">
              <ProtectedTaskComponent requireAll={false} requiredPermissions="dna_slideshow_create">
                <Button onClick={() => setSlideshowGenerationDialogIsOpen(true)} className="min-w-[150px]">
                  {t('dnaConentPgae.slideshow.button')}
                </Button>
              </ProtectedTaskComponent>
            </div>
          </TableContentHeader>
          <TableContentBody />
        </TableContent>
      </Table>
      {slideShowEdit && (
        <SlideShowEditDialog
          onOpen={slideShowEdit}
          onClose={() => setSlideShowEdit(false)}
          data={slideShowData as any}
        />
      )}
      {slideshowGenerationDialogIsOpen && (
        <SlideshowGenerationDialog
          onOpenChange={setSlideshowGenerationDialogIsOpen}
          open={slideshowGenerationDialogIsOpen}
          id={Number(dnaId)}
          data={data as ISingleSlideShow[]}
        />
      )}
    </div>
  );
};

export default SlideshowList;
