import React, { useRef, useState, useEffect, memo } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Icon } from '@/index';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAtom } from 'jotai';
import { IShedredSlide } from '@/modules/app/dna/apis/slideshow/types';
import {
  captionsAtom,
  currentTimeAtom,
  currentTrackIndexAtom,
  isControlsVisibleAtom,
  isFullscreenAtom,
  totalDurationAtom,
} from '../../store';
import ProgressBar from './progress-bar';

interface AudioPlayerProps {
  audioTracks: { url: string; duration: number; startTime: number; index: number }[];
  slides: IShedredSlide[];
  onTrackChange: (trackIndex: number) => void;
  toggleFullscreen: () => void;
}

const icons = {
  play: <Icon icon="solar:play-bold" className="text-primary" width={20} />,
  pause: <Icon icon="solar:pause-bold" className="text-primary" width={20} />,
  volumeHigh: <Icon icon="ph:speaker-high-bold" width={20} />,
  volumeLow: <Icon icon="ph:speaker-low-bold" width={20} />,
  volumeMute: <Icon icon="ph:speaker-x-bold" width={20} />,
};

const AudioPlayer: React.FC<AudioPlayerProps> = ({ audioTracks, slides, onTrackChange, toggleFullscreen }) => {
  const [volume, setVolume] = useState(1);
  const [previousVolume, setPreviousVolume] = useState(1);
  const [isVolumeHovered, setIsVolumeHovered] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);
  const volumeTimeoutRef = useRef<NodeJS.Timeout>();

  const [isControlsVisible, setIsControlsVisible] = useAtom(isControlsVisibleAtom);
  const [currentTrackIndex, setCurrentTrackIndex] = useAtom(currentTrackIndexAtom);
  const [totalDuration, setTotalDuration] = useAtom(totalDurationAtom);
  const [currentTime, setCurrentTime] = useAtom(currentTimeAtom);
  const [captions, setCaptions] = useAtom(captionsAtom);
  const [isFullscreen] = useAtom(isFullscreenAtom);

  const formatTime = (timeInSeconds: number): string => {
    const minutes = Math.floor(timeInSeconds / 60);
    const seconds = Math.floor(timeInSeconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (audioRef.current) {
      setIsControlsVisible(true);
      audioRef.current.volume = newVolume;
    }
  };

  const getVolumeIcon = () => {
    if (volume === 0) return icons.volumeMute;
    if (volume < 0.5) return icons.volumeLow;
    return icons.volumeHigh;
  };

  const toggleMute = () => {
    if (volume > 0) {
      setPreviousVolume(volume);
      setVolume(0);
      if (audioRef.current) {
        audioRef.current.volume = 0;
      }
    } else {
      setVolume(previousVolume);
      if (audioRef.current) {
        audioRef.current.volume = previousVolume;
      }
    }
  };

  const handleTimeUpdate = (e: React.SyntheticEvent<HTMLAudioElement>) => {
    const currentAudio = e.target as HTMLAudioElement;
    const newTime = audioTracks[currentTrackIndex].startTime + currentAudio.currentTime;
    setCurrentTime(newTime);
  };

  const handleTrackEnd = () => {
    if (currentTrackIndex < audioTracks.length - 1) {
      setCurrentTrackIndex((prev) => prev + 1);
      onTrackChange(currentTrackIndex + 1);
    }
  };

  const togglePlayPause = () => {
    if (audioRef.current) {
      audioRef.current.paused ? audioRef.current.play() : audioRef.current.pause();
    }
  };

  const navigateToTrack = (trackIndex: number, timeWithinTrack: number = 0) => {
    setCurrentTrackIndex(trackIndex);
    onTrackChange(trackIndex);

    if (audioRef.current) {
      audioRef.current.src = audioTracks[trackIndex].url;
      audioRef.current.currentTime = timeWithinTrack;
      audioRef.current.play().catch(console.error);
    }
  };

  useEffect(() => {
    const loadTrackDurations = async () => {
      let cumulative = 0;
      const updatedTracks = [...audioTracks];

      for (let track of updatedTracks) {
        const audio = new Audio(track.url);
        await new Promise<void>((resolve) => {
          audio.addEventListener('loadedmetadata', () => {
            track.duration = audio.duration;
            track.startTime = cumulative;
            cumulative += audio.duration;
            resolve();
          });
        });
      }

      setTotalDuration(cumulative);
    };

    if (audioTracks.length > 0) {
      loadTrackDurations();
    }
  }, [audioTracks.length]);

  return (
    <div
      className={`
        relative transition-all duration-500 ease-in-out
        ${isFullscreen ? 'mt-auto w-full px-4' : 'mt-4 px-8'}
        ${
          isControlsVisible
            ? 'opacity-100 translate-y-0 pointer-events-auto'
            : 'opacity-0 translate-y-full pointer-events-none'
        }
        before:content-[''] before:absolute before:bottom-0 before:left-0 before:right-0 
        before:h-48 before:bg-gradient-to-t before:from-background before:to-transparent 
        before:-z-50 before:pointer-events-none
      `}
    >
      <audio
        ref={audioRef}
        src={audioTracks[currentTrackIndex]?.url}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleTrackEnd}
        autoPlay
      />
      <div className="space-y-3">
        <ProgressBar
          navigateToTrack={navigateToTrack}
          audioTracks={audioTracks}
          totalDuration={totalDuration}
          slides={slides}
        />
        <div>
          <div className="flex justify-between items-center gap-2">
            <Button variant="ghost" onClick={togglePlayPause} className="p-2">
              {audioRef.current?.paused ? icons.play : icons.pause}
            </Button>
            <div className="flex gap-3 text-gray-400">
              <div className="flex gap-1">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(totalDuration)}</span>
              </div>
              <div
                className="relative flex items-center gap-1"
                onMouseEnter={() => {
                  if (volumeTimeoutRef.current) {
                    clearTimeout(volumeTimeoutRef.current);
                  }
                  setIsVolumeHovered(true);
                }}
                onMouseLeave={() => {
                  volumeTimeoutRef.current = setTimeout(() => {
                    setIsVolumeHovered(false);
                  }, 300);
                }}
              >
                <Button variant="ghost" size="sm" className="p-0 h-auto hover:text-primary" onClick={toggleMute}>
                  {getVolumeIcon()}
                </Button>
                <div
                  className={`
                    h-8 flex items-center
                    transition-opacity duration-200
                    ${isVolumeHovered ? 'w-24 opacity-100' : 'w-0 opacity-0 pointer-events-none'}
                  `}
                >
                  <Slider value={[volume]} max={1} step={0.01} onValueChange={handleVolumeChange} className="w-full" />
                </div>
              </div>
              <Icon
                onClick={() => setCaptions(!captions)}
                className={`cursor-pointer ${captions ? 'text-primary' : 'text-gray-400'}`}
                icon="gg:captions"
                width={25}
              />
              <DropdownMenu>
                <DropdownMenuTrigger>
                  <Icon
                    icon="material-symbols:settings-outline-rounded"
                    width={25}
                    className="cursor-pointer data-[state=open]:text-primary data-[state=closed]:text-gray-400"
                  />
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56">
                  <DropdownMenuLabel className="opacity-50 font-light">Audio Settings</DropdownMenuLabel>
                  <DropdownMenuItem>Under Development</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Icon
                icon={isFullscreen ? 'hugeicons:arrow-collapse' : 'hugeicons:arrow-expand'}
                width={25}
                onClick={toggleFullscreen}
                className={`cursor-pointer ${isFullscreen ? 'text-primary' : 'text-gray-400'}`}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default memo(AudioPlayer);
