import { Modal, useForm, Form, TextInput, useValidate } from '@/index';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useHasPermission } from '@/modules/auth/store';
import { useGetRoles } from '../../roles/apis/queries';
import { useEditUser } from '../apis/queries';
import { IUser } from '../types';

interface EditUserProps {
  onOpen: boolean;
  onOpenChange: () => void;
  data: IUser;
}

const EditUser = ({ onOpen, onOpenChange, data }: EditUserProps) => {
  // Hooks
  const { isRequired, isEmail } = useValidate();
  const { t } = useTranslation();
  const { data: roles } = useGetRoles();
  const { mutate, isPending } = useEditUser();

  const { form, setFieldValue } = useForm({
    name: data.name,
    email: data.email,
    password: '',
    role: data.roles.map((role: any) => role).join(', '),
    account_status: data.account_status,
  });

  // Edit user
  const handleEdit = () => {
    mutate(
      { id: data.id, ...form },
      {
        onSuccess: () => {
          onOpenChange();
        },
      }
    );
  };

  return (
    <Modal open={onOpen} onOpenChange={onOpenChange} modalHeader={t('userPage.dialogHeaders.editUser')}>
      <Form onSubmit={handleEdit}>
        <div className="space-y-4">
          <TextInput
            name="name"
            label={t('name')}
            placeholder={t('name')}
            value={form.name}
            onChange={setFieldValue('name')}
            isRequired
            validators={[isRequired()]}
          />
          <TextInput
            name="email"
            label={t('email')}
            placeholder={t('email')}
            value={form.email}
            onChange={setFieldValue('email')}
            validators={[isRequired(), isEmail()]}
          />
          <ComboboxInput
            name="role"
            disabled={!useHasPermission('user_edit')}
            label={t('userPage.role')}
            placeholder={t('userPage.role')}
            options={roles || []}
            value={form.role}
            onChange={setFieldValue('role')}
            optionLabelKey="name"
            optionValueKey="name"
            validators={[isRequired()]}
            transalatedLableKey="roles"
          />
        </div>
        <div className="my-5">
          <RadioGroup
            className="mt-5 flex flex-col sm:flex-row gap-4"
            defaultValue="comfortable"
            value={form.account_status}
            onValueChange={setFieldValue('account_status')}
          >
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="active" id="r1" />
              <Label htmlFor="r1">{t('userPage.account.active')}</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="blocked" id="r2" />
              <Label htmlFor="r2">{t('userPage.account.blocked')}</Label>
            </div>
          </RadioGroup>
        </div>
        <div className="mt-2 flex justify-end">
          <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
            {t('userPage.buttons.updateUser')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default EditUser;
