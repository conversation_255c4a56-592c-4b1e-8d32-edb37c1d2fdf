import { useFetchList, Icon, ProtectedComponent } from '@/index';
import { useMemo, useState } from 'react';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useDeleteOperation } from '@/modules/app/dashboard/operation/apis/queries';
import { IOperation } from '@/modules/app/dashboard/operation/types';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import {
  Table,
  TableContentBody,
  TableContent,
  TableContentHeader,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { useAdminTablesTabs } from '@/modules/app/content-tabs';
import ListExpander from '@/modules/app/components/list-expander';
const OperationList = () => {
  // State
  const [deletedOperationId, setDeletedOperationId] = useState<string | number | null>(null);
  const permissionsLimit = 4;
  // hooks
  const navigate = useLanguageNavigate();
  const { t } = useTranslation();

  const { mutate: deleteOperation, isPending: isDeleting } = useDeleteOperation();
  const adminTableTabs = useAdminTablesTabs();

  const { loading, list, count, refresh, search, pagination } = useFetchList<IOperation>('/operations', 'operations', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 10,
    },
  });

  // Functions
  const handleDeleteOperation = async (id: string | number) => {
    setDeletedOperationId(id);
    deleteOperation(id as any, {
      onSuccess: () => {
        refresh();
      },
    });
  };

  const columns: ITableColumn<IOperation>[] = useMemo(() => {
    return [
      {
        accessorKey: 'id',
        header: 'Id',
        width: '50px',
      },
      {
        accessorKey: 'name',
        header: t('userPage.table.name'),
        width: '300px',
      },
      {
        accessorKey: 'description',
        header: t('userPage.table.description'),
        tooltip: true,
      },
      {
        accessorKey: 'permissions',
        header: t('userPage.table.permissions'),
        cell: ({ row }) => (
          <ListExpander initialVisiableItems={2} list={row.permissions.map((permission) => permission.name)} />
        ),
      },
      {
        accessorKey: 'actions',
        header: t('dnaConentPgae.table.actions'),
        width: '200px',
        cell: ({ row }) => {
          return (
            <div className="flex gap-2">
              <ProtectedComponent requiredPermissions="role_edit">
                <Button variant={'ghost'} size={'icon'}>
                  <Icon
                    onClick={() => {
                      navigate(`/app/dashboard/operations/edit/${row.id}`);
                    }}
                    icon="basil:edit-outline"
                    width={25}
                    className="text-primary cursor-pointer"
                  />
                </Button>
              </ProtectedComponent>
              <ProtectedComponent requiredPermissions="role_delete">
                <Button loading={isDeleting && row.id === deletedOperationId} variant={'ghost'} size={'icon'}>
                  <Icon
                    onClick={() => handleDeleteOperation(row?.id)}
                    icon="gg:trash"
                    width="25"
                    className="text-red-500 cursor-pointer"
                  />
                </Button>
              </ProtectedComponent>
            </div>
          );
        },
      },
    ];
  }, [t]);
  return (
    <div>
      <Table rows={list} columns={columns} loading={loading}>
        <TableContent>
          <TableContentHeader tabs={adminTableTabs} className="justify-between">
            <div className="flex gap-3 items-center">
              <TableSearch className="w-fit" search={search} placeholder={t('userPage.filters.operationSearch')} />
            </div>
            <div className="ms-auto">
              <Button onClick={() => navigate('/app/dashboard/operations/create')}>
                {' '}
                {t('userPage.buttons.headerOperationButton')}{' '}
              </Button>
            </div>
          </TableContentHeader>
          <TableContentBody />
          <TablePagination count={count} pagination={pagination} />
        </TableContent>
      </Table>
    </div>
  );
};

export default OperationList;
