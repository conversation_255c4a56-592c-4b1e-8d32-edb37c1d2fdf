import { IDNA } from '@/modules/app/dna/types';
import { useTranslation } from 'react-i18next';
import { Combobox } from '@/components/combo-box';
import { generateEnum } from '@/utils/helpers';
import { useForm } from '@/hooks';
import { Button } from '@/components/ui/button';
import { useGenerateDnaTranslations, useGetDnaTranslations } from '@/modules/app/dna/apis/queries';
import { Form } from '@/index';
import TranslationTable from '../../../components/localization/transaltion-table';

const TranslationTab = ({ details }: { details: IDNA | any }) => {
  const { t, i18n } = useTranslation();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { form, setFieldValue } = useForm({
    id: details.id,
    language_id: '',
  });

  const { data } = useGetDnaTranslations(details?.id);
  const { mutate: generateTranslation, isPending } = useGenerateDnaTranslations();

  const handleSubmit = () => {
    generateTranslation(form);
  };

  return (
    <div>
      {!details?.content_translations?.length && (
        <div className="text-lg font-medium flex gap-5 items-center ms-2 mb-4">
          <span>{t('dnaSinglePage.localiztion.noLocalization')}</span>
        </div>
      )}
      <Form onSubmit={handleSubmit}>
        <div className="flex gap-3">
          <div className="w-[400px]">
            <Combobox
              placeholder={t('dnaCreationPage.form.language')}
              options={generateEnum(data || [], 'id', labelKey)}
              value={form.language_id}
              onChange={setFieldValue('language_id')}
            />
          </div>
          <div>
            <div className="flex">
              <Button
                variant={'ghost'}
                loading={isPending}
                disabled={isPending}
                className="min-w-[100px] border-primary border-2 flex gap-1 items-center"
                type="submit"
              >
                <span className="text-lg self-center mb-0.5 ">+</span> {t('dnaCreationPage.translate')}
              </Button>
            </div>
          </div>
        </div>
      </Form>
      {details?.content_translations?.length > 0 && (
        <div className="mt-5">
          <TranslationTable details={details} />
        </div>
      )}
    </div>
  );
};

export default TranslationTab;
