import React, { memo } from 'react';
import { useTable } from './context';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { useTranslation } from 'react-i18next';

function RowsExpander() {
  const { rows, setExpandedRows, expandedRows } = useTable();
  const { t } = useTranslation();

  const toggle = (value: string) => {
    if (value === 'expand') {
      handleExpandAll();
    } else if (value === 'collapse') {
      handleCollapseAll();
    }
  };

  const handleExpandAll = () => {
    setExpandedRows(new Set(rows.map((row) => row.id.toString())));
  };

  const handleCollapseAll = () => {
    setExpandedRows(new Set());
  };

  return (
    <Tabs defaultValue={'collapse'} className="rtl:text-right" onValueChange={(value) => toggle(value)}>
      <TabsList>
        <TabsTrigger value="collapse">{t('collapse')}</TabsTrigger>
        <TabsTrigger value="expand">{t('expand')}</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}

export default memo(RowsExpander);
