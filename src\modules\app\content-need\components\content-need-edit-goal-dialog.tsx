import { Modal } from '@/index';
import { Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { contentNeedsAtom } from '../../tools/store/contnet-need';
import { Button } from '@/components/ui/button';
import { useAddContentNeedGoal, useEditContentNeedGoal } from '@/modules/app/content-need/apis/queries';
import { useAtom } from 'jotai';

const ContentNeedEditGoalDialog = ({
  isOpen,
  setIsOpen,
  goalBeingEdited,
}: {
  isOpen: boolean;
  setIsOpen: (x: boolean) => void;
  goalBeingEdited: { id: string | null; value: string };
}) => {
  const [data] = useAtom(contentNeedsAtom);
  const { t } = useTranslation();
  const { form, setFieldValue } = useForm({
    title: goalBeingEdited?.value || '',
  });
  const { mutate: editGoal, isPending: isEditing } = useEditContentNeedGoal();
  const { mutate: addGoal, isPending: isAdding } = useAddContentNeedGoal();
  const handleSubmit = () => {
    if (goalBeingEdited.id !== null) {
      // Edit existing goal
      editGoal(
        { id: goalBeingEdited.id, ...form },
        {
          onSuccess: () => {
            setIsOpen(false);
          },
        }
      );
    } else {
      addGoal(
        { content_id: data.id, ...form },
        {
          onSuccess: () => {
            setIsOpen(false);
          },
        }
      );
    }
  };

  return (
    <Modal
      open={isOpen}
      onOpenChange={setIsOpen}
      modalHeader={t(goalBeingEdited.id !== null ? 'editGoal.title' : 'addGoal.title')}
    >
      <Form onSubmit={handleSubmit} className="flex flex-col">
        <TextInput name="title" label={t('editGoal.goal')} value={form.title} onChange={setFieldValue('title')} />
        <Button
          loading={isEditing || isAdding}
          disabled={isEditing || isAdding}
          type="submit"
          className="mt-4 ml-auto w-fit"
        >
          {t(goalBeingEdited.id !== null ? 'save' : 'add')} {/* Switch button label */}
        </Button>
      </Form>
    </Modal>
  );
};

export default ContentNeedEditGoalDialog;
