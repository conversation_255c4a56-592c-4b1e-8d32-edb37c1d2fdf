import { useNotify } from '@/hooks';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  confrimAImage,
  deleteMedia,
  editImage,
  generateImage,
  getMedia,
  renameImage,
  replaceImage,
  uploadImage,
} from './endpoints';
import { t } from 'i18next';

export const useGetMedia = (id: string | number | undefined, modle: string) => {
  return useQuery({
    queryKey: ['media', id],
    queryFn: () => getMedia(id || '', modle),
  });
};

export const useUploadImage = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: uploadImage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      notify.success(t('media.imageUploadedSuccessfully'));
    },
    onError: () => {
      notify.error(t('media.imageUploadFailed'));
    },
  });
};

export const useReplaceImage = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: replaceImage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      notify.success(t('media.imageReplacedSuccessfully'));
    },
    onError: () => {
      notify.error(t('media.imageReplaceFailed'));
    },
  });
};

export const useDeleteMedia = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteMedia,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      notify.success(t('media.imageDeleted'));
    },
    onError: () => {
      notify.error(t('media.imageDeleteFailed'));
    },
  });
};

export const useGenerateImage = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: generateImage,
    onSuccess: () => {
      notify.success(t('media.imageGeneratedSuccessfully'));
    },
    onError: () => {
      notify.error(t('media.imageGenerationFailed'));
    },
  });
};
export const useEditImage = () => {
  const { notify } = useNotify();
  return useMutation({
    mutationFn: editImage,
    onSuccess: () => {
      notify.success(t('media.imageEditedSuccessfully'));
    },
    onError: () => {
      notify.error(t('media.imageGenerationFailed'));
    },
  });
};
export const useConftrimAImage = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();

  return useMutation({
    mutationFn: confrimAImage,
    onSuccess: () => {
      notify.success(t('media.imageGeneratedSuccessfully'));
      queryClient.invalidateQueries({ queryKey: ['dna-articulate-analysis'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
    },
    onError: () => {
      notify.error(t('media.imageGenerationFailed'));
    },
  });
};

export const useRenameImage = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: renameImage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['media'] });
      notify.success(t('media.imageRenamedSuccessfully'));
    },
    onError: () => {
      notify.error(t('media.imageRenameFailed'));
    },
  });
};
