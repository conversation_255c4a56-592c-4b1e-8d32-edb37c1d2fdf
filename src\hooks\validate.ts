import { useTranslation } from 'react-i18next';

export const useValidate = () => {
  const { t } = useTranslation();

  const isRequired = () => {
    return (value: any) => {
      return !!value ? null : t('validation.required');
    };
  };

  const isNotEmptyArray = () => {
    return (value: any[]) => {
      return value.length !== 0 ? null : 'This field is required';
    };
  };

  const isSelected = () => {
    return (value: any) => {
      return value?.length !== 0 ? null : 'This field is required';
    };
  };

  const isNotSpaces = () => {
    return (value: any) => {
      return value && value.trim().length === 0 ? `This field shouldn't be empty` : null;
    };
  };

  const minLength = (length: any) => {
    return (value: any) => {
      return value && value.length >= length ? null : `${t('validation.minLength')} ${length}`;
    };
  };

  const maxLength = (length: any) => {
    return (value: any) => {
      return value && value.length <= length ? null : `${t('validation.maxLength')} ${length}`;
    };
  };

  const validateRegex = (regex: any, errorMessage: string = '') => {
    return (value: any) => {
      return value && regex.test(value) && value && value.length >= length
        ? null
        : errorMessage || t(`validation.validateRegex`);
    };
  };

  const validatePasswordRegex = (regex: any, length: any = 0) => {
    return (value: any) => {
      return value && regex.test(value) ? null : ` ${t('validation.validatePasswordRegex')} ${length}`;
    };
  };
  const validateUrlRegex = (regex: any) => {
    return (value: any) => {
      return value && regex.test(value) ? null : ` ${t('validtion.url')}`;
    };
  };
  const isEmail = () => {
    return (value: any) => {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      return emailRegex.test(value) ? null : t('validation.isEmail');
    };
  };

  const isNumber = () => {
    return (value: any) => {
      return !isNaN(value) ? null : 'This field must be a number';
    };
  };

  return {
    isRequired,
    minLength,
    maxLength,
    validateRegex,
    isNotEmptyArray,
    // isNotEmptyString,
    validatePasswordRegex,
    // startAndEndWith,
    validateUrlRegex,
    isNotSpaces,
    isSelected,
    isEmail,
    isNumber,
    // Error message
    validate(value: any, validators: any = []) {
      // Validation Logic
      const results = validators.map((validator: any) => validator(value));

      //   Messages
      const errorMessage = results.every((item: any) => item === null)
        ? null
        : results.find((item: any) => item !== null);

      return errorMessage;
    },
  };
};
