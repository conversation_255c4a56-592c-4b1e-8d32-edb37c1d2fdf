import { useState } from 'react';
import { Icon } from '@/index';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { LearningOutcomeCustomeRow } from './learning-outcome-custome-row';
import { Button } from '@/components/ui/button';
import { currentStepAtom, programPlan<PERSON>tom } from '../../../store';
import ProgramEditDialog from './learning-outcome-edit';
import { useDeleteLearningOutcome } from '../../../apis/queries';
const LearningOutcomeTable = () => {
  //  State
  const [data] = useAtom(programPlanAtom);
  const [currentStep] = useAtom<number>(currentStepAtom);
  const [selectedRows, setSelectedRows] = useState<any>([]);
  // const [data] = useAtom(programPlanAtom);
  const [learningOutcomeDialogOpen, setLearningOutcomeDialogOpen] = useState(false);
  const [rowBeingEdited, setRowBeingEdited] = useState<{
    learning_outcome_id: number;
    outcome: string;
    learning_skill_type: number;
    program_id: number;
  }>({
    learning_outcome_id: 0,
    outcome: '',
    learning_skill_type: 0,
    program_id: 0,
  });

  //  Hooks
  const { t } = useTranslation();
  const { mutate: deleteLearning, isPending: isDeleting, variables } = useDeleteLearningOutcome();

  return (
    <>
      <div className="mt-[5.25rem] flex items-center justify-center ">
        {/* <div className="max-w-[1500px] w-full bg-background  p-6">
          <Table
            slots={{
              actions: (_: any, row: any) => {
                return (
                  <div className="flex">
                    <Button
                      size="icon"
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        setRowBeingEdited({
                          learning_outcome_id: row.id,
                          program_id: data.id,
                          outcome: row.outcome,
                          learning_skill_type: row.learning_skill_type,
                        });
                        setLearningOutcomeDialogOpen(true);
                      }}
                      className="border-primary"
                      variant={'ghost'}
                    >
                      <Icon icon="basil:edit-outline" width={25} className="text-primary cursor-pointer" />
                    </Button>

                    <Button
                      size="icon"
                      loading={isDeleting && variables.learning_outcome_id === row.id}
                      disabled={isDeleting && variables.learning_outcome_id === row.id}
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteLearning({ id: data.id, learning_outcome_id: row.id });
                      }}
                      variant={'ghost'}
                    >
                      <Icon icon="gg:trash" width={25} className="text-red-500 cursor-pointer" />
                    </Button>
                  </div>
                );
              },
            }}
            breakpoint={'lg'}
            ready={true}
            loading={false}
            rows={data.learning_outcomes || []}
            count={data.learning_outcomes?.length}
            title={t('contentNeed.steps.outcome.title')}
            jumbotron={false}
            searchInupt={false}
            customeRows={<LearningOutcomeCustomeRow id={data?.id} />}
            selectedRows={selectedRows}
            onSelectedRowsChange={setSelectedRows}
            isMultiSelectTable={false}
            columns={[
              {
                key: 'outcome',
                label: t('contentNeed.steps.outcome.title'),
              },
              {
                key: 'learning_skill_type',
                label: t('contentNeed.steps.skills.table.type'),
                width: '250px',
              },
              {
                key: 'actions',
                label: t('topicContentPage.table.actions'),
                width: '100px',
              },
            ]}
          />
          {learningOutcomeDialogOpen && (
            <ProgramEditDialog
              isOpen={learningOutcomeDialogOpen}
              setIsOpen={() => setLearningOutcomeDialogOpen(false)}
              rowBeingEdited={rowBeingEdited}
            />
          )}
        </div> */}
      </div>
    </>
  );
};

export default LearningOutcomeTable;
