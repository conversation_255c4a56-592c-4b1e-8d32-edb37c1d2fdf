import { Button } from '@/components/ui/button';
import { useEffect } from 'react';
import i18n from '@/utils/i18n';
import { useNavigate, useParams, useLocation } from 'react-router-dom';

export function LangToggle() {
  const { lng } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const updateLangAttribute = (lang: string) => {
    document.documentElement.setAttribute('lang', lang);
    if (lang === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }
  };

  const handleLanguageChange = (language: string) => {
    if (language !== lng) {
      i18n.changeLanguage(language);

      const currentSearchParams = location.search;
      const basePath = location.pathname.replace(`/${lng}`, `/${language}`);
      const newPath = `${basePath}${currentSearchParams}`;

      updateLangAttribute(language);
      localStorage.setItem('i18nextLng', language);
      navigate(newPath, { replace: true });
    }
  };

  useEffect(() => {
    const savedLanguage = i18n.language || 'ar';
    i18n.changeLanguage(savedLanguage).then(() => {
      updateLangAttribute(savedLanguage);
    });
    localStorage.setItem('i18nextLng', savedLanguage);
  }, [i18n.language]);

  return (
    <Button
      onClick={() => {
        if (i18n.language === 'en') {
          handleLanguageChange('ar');
        } else {
          handleLanguageChange('en');
        }
      }}
      variant="ghost"
      size="icon"
      className="capitalize text-gray-500"
    >
      {i18n.language === 'en' ? 'Ar' : 'En'}
    </Button>
  );
}
