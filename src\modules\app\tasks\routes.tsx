import { TaskMainLayout } from './layout';
import { ProtectedRoute } from '@/components';
import { lazyWithRetry } from '@/utils/lazy-retry';

const AllTasks = lazyWithRetry(() => import('./pages/all-tasks/list'));
const MyTasks = lazyWithRetry(() => import('./pages/my-tasks/list'));
const SingleTaskDna = lazyWithRetry(() => import('./pages/my-tasks/single-dna'));
const SingleTaskDnaAdmin = lazyWithRetry(() => import('./pages/all-tasks/single-dna'));
const SignleTaskReviwer = lazyWithRetry(() => import('./pages/my-tasks/signle-task'));
const SingleTask = lazyWithRetry(() => import('./pages/all-tasks/single'));

export default [
  {
    path: 'all-tasks',
    element: <TaskMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.allTasks',
        title: 'breadcrumb.myContentPage.allTasks',
      };
    },
    children: [
      {
        path: '',
        element: (
          <ProtectedRoute requiredPermissions={'task_index'}>
            <AllTasks />
          </ProtectedRoute>
        ),
      },
      {
        path: 'course/:taskId',
        element: (
          <ProtectedRoute requiredPermissions={'task_show'}>
            <SingleTask />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.myContentPage.allTasks.course',
            title: 'breadcrumb.myContentPage.allTasks.course',
          };
        },
      },

      {
        path: 'course/:taskId/dna/:dnaId',
        element: (
          <ProtectedRoute requiredPermissions={'task_show'}>
            <SingleTaskDnaAdmin />
          </ProtectedRoute>
        ),
        loader() {
          return {
            label: 'breadcrumb.myContentPage.myTasks.dna',
            title: 'breadcrumb.myContentPage.myTasks.dna',
          };
        },
      },
    ],
  },

  {
    path: 'my-tasks',
    element: <TaskMainLayout />,
    loader() {
      return {
        label: 'breadcrumb.myContentPage.myTasks',
        title: 'breadcrumb.myContentPage.myTasks',
      };
    },
    children: [
      {
        path: '',
        element: <MyTasks />,
      },
      {
        path: 'course/:taskId',
        element: <SignleTaskReviwer />,
        loader() {
          return {
            label: 'breadcrumb.myContentPage.allTasks.course',
            title: 'breadcrumb.myContentPage.allTasks.course',
          };
        },
      },

      {
        path: 'course/:taskId/dna/:dnaId',
        element: <SingleTaskDna />,
        loader() {
          return {
            label: 'breadcrumb.myContentPage.myTasks.dna',
            title: 'breadcrumb.myContentPage.myTasks.dna',
          };
        },
      },
    ],
  },
];
