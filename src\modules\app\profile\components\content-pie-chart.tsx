import { TrendingUp } from 'lucide-react';
import { Label, Pie, Pie<PERSON>hart } from 'recharts';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { IDashboardData } from '@/modules/auth/types';
import { useTranslation } from 'react-i18next';

export const ConetntPieChart = ({ data }: { data: IDashboardData }) => {
  const { t } = useTranslation();
  const chartData = [
    { type: 'dna', number: data?.dnas_count || 0, fill: 'var(--color-dna)' },
    { type: 'topic', number: data?.topics_count || 0, fill: 'var(--color-topic)' },
    { type: 'course', number: data?.courses_count || 0, fill: 'var(--color-course)' },
  ];

  const chartConfig = {
    number: {
      label: 'number',
    },
    dna: {
      label: t('breadcrumb.myContentPage.DNA'),
      color: 'hsl(264, 40%, 65%)',
    },
    topic: {
      label: t('breadcrumb.myContentPage.topics'),
      color: 'hsl(264, 60%, 40%)',
    },
    course: {
      label: t('breadcrumb.myContentPage.courses'),
      color: 'hsl(280, 55%, 48%)',
    },
  } satisfies ChartConfig;
  return (
    <Card className="flex flex-col xl:w-[290px]">
      <CardHeader className="items-center pb-0">
        <CardTitle className="font-medium">{t('pieChart.title')}</CardTitle>
        <CardDescription>{t('pieChart.description')}</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 pb-0">
        <ChartContainer config={chartConfig} className="mx-auto aspect-square max-h-[250px]">
          <PieChart>
            <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
            <Pie data={chartData} dataKey="number" nameKey="type" innerRadius={60} strokeWidth={5}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && 'cx' in viewBox && 'cy' in viewBox) {
                    return (
                      <text x={viewBox.cx} y={viewBox.cy} textAnchor="middle" dominantBaseline="middle">
                        <tspan x={viewBox.cx} y={viewBox.cy} className="fill-foreground text-3xl font-bold">
                          {data?.dnas_count + data?.topics_count + data?.courses_count}
                        </tspan>
                        <tspan x={viewBox.cx} y={(viewBox.cy || 0) + 24} className="fill-muted-foreground">
                          {t('pieChart.total')}
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
      </CardContent>
      <CardFooter className="flex-col gap-2 text-sm">
        <div className="flex items-center gap-2 font-medium leading-none">
          {t('pieChart.footer.title')} <TrendingUp className="h-4 w-4" />
        </div>
        <div className="leading-none text-muted-foreground">{t('pieChart.footer.description')}</div>
      </CardFooter>
    </Card>
  );
};
