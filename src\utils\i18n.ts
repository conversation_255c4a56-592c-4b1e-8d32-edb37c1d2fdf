import i18n from 'i18next';
import HttpBackend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { Api } from '@/services';
import { useHasPermission } from '@/modules/auth/store';
import { useAtom } from 'jotai';

function processTranslationKey(translationKey: string) {
  const parts = translationKey.split('.');
  const lastPart = parts[parts.length - 1];
  function splitCamelCase(word: string) {
    return word.replace(/([a-z])([A-Z])/g, '$1 $2').replace(/^[a-z]/, (match) => match.toUpperCase());
  }

  const formattedLastPart = splitCamelCase(lastPart);
  return formattedLastPart;
}

const VITE_API_BASE_URL = (import.meta as any).env.VITE_API_BASE_URL;
const missingKeyCache = new Set<string>();
i18n
  .use(HttpBackend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    fallbackLng: 'ar',
    detection: {
      order: ['cookie', 'localStorage', 'htmlTag'],
      caches: [],
    },
    backend: {
      loadPath: `${VITE_API_BASE_URL}/translations/{{lng}}`,
      addPath: `${VITE_API_BASE_URL}/locales/add/{{lng}}/{{ns}}`,
      parse: (data: any) => {
        const parsedData = JSON.parse(data);
        return parsedData.data;
      },
    },
    missingKeyHandler: async (lng, ns, key, fallbackValue) => {
      const hasPermission = localStorage.getItem('cross_validation') === 'true';
      const currentLocale = localStorage.getItem('i18nextLng');
      const cacheKey = `${lng}-${ns}-${key}`;
      if (missingKeyCache.has(cacheKey) || !hasPermission) {
        return;
      }
      missingKeyCache.add(cacheKey);
      // try {
      //   const response = await Api.post(`${VITE_API_BASE_URL}/translation-keys`, {
      //     key: key,
      //     translations: [
      //       { language_id: 1, value: processTranslationKey(key) },
      //       { language_id: 2, value: processTranslationKey(key) },
      //     ],
      //   });

      //   if (response.status === 200 && currentLocale) {
      //     await i18n.reloadResources(lng, ns);
      //     await i18n.changeLanguage(currentLocale);
      //   }
      // } catch (error) {
      // }
    },
    saveMissing: false,
    saveMissingTo: 'all',
    interpolation: {
      escapeValue: false,
    },
    supportedLngs: ['ar', 'en'],
  });

export default i18n;
