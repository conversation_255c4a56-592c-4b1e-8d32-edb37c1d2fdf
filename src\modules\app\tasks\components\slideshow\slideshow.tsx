import { Icon } from '@/components';
import { Button } from '@/components/ui/button';
import { useGetSlideShowById, useUpdateSlideshowData } from '../../apis/queries';
import SingleSlideEditor from './single-slide-editor';
import SwiperPresentation from './presentation';
import { useEffect, useState } from 'react';
import SlidesReorderSheet from './slides-reorder-sheet';
import { t } from 'i18next';
import { useGetSlideshowPlay, useReGenerateDnaSlideshow } from '../../apis/queries';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { useParams } from 'react-router-dom';
import { useAtom, useSetAtom } from 'jotai';
import {
  finalSlidesAtom,
  selectedSlideshowAudioLanguageAtom,
  selectedSlideshowIdAtom,
  selectedSlideshowSlidesLanguageAtom,
  selectedSlideshowSubtitleLanguageAtom,
  slideshowPlayData<PERSON>tom,
} from '../../store';
import ShareDialog from './share-dialog';
import { ProtectedTaskComponent } from '../protected-task-component';

const SingleSlideShow = ({ singleSlideshow, setSelectedSlide }: { singleSlideshow: any; setSelectedSlide: any }) => {
  const [isPlayerOpen, setIsPlayerOpen] = useState(false);

  const { taskId } = useParams();
  const { data } = useGetSlideShowById({
    taskCode: taskId || '',
    dnaId: singleSlideshow?.dna_id,
    slideshowId: singleSlideshow?.id,
  });
  const { data: slideshowPlayData } = useGetSlideshowPlay({
    taskCode: taskId || '',
    dnaId: singleSlideshow?.dna_id,
    slideshowId: singleSlideshow?.id,
  });
  const [_, setSlideshowPlayData] = useAtom(slideshowPlayDataAtom);
  const setSlidesLang = useSetAtom(selectedSlideshowSlidesLanguageAtom);
  const setSlidesAudio = useSetAtom(selectedSlideshowAudioLanguageAtom);
  const setSlidesSubtitle = useSetAtom(selectedSlideshowSubtitleLanguageAtom);
  const setSelectedSlideshowId = useSetAtom(selectedSlideshowIdAtom);
  const [finalSlides] = useAtom(finalSlidesAtom);

  useEffect(() => {
    if (slideshowPlayData) {
      setSlideshowPlayData(slideshowPlayData);
      setSlidesLang(singleSlideshow?.language?.id);
      setSlidesAudio(singleSlideshow?.language?.id);
      setSlidesSubtitle(singleSlideshow?.language?.id);
      setSelectedSlideshowId(singleSlideshow?.id);
    }
  }, [slideshowPlayData]);
  const { mutate, isPending } = useUpdateSlideshowData();
  const { mutate: reGenerate, isPending: isRegenerating } = useReGenerateDnaSlideshow();
  const { confirm } = useConfirmation();

  const handelReGenerate = () => {
    confirm({
      variant: 'info',
      title: t('sliddeshow.reGenetrate.confirmation.title'),
      description: t('sliddeshow.approve.reGenetrate.description'),
      onConfirm: () =>
        reGenerate(
          { taskCode: taskId || '', dnaId: singleSlideshow?.dna_id, slideshow_id: singleSlideshow?.id },
          { onSuccess: () => setSelectedSlide(null) }
        ),
    });
  };

  if (!data) return <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={30} />;
  return (
    <div className="relative">
      <SlidesReorderSheet slides={data?.slides || []} slideshowId={data?.id} dnaId={data?.dna_id} />
      <div className="flex justify-between">
        <Button variant="outline" className=" flex items-center gap-3" onClick={() => setSelectedSlide(null)}>
          <Icon icon="solar:arrow-left-linear" className="mt-1 rtl:rotate-180" width={20} />
          {t('slideshow.backToSlideshows')}
        </Button>

        <div className="flex gap-3">
          <ShareDialog uuid={singleSlideshow.uuid} />
          <ProtectedTaskComponent requiredPermissions={'dna_slideshow_show'}>
            <Button onClick={() => setIsPlayerOpen(true)} variant={'outline'}>
              {t('slideshow.playPresentation')}
            </Button>
          </ProtectedTaskComponent>
          <ProtectedTaskComponent requiredPermissions={'dna_slideshow_create'}>
            <Button onClick={handelReGenerate} loading={isRegenerating} variant={'outline'}>
              {t('slideshow.regenerateSlides')}
            </Button>
          </ProtectedTaskComponent>
          <ProtectedTaskComponent requiredPermissions={'dna_slideshow_edit'}>
            <Button
              disabled={isPending}
              loading={isPending}
              onClick={() =>
                mutate({
                  taskCode: taskId || '',
                  slideshow_id: data.id,
                  dnaId: data.dna_id,
                  status: 'reviewed',
                })
              }
            >
              {t('slideshow.finishReviewingSlides')}
            </Button>
          </ProtectedTaskComponent>
        </div>
      </div>
      <div className="space-y-7 mx-[10%] mt-7">
        {data &&
          data?.slides?.map((slide: any, index: number) => {
            return (
              <SingleSlideEditor
                key={index}
                slide={slide}
                nextSlideId={data?.slides[index + 1]?.id || null}
                dnaId={data.dna_id}
                slideshowId={data.id}
                slideNumber={index + 1}
              />
            );
          })}
      </div>
      {isPlayerOpen && (
        <SwiperPresentation
          onOpenChange={() => setIsPlayerOpen(false)}
          isOpen={isPlayerOpen}
          slides={finalSlides || []}
        />
      )}
    </div>
  );
};

export default SingleSlideShow;
