import { Card, Icon, Modal, Textarea } from '@/components';
import { Button } from '@/components/ui/button';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { userAtom } from '@/modules/auth/store';
import { Form, TextInput, ComboboxInput } from '@/components';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';
import { useParams } from 'react-router-dom';
import { useForm, useNotify } from '@/hooks';
import { useEditImage, useGenerateImage, useUploadImage } from '../../modules/app/dna/apis/media/queries';
import { cn } from '@/lib/utils';
import { DnaEnums } from '@/services';
import { useTranslation } from 'react-i18next';
import { Label } from '../ui/label';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  defaultPrompt?: string;
}

const ImageGenerationDialog = ({ open, onOpenChange, defaultPrompt }: IProps) => {
  // States
  const [prompt, setPrompt] = useState('');
  const [history, setHistory] = useState<{ prompt: string; url: string }[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('generate');
  const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const [user] = useAtom(userAtom);
  const { mutate: uploadImage, isPending: isUploading } = useUploadImage();

  // Metadata states
  const { data: allmetaData } = useGetSingleMetadata('');
  const subjectOptions = allmetaData?.filter((item) => item.type === 'subject') || [];
  const audienceOptions = allmetaData?.filter((item) => item.type === 'audience') || [];
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];
  const drawingStyles = allmetaData?.filter((item) => item.type === 'drawing_style') || [];

  // Hooks
  const { mutate: generateImage, isPending: isGenerating } = useGenerateImage();
  const { mutate: editImage, isPending: isEditing } = useEditImage();
  const { dnaId } = useParams();
  const { notify } = useNotify();
  const { t, i18n } = useTranslation();

  // Forms
  const { form: firstPromptForm, setFieldValue: setInitialFieldValue } = useForm({
    prompt: '',
    drawing_style_id: '',
    dimensions: '',
    quality: '',
    model: '',
  });

  const { form, setFieldValue } = useForm({
    file: null,
    url: '',
    dna_id: dnaId,
    media_name: '',
    audience_id: null,
    language_id: null,
    subject_id: null,
    prompt: '',
    drawing_style_id: firstPromptForm.drawing_style_id,
    dimensions: firstPromptForm.dimensions,
    quality: firstPromptForm.quality,
    model: firstPromptForm.model,
  });

  const tabs = [
    {
      title: t('Generate'),
      mode: 'generate',
    },
    {
      title: t('Edit'),
      mode: 'edit',
    },
  ];

  const handleImageGeneration = (promptText: string) => {
    if (!promptText.trim()) {
      notify.error(t('media.promptRequired'));
      return;
    }

    generateImage(
      {
        prompt: promptText,
        drawing_style_id: firstPromptForm.drawing_style_id,
        quality: firstPromptForm.quality,
        dimensions: firstPromptForm.dimensions,
        model: firstPromptForm.model,
      },
      {
        onSuccess: (data, { prompt }) => {
          setHistory((prev) => [...prev, { prompt, url: data }]);
          setPrompt(prompt);
          setFieldValue('url')(data);
          setFieldValue('prompt')(prompt);
          setCurrentIndex(history.length);
          setFieldValue('drawing_style_id')(firstPromptForm.drawing_style_id);
          setFieldValue('dimensions')(firstPromptForm.dimensions);
          setFieldValue('quality')(firstPromptForm.quality);
          setFieldValue('model')(firstPromptForm.model);
        },
      }
    );
  };

  const handleImageEdit = (promptText: string) => {
    if (!promptText.trim()) {
      notify.error(t('media.promptRequired'));
      return;
    }

    editImage(
      {
        prompt: promptText,
        drawing_style_id: firstPromptForm.drawing_style_id,
        image_url: form.url,
        image_uuid: firstPromptForm.uuid,
      },
      {
        onSuccess: (data, { prompt }) => {
          setHistory((prev) => [...prev, { prompt, url: data }]);
          setPrompt(prompt);
          setFieldValue('url')(data);
          setFieldValue('prompt')(prompt);
          setCurrentIndex(history.length);
          setFieldValue('drawing_style_id')(firstPromptForm.drawing_style_id);
          setFieldValue('quality')(firstPromptForm.quality);
        },
      }
    );
  };

  const handleSelectTab = (tab: string) => {
    if (tab === 'edit') {
      setActiveTab(tab);
      setInitialFieldValue('model')('gpt-image-1');
    } else {
      setActiveTab(tab);
    }
  };

  const handleInitialPrompt = () => {
    handleImageGeneration(firstPromptForm.prompt);
  };

  const handleGenerateImage = () => {
    handleImageGeneration(prompt);
  };

  const handleEditImage = () => {
    handleImageEdit(prompt);
  };

  const handleImageClick = (index: number, prompt: string, url: string) => {
    setCurrentIndex(index);
    setPrompt(prompt);
    setFieldValue('url')(url);
    setFieldValue('prompt')(prompt);
  };

  const handleDeleteHistoryItem = (index: number, e: React.MouseEvent) => {
    e.stopPropagation();

    const newHistory = history.filter((_, i) => i !== index);
    setHistory(newHistory);

    // Adjust current index if necessary
    if (currentIndex === index) {
      // If deleting current item, go to the previous item or first item
      const newIndex = index > 0 ? index - 1 : 0;
      setCurrentIndex(newIndex);
      if (newHistory[newIndex]) {
        setPrompt(newHistory[newIndex].prompt);
        setFieldValue('url')(newHistory[newIndex].url);
        setFieldValue('prompt')(newHistory[newIndex].prompt);
      }
    } else if (currentIndex > index) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleImagePreview = (imageUrl: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setPreviewImageUrl(imageUrl);
    setImagePreviewModal(true);
  };

  const handleSubmit = () => {
    uploadImage(form, {
      onSuccess: () => {
        notify.success(t('media.uploadSuccess'));
        onOpenChange(false);
      },
      onError: (error) => {
        notify.error(error.message || t('common.error'));
      },
    });
  };
  useEffect(() => {
    if (defaultPrompt) {
      setInitialFieldValue('prompt')(defaultPrompt);
      setFieldValue('prompt')(defaultPrompt);
      setActiveTab('generate');
    }
  }, [defaultPrompt]);

  return (
    <Modal open={open} onOpenChange={onOpenChange} width={history.length > 0 ? '1100px' : '850px'}>
      {history.length > 0 ? (
        <Form id="image-generation-form" onSubmit={handleSubmit}>
          <Textarea
            value={prompt}
            onChange={(value: string) => setPrompt(value)}
            placeholder="Enter a creative prompt..."
            className="p-3 border rounded-lg shadow-sm resize-none"
            rows={4}
          />
          <div className="grid grid-cols-8 gap-5 w-full py-5">
            {/* Sidebar */}
            <div className="col-span-2 max-h-[500px] overflow-y-auto border border-border rounded-md p-4">
              <div className="flex flex-col-reverse gap-1.5">
                {history.map((item, index) => (
                  <Card
                    key={index}
                    className={cn('p-2 rounded-lg flex items-center gap-2 cursor-pointer relative group', {
                      'outline-1 outline outline-primary': currentIndex === index,
                    })}
                    onClick={() => handleImageClick(index, item.prompt, item.url)}
                  >
                    <img src={item.url} alt={`History ${index}`} className="w-10 h-10 rounded-md" />
                    <p className="text-sm truncate w-full">{item.prompt}</p>
                    {history.length > 1 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        type="button"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white"
                        onClick={(e) => handleDeleteHistoryItem(index, e)}
                      >
                        <Icon icon="gg:trash" width={12} />
                      </Button>
                    )}
                  </Card>
                ))}
              </div>
            </div>
            <div className="w-full col-span-2 border border-border rounded-md flex flex-col h-full">
              <div className="p-4 space-y-2 flex-1 overflow-auto">
                <ComboboxInput
                  name="model"
                  label={t('analysis.models')}
                  placeholder={t('analysis.dimensions.models')}
                  options={DnaEnums.aiImageModels}
                  value={firstPromptForm.model}
                  disabled={activeTab === 'edit'}
                  onChange={setInitialFieldValue('model')}
                  dropIcon
                />
                <ComboboxInput
                  name={t('analysis.drawingStyles')}
                  label={t('analysis.drawingStyles')}
                  placeholder={t('analysis.drawingStyles.placeholder')}
                  options={generateEnum(drawingStyles || [], 'id', 'name_en')}
                  value={firstPromptForm.drawing_style_id}
                  onChange={setInitialFieldValue('drawing_style_id')}
                />
                <ComboboxInput
                  name="dimensions"
                  label={t('analysis.dimensions')}
                  placeholder={t('analysis.dimensions.placeholder')}
                  options={
                    firstPromptForm.model === 'gpt-image-1' ? DnaEnums.gptImageDimensions : DnaEnums.dallImageDimensions
                  }
                  value={firstPromptForm.dimensions}
                  onChange={setInitialFieldValue('dimensions')}
                  dropIcon
                />
                <ComboboxInput
                  name="quality"
                  label={t('analysis.quality')}
                  placeholder={t('analysis.quality.placeholder')}
                  options={
                    firstPromptForm.model === 'gpt-image-1'
                      ? DnaEnums.gptImageQualityOptions
                      : i18n.language === 'en'
                      ? DnaEnums.dallImageQualityOptionsEnglish
                      : DnaEnums.dallImageQualityOptionsArabic
                  }
                  value={firstPromptForm.quality}
                  onChange={setInitialFieldValue('quality')}
                  dropIcon
                />

                <div className="flex flex-col gap-2 justify-start mt-3">
                  <Label>{t('fileManager.tabs.mode')}</Label>
                  <div className="w-fit border border-border flex rounded-md">
                    {tabs.map((tab, index) => (
                      <div
                        key={tab.title}
                        onClick={() => handleSelectTab(tab.mode)}
                        className={cn(
                          'cursor-pointer p-2.5 truncate px-5 text-sm font-medium relative after:opacity-0 transition-all duration-300 after:content-[""] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:h-0.5 after:w-8 after:bg-primary',
                          activeTab === tab.mode ? 'bg-primary/5 text-primary after:opacity-100' : 'text-[#6B7280]',
                          index > 0 && 'border-s border-border'
                        )}
                      >
                        {t(tab.title)}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="p-4 border-t border-border">
                <Button
                  type="button"
                  loading={isGenerating || isEditing}
                  onClick={() => (activeTab === 'generate' ? handleGenerateImage() : handleEditImage())}
                  className="w-full"
                >
                  {activeTab === 'generate' ? t('media.generate') : t('media.edit')}
                </Button>
              </div>
            </div>
            {/* Main Content */}
            <div className="w-full col-span-4 h-[500px] border border-border rounded-md p-5">
              <div className="grid grid-rows-5 h-full">
                <div className="row-span-3 border border-border rounded-lg relative group">
                  <img
                    src={history[currentIndex]?.url || '/placeholder.png'}
                    alt="Generated"
                    className="rounded-lg w-full h-full object-contain"
                  />
                  {history[currentIndex]?.url && (
                    <Button
                      variant="ghost"
                      type="button"
                      size="sm"
                      className="absolute top-2 right-2 h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-primary"
                      onClick={(e) => handleImagePreview(history[currentIndex]?.url, e)}
                    >
                      <Icon icon="lucide:expand" width={20} />
                    </Button>
                  )}
                </div>
                <div className="grid gap-2 row-span-2 items-end">
                  <div className="grid grid-cols-2 gap-2">
                    <TextInput
                      placeholder={t('media.name')}
                      name="media_name"
                      label={t('media.name')}
                      value={form.media_name}
                      onChange={setFieldValue('media_name')}
                      // validators={[isRequired()]}
                    />
                    <ComboboxInput
                      placeholder={t('media.language')}
                      name="language_id"
                      label={t('media.language')}
                      options={generateEnum(languageOptions, 'id', 'name_en')}
                      value={form.language_id}
                      onChange={setFieldValue('language_id')}
                      // validators={[isRequired()]}
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-2 items-center">
                    <ComboboxInput
                      placeholder={t('media.subject')}
                      name="subject_id"
                      label={t('media.subject')}
                      options={generateEnum(subjectOptions, 'id', 'name_en')}
                      value={form.subject_id}
                      onChange={setFieldValue('subject_id')}
                      // validators={[isRequired()]}
                    />
                    <ComboboxInput
                      placeholder={t('media.audience')}
                      name="audience_id"
                      label={t('media.audience')}
                      options={generateEnum(audienceOptions, 'id', 'name_en')}
                      value={form.audience_id}
                      onChange={setFieldValue('audience_id')}
                      // validators={[isRequired()]}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-end border-t border-border pt-5">
            <Button type="submit" className="min-w-28 flex items-center gap-2" loading={isUploading}>
              <Icon icon="solar:upload-minimalistic-bold" width={19} />
              {t('media.upload')}
            </Button>
          </div>
        </Form>
      ) : (
        <Form
          id="initial-prompt-form"
          onSubmit={handleInitialPrompt}
          className="w-full rounded-xl h-[600px] flex flex-col justify-center px-10 text-lg"
        >
          <div>
            <h1 className="text-3xl font-semibold bg-gradient-to-r from-black to-primary inline-block text-transparent bg-clip-text">
              {t('media.greeting', { name: user?.name })}
            </h1>
            <p className="text-slate-600 mt-1 text-sm">{t('media.promptInstructions')}</p>
          </div>
          <div className="relative mt-8 border border-border rounded-md">
            <Textarea
              value={firstPromptForm.prompt}
              onChange={setInitialFieldValue('prompt')}
              placeholder={t('media.promptPlaceholder')}
              className="p-3 border-none rounded-lg shadow-sm resize-none"
              rows={6}
            />
          </div>
          <div className="grid grid-cols-2 gap-3">
            <ComboboxInput
              name="model"
              label={t('analysis.models')}
              placeholder={t('analysis.dimensions.models')}
              options={DnaEnums.aiImageModels}
              value={firstPromptForm.model}
              onChange={setInitialFieldValue('model')}
              dropIcon
            />
            <ComboboxInput
              name={t('analysis.drawingStyles')}
              label={t('analysis.drawingStyles')}
              placeholder={t('analysis.drawingStyles.placeholder')}
              options={generateEnum(drawingStyles || [], 'id', 'name_en')}
              value={firstPromptForm.drawing_style_id}
              onChange={setInitialFieldValue('drawing_style_id')}
            />
            <ComboboxInput
              name="dimensions"
              label={t('analysis.dimensions')}
              placeholder={t('analysis.dimensions.placeholder')}
              options={
                firstPromptForm.model === 'gpt-image-1' ? DnaEnums.gptImageDimensions : DnaEnums.dallImageDimensions
              }
              value={firstPromptForm.dimensions}
              onChange={setInitialFieldValue('dimensions')}
              dropIcon
            />
            <ComboboxInput
              name="quality"
              label={t('analysis.quality')}
              placeholder={t('analysis.quality.placeholder')}
              options={
                firstPromptForm.model === 'gpt-image-1'
                  ? DnaEnums.gptImageQualityOptions
                  : i18n.language === 'en'
                  ? DnaEnums.dallImageQualityOptionsEnglish
                  : DnaEnums.dallImageQualityOptionsArabic
              }
              value={firstPromptForm.quality}
              onChange={setInitialFieldValue('quality')}
              dropIcon
            />
          </div>
          <div className="flex justify-center mt-6">
            <Button type="submit" form="initial-prompt-form" loading={isGenerating} className="flex gap-2">
              <Icon icon="mage:stars-a-fill" width={20} className="cursor-pointer" />
              {t('generate')}
            </Button>
          </div>
        </Form>
      )}

      <Modal open={imagePreviewModal} onOpenChange={setImagePreviewModal} width="1000px" className="max-w-none">
        <div className="flex flex-col items-center justify-center p-4">
          <div className="relative max-w-full max-h-[80vh] overflow-hidden">
            <img src={previewImageUrl} alt="Full Size Preview" className="max-w-full max-h-full object-contain" />
          </div>
        </div>
      </Modal>
    </Modal>
  );
};

export default ImageGenerationDialog;
