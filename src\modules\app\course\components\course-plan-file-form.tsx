import { useState, useEffect, useRef } from 'react';
import { saveAs } from 'file-saver';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Form, useForm, useValidate, Icon, Textarea, useNotify, TextInput, DnaEnums } from '@/index';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import { generateEnum } from '@/utils/helpers';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { Button } from '@/components/ui/button';

const CoursePlanFileForm = ({ courseData, onSubmit, id, error, counterToResetForm }: any) => {
  const [loading, setLoading] = useState(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [errorMessage, setErrorMessage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { notify } = useNotify();
  const { data: allmetaData } = useGetSingleMetadata('');
  const subjectOptions = allmetaData?.filter((item) => item.type === 'subject') || [];
  const audienceOptions = allmetaData?.filter((item) => item.type === 'audience') || [];
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];
  const difficultyLevelOptions = allmetaData?.filter((item) => item.type === 'difficulty_level') || [];
  const creditHoursOptions = allmetaData?.filter((item) => item.type === 'credit_hours') || [];

  const { t, i18n } = useTranslation();

  const { isRequired } = useValidate();
  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';
  const initialState = {
    version: 'v.1.02',
    file: '',
    info: '',
    subject_id: '',
    audience_id: '',
    language_id: '',
    difficulty_level_id: '',
    credit_hours_id: '',
    model: 'gpt-4.1',
  };

  const { form, setFieldValue, setFormValue } = useForm(initialState);

  useEffect(() => {
    if (courseData) {
      setFormValue({
        version: 'v.1.02',
        file: courseData?.file,
        info: courseData?.info,
        subject_id: courseData?.subject?.id,
        audience_id: courseData?.audience?.id,
        language_id: courseData?.language?.id,
        bloom_tax_id: courseData?.blomm_tax?.id,
        duration: courseData?.duration,
        difficulty_level_id: courseData?.difficultyLevel?.id,
        credit_hours_id: courseData?.creditHours?.id,
        model: courseData?.model,
      });
    }
  }, [courseData]);

  const handleCoursePlanFormSubmittionFile = async (event: Event) => {
    if (form.file === '') {
      setErrorMessage(true);
    } else {
      setErrorMessage(false);
      event.preventDefault();
      onSubmit(form, {
        onSuccess: (data: any) => {
          setSearchParams({ courseId: data.id, tab: 'file-form' }, { replace: true });
        },
      });
    }
  };
  const downloadFile = () => {
    const fileUrl = `/course plan template.xlsx`;
    saveAs(fileUrl, 'course plan template.xlsx');
  };

  const handleFileUpload = (event: any) => {
    setLoading(true);
    const file = event.target.files[0];
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
      'application/vnd.ms-excel', // .xls
    ];
    if (file && !allowedTypes.includes(file.type)) {
      notify.error(t('dnaCreationPage.form.file.validation'));
      event.target.value = '';
    } else {
      const formData = new FormData();
      formData.append('dna', file);
      setFieldValue('file')(file);
    }
    setLoading(false);
  };

  useEffect(() => {
    if (counterToResetForm) {
      setFormValue(initialState);
    }
  }, [counterToResetForm]);

  return (
    <Form id={id} onSubmit={handleCoursePlanFormSubmittionFile}>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 lg:block space-y-4 " dir={i18n.dir()}>
        <div className="self-center">
          <div className="mb-2 block">
            <Label htmlFor="file-upload">{t('uploadFile')}</Label>
          </div>
          <div>
            <div className="relative">
              <Input
                ref={fileInputRef}
                className={`absolute inset-0 w-full h-full opacity-0 cursor-pointer ${
                  error?.message === 'Invalid data in the Excel file.' ? 'border-[.5px] border-red-500' : ''
                }`}
                type="file"
                id="file-upload"
                accept=".xlsx"
                onChange={handleFileUpload}
              />

              <div className="flex items-center gap-2">
                <Button type="button" onClick={() => fileInputRef.current?.click()}>
                  {t('courseCreation.file.chooseFile')}
                </Button>
                <span className="text-sm text-gray-500">
                  {form?.file?.name || t('courseCreation.file.selectedFile')}
                </span>
              </div>
            </div>
            <span className="block text-sm text-gray-500 mt-2">
              {loading ? (
                'Your file is being uploading please wait'
              ) : (
                <div>
                  <p>{t('Accept')}: xlsx. </p>
                  {t('Click')}{' '}
                  <span onClick={downloadFile} className="text-blue-500 cursor-pointer">
                    {' '}
                    {t('here')}{' '}
                  </span>
                  {t('dnaRewrite.dowload')}
                  <div>
                    {error?.message === 'Invalid data in the Excel file.' && (
                      <div className="flex gap-3 mt-2 text-red-500">
                        {' '}
                        <div>
                          <p> invalid data in the Excel file</p>
                          <p> see what you did wrong</p>
                        </div>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger type="button" className="self-center">
                              <Icon icon="solar:info-circle-broken" width={18} />
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-72">
                              {error?.errors.map((error: any) => (
                                <p className="my-2">{error}</p>
                              ))}
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                    )}
                  </div>
                </div>
              )}{' '}
            </span>
          </div>
          {errorMessage && <p className="text-red-500">{t('Please Upload file')}</p>}
        </div>
        <Textarea
          name="info"
          label={t('Course title')}
          placeholder={t('CoursePlanCreationPage.form.titlePLaceholder')}
          rows={6}
          value={form.info}
          onChange={setFieldValue('info')}
          isRequired
          validators={[isRequired()]}
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.subject')}
          name="subject_id"
          label={t('CoursePlanCreationPage.form.subject')}
          options={generateEnum(subjectOptions, 'id', labelKey)}
          value={form.subject_id}
          onChange={setFieldValue('subject_id')}
          validators={[isRequired()]}
          dropIcon
        />
        {form.subject === 'Other (write a subject)' && (
          <TextInput
            name="otherSubject"
            label="Other Subject"
            placeholder="Enter other subject"
            value={form.other_subject}
            onChange={setFieldValue('other_subject')}
            isRequired
            validators={[isRequired()]}
          />
        )}
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.audience')}
          name="audience_id"
          label={t('CoursePlanCreationPage.form.audience')}
          options={generateEnum(audienceOptions, 'id', labelKey)}
          value={form.audience_id}
          onChange={setFieldValue('audience_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.language')}
          name="language_id"
          label={t('CoursePlanCreationPage.form.language')}
          options={generateEnum(languageOptions, 'id', labelKey)}
          value={form.language_id}
          onChange={setFieldValue('language_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.creditHours')}
          name="credit_hours_id"
          label={t('CoursePlanCreationPage.form.creditHours')}
          options={generateEnum(creditHoursOptions, 'id', labelKey)}
          value={form.credit_hours_id}
          onChange={setFieldValue('credit_hours_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          placeholder={t('CoursePlanCreationPage.form.difficultyLevel')}
          name="difficulty_level_id"
          label={t('CoursePlanCreationPage.form.difficultyLevel')}
          options={generateEnum(difficultyLevelOptions, 'id', labelKey)}
          value={form.difficulty_level_id}
          onChange={setFieldValue('difficulty_level_id')}
          validators={[isRequired()]}
          dropIcon
        />
        <ComboboxInput
          name="aiBaseModel"
          label={t('dnaCreationPage.form.aiBaseModel')}
          placeholder={t('dnaCreationPage.form.aiBaseModel')}
          options={DnaEnums.ai_base_model}
          value={form.model}
          onChange={setFieldValue('model')}
          dropIcon
          disabled
          validators={[isRequired()]}
        />
      </div>
    </Form>
  );
};

export default CoursePlanFileForm;
