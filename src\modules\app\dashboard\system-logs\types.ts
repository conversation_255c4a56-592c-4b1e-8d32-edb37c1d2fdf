interface User {
  id: number;
  name: string;
  email: string;
}

interface LogEntry {
  attribute: string;
  old: string | null;
  new: string | null;
}

export interface IDnaLogData {
  id: number;
  log_name: string;
  action: string;
  description: string;
  model_type: string;
  model_id: number;
  user_type: string;
  user: User;
  logs: LogEntry[];
  created_at: string; // ISO 8601 date string
}
