import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Icon } from '@/components';
import { t } from 'i18next';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { useDeleteMedia, useRenameImage } from '../../modules/app/dna/apis/media/queries';
import { IMedia } from '@/modules/app/common/types';
import RenameImageDialog from './rename-image-dialog';
import ImageUploaderDialog from './image-uploader-dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import ImageReplaceDialog from './image-replace-dialog';
import ImageEditOrReplaceDialog from './image-replace-with-ai-dialog.tsx';

const ImagesActionToolbar = ({ selectedImage, setSelectedImage }: { selectedImage: IMedia; setSelectedImage: any }) => {
  // states
  const [isRenameImageDialogOpen, setIsRenameImageDialogOpen] = useState(false);
  const [isUploadImageDialogOpen, setIsUploadImageDialogOpen] = useState(false);
  const [isImageReplaceWithAiDialogOpen, setIsImageReplaceWithAiDialogOpen] = useState(false);
  const [isEditingImageWithAi, setIsEditingImageWithAi] = useState(false);

  // hooks
  const { confirm } = useConfirmation();
  const { mutate: mutateDelete, isPending: isDeleting } = useDeleteMedia();
  const { mutate: renameImage, isPending: isRenaming } = useRenameImage();
  // Functions

  const handleDownloadImage = async (item: IMedia) => {
    try {
      const response = await fetch(`${item.url}?download=${Date.now()}`);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = item.name || `download-${item.uuid}-${Date.now()}`;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
    }
  };

  const handleDeleteImage = (item: IMedia) => {
    confirm({
      variant: 'destructive',
      title: t('fileManager.replaceMedia.delete.title'),
      description: t('fileManager.replaceMedia.delete.description'),
      onConfirm: () => {
        mutateDelete(
          { dna_id: item.model_id, media_name: item.name, media_uuid: item.uuid },
          {
            onSuccess: () => {
              setSelectedImage(null);
            },
          }
        );
      },
    });
  };

  const handleOpenImageReplaceWithAiDialog = () => {
    confirm({
      variant: 'destructive',
      title: t('fileManager.replaceMedia.confirmation.title'),
      description: t('fileManager.replaceMedia.confirmation.description'),
      onConfirm: () => setIsImageReplaceWithAiDialogOpen(true),
    });
  };

  const handleOpenImageReplaceWithFileDialog = () => {
    confirm({
      variant: 'destructive',
      title: t('fileManager.replaceMedia.confirmation.title'),
      description: t('fileManager.replaceMedia.confirmation.description'),
      onConfirm: () => setIsUploadImageDialogOpen(true),
    });
  };

  const handleRenameImage = (name: string) => {
    renameImage(
      {
        dna_id: selectedImage.model_id,
        media_uuid: selectedImage.uuid,
        media_name: name,
      },
      {
        onSuccess: () => {
          setSelectedImage(null);
        },
      }
    );
    setIsRenameImageDialogOpen(false);
  };

  const handleReplaceImage = (selectedImage: IMedia) => {
    setIsUploadImageDialogOpen(true);
    setSelectedImage(selectedImage);
  };
  return (
    <div className="flex items-center gap-2">
      <Button
        size={'sm'}
        variant={'ghost'}
        className="flex items-center gap-2 min-w-24"
        onClick={() => handleDownloadImage(selectedImage)}
      >
        <Icon icon="solar:download-minimalistic-outline" width={20} />
        {t('media.download')}
      </Button>

      <Button
        size={'sm'}
        variant={'ghost'}
        className="flex items-center gap-2 min-w-24"
        loading={isDeleting}
        disabled={isDeleting}
        onClick={() => handleDeleteImage(selectedImage)}
      >
        <Icon icon="solar:trash-bin-minimalistic-broken" width={20} />
        {t('media.delete')}
      </Button>

      <DropdownMenu>
        <DropdownMenuTrigger className="ms-auto">
          <Button size={'sm'} variant={'ghost'} className="flex items-center gap-2 min-w-24">
            <Icon icon="solar:video-frame-replace-outline" width={20} />
            {t('media.replace')}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem className="flex items-center gap-2 min-w-24" onClick={handleOpenImageReplaceWithFileDialog}>
            <Icon icon="solar:upload-minimalistic-bold" width={19} />
            {t('media.upload')}
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="flex items-center gap-2 min-w-24" onClick={handleOpenImageReplaceWithAiDialog}>
            <Icon icon="hugeicons:ai-magic" width="22" />
            {t('media.editOrReplaceWithAi')}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Button
        onClick={() => setIsRenameImageDialogOpen(true)}
        size={'sm'}
        variant={'ghost'}
        className="flex items-center gap-2 min-w-24"
        loading={isRenaming}
      >
        <Icon icon="lucide:edit-3" width={19} />
        {t('media.rename')}
      </Button>
      {isRenameImageDialogOpen && (
        <RenameImageDialog
          isOpen={isRenameImageDialogOpen}
          onOpenChange={setIsRenameImageDialogOpen}
          onSubmit={(name) => handleRenameImage(name)}
          initialName={selectedImage.name || ''}
        />
      )}
      {isUploadImageDialogOpen && (
        <ImageReplaceDialog
          open={isUploadImageDialogOpen}
          onOpenChange={() => {
            setIsUploadImageDialogOpen(false);
            setSelectedImage(null);
          }}
          selectedImage={selectedImage}
        />
      )}

      {isImageReplaceWithAiDialogOpen && (
        <ImageEditOrReplaceDialog
          open={isImageReplaceWithAiDialogOpen}
          onOpenChange={() => {
            setIsImageReplaceWithAiDialogOpen(false);
            setSelectedImage(null);
          }}
          selectedImage={selectedImage}
        />
      )}
      {isEditingImageWithAi && (
        <ImageEditOrReplaceDialog
          open={isEditingImageWithAi}
          onOpenChange={() => {
            setIsEditingImageWithAi(false);
            setSelectedImage(null);
          }}
          isEditing
          selectedImage={selectedImage}
        />
      )}
    </div>
  );
};

export default ImagesActionToolbar;
