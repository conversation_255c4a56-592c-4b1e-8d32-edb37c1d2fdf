import { But<PERSON><PERSON><PERSON><PERSON>, Downcast<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Editor } from 'ckeditor5';

const ltrIcon =
  '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="m21 18l-4-4v3H5v2h12v3M9 10v5h2V4h2v11h2V4h2V2H9a4 4 0 0 0-4 4a4 4 0 0 0 4 4"/></svg>';
const rtlIcon =
  '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M8 17v-3l-4 4l4 4v-3h12v-2m-10-7v5h2V4h2v11h2V4h2V2h-8a4 4 0 0 0-4 4a4 4 0 0 0 4 4"/></svg>';

/**
 * The plugin function.
 * CKEditor expects plugins to have a `pluginName` property.
 */
export function DirectionPlugin(editor: any) {
  extendSchema(editor);
  setupConversion(editor);
  registerCommands(editor);
  createButtons(editor);
}

DirectionPlugin.pluginName = 'DirectionPlugin';

function extendSchema(editor: any) {
  const schema = editor.model.schema;
  const directions = ['ltr', 'rtl', 'auto'];

  schema.extend('$block', { allowAttributes: ['dir'] });
  schema.extend('$container', { allowAttributes: ['dir'] });

  // Handle special cases.
  ['table', 'listItem', 'blockQuote'].forEach((item) => {
    if (schema.isRegistered(item)) {
      schema.extend(item, { allowAttributes: ['dir'] });
    }
  });
}

const setupConversion = (editor: Editor) => {
  // Downcast conversion
  editor.conversion.for('downcast').add((dispatcher) => {
    dispatcher.on<DowncastAttributeEvent>('attribute:dir', (evt, data, conversionApi) => {
      if (!conversionApi.consumable.consume(data.item, evt.name)) return;

      const viewWriter = conversionApi.writer;
      const element = conversionApi.mapper.toViewElement(data?.item as any);

      if (element) {
        viewWriter.setAttribute('dir', data.attributeNewValue, element);
      }
    });
  });

  // Upcast conversion
  editor.conversion.for('upcast').add((dispatcher) => {
    dispatcher.on<any>('element:dir', (evt, data, conversionApi) => {
      const modelElement = conversionApi.mapper.toModelElement(data.viewItem);

      if (modelElement) {
        conversionApi.writer.setAttribute('dir', data.viewItem.getAttribute('dir'), modelElement);
      }
    });
  });
};

function registerCommands(editor: any) {
  editor.commands.add('dir:ltr', createDirectionCommand(editor, 'ltr'));
  editor.commands.add('dir:rtl', createDirectionCommand(editor, 'rtl'));
  editor.commands.add('dir:auto', createDirectionCommand(editor, 'auto'));
}

function createButtons(editor: any) {
  createButton(editor, 'ltr', ltrIcon, 'Left-to-Right');
  createButton(editor, 'rtl', rtlIcon, 'Right-to-Left');
}

function createButton(editor: any, direction: string, icon: string, label: string) {
  editor.ui.componentFactory.add(`dir:${direction}`, (locale: any) => {
    const command = editor.commands.get(`dir:${direction}`);
    const button = new ButtonView(locale);

    button.set({
      label,
      icon,
      tooltip: true,
      isToggleable: true,
      class: `ck-direction-${direction}`,
    });

    button.bind('isOn', 'isEnabled').to(command, 'value', 'isEnabled');

    button.on('execute', () => {
      editor.execute(`dir:${direction}`);
      editor.editing.view.focus();
    });

    return button;
  });
}

/**
 * Creates a command object that implements the necessary interface:
 * – properties: value, isEnabled, direction
 * – methods: refresh(), execute()
 */
function createDirectionCommand(editor: any, direction: string) {
  const defaultDirection = editor.config.get('language.direction') || 'ltr';

  return {
    direction,
    value: false,
    isEnabled: true,

    refresh() {
      const model = editor.model;
      const selection = model.document.selection;
      const blocks: any = Array.from(selection.getSelectedBlocks());

      // Set value to true only if every block has the given direction.
      this.value = blocks.length > 0 && blocks.every((block: any) => block.getAttribute('dir') === direction);

      // Enable command if at least one block is eligible.
      this.isEnabled = blocks.some((block: any) => {
        if (block.is('element', 'table') || block.is('element', 'listItem')) {
          return model.schema.checkAttribute(block, 'dir');
        }
        return true;
      });
    },

    execute() {
      const model = editor.model;
      const selection = model.document.selection;
      const blocks: any = Array.from(selection.getSelectedBlocks());
      const shouldRemove = blocks.every((block: any) => block.getAttribute('dir') === direction);

      model.change((writer: any) => {
        blocks.forEach((block: any) => {
          if (shouldRemove) {
            writer.removeAttribute('dir', block);

            // Restore default direction if needed.
            if (editor.config.get('directionFeatures.resetToDefault')) {
              writer.setAttribute('dir', defaultDirection, block);
            }
          } else {
            writer.setAttribute('dir', direction, block);
          }
        });
      });

      // Handle inline selections.
      if (blocks.length === 0 && selection.hasAttribute('dir')) {
        const range = selection.getFirstRange();

        model.change((writer: any) => {
          if (shouldRemove) {
            writer.removeAttribute('dir', range);
          } else {
            writer.setAttribute('dir', direction, range);
          }
        });
      }
    },
  };
}
