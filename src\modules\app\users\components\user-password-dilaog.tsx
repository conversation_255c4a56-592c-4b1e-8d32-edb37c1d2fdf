import { useState } from 'react';

import { Form, useForm, useValidate, Regex, TextInput, Icon, Modal } from '@/index';

import { useUpdatePassword } from '@/modules/app/users/apis/queries';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

const UserPasswordDialog = ({ data, onOpen, onClose, onFinish }: any) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const toggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword((prevShowPassword) => !prevShowPassword);
  };

  const { isRequired, minLength, maxLength, validatePasswordRegex } = useValidate();
  const { t } = useTranslation();

  const { form, setFieldValue } = useForm({
    password: '',
    password_confirmation: '',
  });

  const { mutate: updatePassword, isPending } = useUpdatePassword();

  const handleGenerate = async () => {
    updatePassword(
      { id: data.id, payload: form },
      {
        onSuccess: () => {
          onFinish();
          onClose();
        },
      }
    );
  };

  return (
    <Modal open={onOpen} onOpenChange={onClose} modalHeader={t('userPage.dialogHeaders.updatePassword')}>
      <Form onSubmit={handleGenerate}>
        <div className="grid grid-cols-1  gap-4">
          <div className="flex w-full">
            <div className="w-full">
              <TextInput
                name={'password'}
                label={t('password')}
                placeholder={t('password')}
                type={showPassword ? 'text' : 'password'}
                value={form.password}
                onChange={setFieldValue('password')}
                validators={[isRequired(), validatePasswordRegex(Regex.password, 8), maxLength(50)]}
              />
            </div>

            <div className="mt-8" onClick={() => togglePasswordVisibility()}>
              <Icon
                className="ltr:ml-3 rtl:mr-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                width="25"
                icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
              />
            </div>
          </div>

          <div className="flex w-full">
            <div className="w-full">
              <TextInput
                name={'confirmPassword'}
                label={t('confirmPassword')}
                placeholder={t('confirmPassword')}
                type={showConfirmPassword ? 'text' : 'password'}
                value={form.password_confirmation}
                onChange={setFieldValue('password_confirmation')}
                validators={[isRequired()]}
              />
              {form.password !== form.password_confirmation && form.password_confirmation ? (
                <label className="text-red-500 text-sm">Confirm password doesn't match the password</label>
              ) : null}
            </div>

            <div className="mt-8" onClick={() => toggleConfirmPasswordVisibility()}>
              <Icon
                className="ltr:ml-3 rtl:mr-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                width="25"
                icon={!showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
              />
            </div>
          </div>
          <div className="flex justify-end gap-3">
            <Button
              disabled={isPending}
              onClick={() => onClose(false)}
              variant={'outline'}
              className="min-w-[100px] mt-4"
            >
              {t('cancel')}
            </Button>
            <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
              {t('update')}
            </Button>
          </div>
        </div>
      </Form>
    </Modal>
  );
};

export default UserPasswordDialog;
