import { Api } from '@/services';
import type { GenerateSlideshowParams, AddSlideParams, ISlideShow, ISingleSlideShow } from './types';
import { ILanguage } from '@/modules/app/dashboard/transaltion/types';

export const getSharedSlideshow = async (
  id: string | number
): Promise<{ items: any; additional_data: ILanguage[] }> => {
  const { data } = await Api.get(`/lms/share/slideshow/${id}`);
  return data?.data || [];
};

export const generateSlideshow = async ({ id, slideshow_sound, language_id }: GenerateSlideshowParams) => {
  const { data } = await Api.post(`/dna/slideshow/${id}`, { slideshow_sound, language_id });
  return data.data;
};

export const addSlide = async ({
  id,
  prompt,
  template,
  voice_over_sound,
  next_slide_id,
  slideshow_id,
}: AddSlideParams) => {
  const { data } = await Api.post(`/dna/slideshow/${id}/slides/add-slide`, {
    template,
    prompt,
    next_slide_id,
    slideshow_id,

    voice_over_sound,
  });
  return data.data;
};

export const listSlideshow = async (id: number): Promise<ISlideShow[] | []> => {
  const { data } = await Api.get(`/dna/slideshow/list/${id}`);
  return data?.data || [];
};

export const singleSlideShow = async (id: number, langId: number): Promise<ISingleSlideShow> => {
  const { data } = await Api.get(`/dna/slideshow/${id}/${langId}`);
  return data?.data || {};
};

export const deleteSlide = async ({
  id,
  payload,
}: {
  id: number;
  payload: { slideshow_id: Number; slide_id: Number; _method: String };
}) => {
  const { data } = await Api.post(`/dna/slideshow/${id}/slides/delete-slide`, { ...payload });
  return data?.data || [];
};

export const getAllowedLanguages = async (id: number | string): Promise<IMetadata[] | []> => {
  const { data } = await Api.get(`/dna/slideshow/${id}/allowed-languages`);
  return data?.data || [];
};

export const getTranslatedSlideshowAllowedLanguages = async ({
  dnaId,
  slideshowId,
}: {
  dnaId: number | string;
  slideshowId: number | string;
}): Promise<IMetadata[] | []> => {
  const { data } = await Api.get(`/dna/slideshow/${dnaId}/translation/${slideshowId}/allowed-languages`);
  return data?.data || [];
};

export const deleteSlideshow = async ({ dnaId, slideshow_id }: { dnaId: number; slideshow_id: number }) => {
  const { data } = await Api.post(`/dna/slideshow/${dnaId}`, { slideshow_id, _method: 'delete' });
  return data.data;
};

export const updateSlideshow = async ({
  dnaId,
  title,
  status,
  slideshow_id,
}: {
  dnaId: number;
  title?: string;
  status?: string;
  slideshow_id: number;
}) => {
  const { data } = await Api.post(`/dna/slideshow/${dnaId}`, { slideshow_id, title, status, _method: 'put' });
  return data.data;
};

export const addTransaltionSlideshow = async ({
  id,
  slideshow_id,
  language_id,
  model,
}: {
  id: number;
  slideshow_id?: string;
  language_id: number;
  model: string;
}) => {
  const { data } = await Api.post(`/dna/slideshow/${id}/translation/translate`, { slideshow_id, language_id, model });
  return data.data;
};
