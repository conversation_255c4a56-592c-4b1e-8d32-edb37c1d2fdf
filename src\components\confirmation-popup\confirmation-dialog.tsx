import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
import { useConfirmation } from './hooks';
import { Icon } from '../icon';

// constants
const iconVariants: Record<string, { icon: string; middleCircleClass: string; mainCircleClass: string }> = {
  default: {
    icon: 'simple-line-icons:check',
    middleCircleClass: 'text-green-500 p-3 bg-green-500/10 rounded-full',
    mainCircleClass: 'text-green-500 p-3 bg-green-400/10 rounded-full',
  },
  destructive: {
    icon: 'ant-design:exclamation-circle-outlined',
    middleCircleClass: 'text-destructive p-3 bg-destructive/10 rounded-full',
    mainCircleClass: 'text-destructive p-3 bg-destructive/5 rounded-full',
  },
  info: {
    icon: 'quill:info',
    middleCircleClass: 'text-primary p-3 bg-primary/10 rounded-full',
    mainCircleClass: 'text-primary p-3 bg-primary/5 rounded-full',
  },
};

export const ConfirmationDialog = () => {
  const { isOpen, setIsOpen, config } = useConfirmation();

  return (
    <AlertDialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          config.onClose();
        }
        setIsOpen(open);
      }}
    >
      <AlertDialogContent className="max-w-[400px] p-5">
        <AlertDialogHeader className="text-center flex flex-col justify-center items-center">
          <div className={iconVariants[config.variant].mainCircleClass}>
            <div className={iconVariants[config.variant].middleCircleClass}>
              <Icon icon={iconVariants[config.variant].icon} width="28" />
            </div>
          </div>
          <AlertDialogTitle className="text-xl max-w-[300px] break-words whitespace-normal">
            {config.title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-base text-center">{config.description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="border-t border-border pt-4">
          <AlertDialogCancel className="w-full rounded-md" onClick={config.onCancel}>
            {config.cancelLabel}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={config.onConfirm}
            className={cn(
              'w-full rounded-md',
              config.variant === 'destructive' && 'bg-destructive text-destructive-foreground hover:bg-destructive/80'
            )}
          >
            {config.confirmLabel}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
