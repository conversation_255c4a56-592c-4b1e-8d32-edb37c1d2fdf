import { Icon } from '@/components/icon';
import React from 'react';
import { Button } from '@/components/ui/button';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useTranslation } from 'react-i18next';
interface Props {
  onApprove: () => void;
  onEdit: () => void;
  onGenerateXml: () => void;
  onRegenerate: () => void;
  onDelete: () => void;
  isLoading?: boolean;
  hasXml?: boolean;
}

const QuestionFloatingMenu = ({
  onApprove,
  onEdit,
  onGenerateXml,
  onRegenerate,
  onDelete,
  isLoading,
  hasXml,
}: Props) => {
  const [currentAction, setCurrentAction] = React.useState<string | null>(null);
  const { t } = useTranslation();
  const actions = [
    {
      icon: 'mynaui:check-square',
      label: 'activity.actions.approve',
      action: onApprove,
      iconColor: 'text-primary',
    },
    {
      icon: 'solar:pen-new-square-linear',
      label: 'activity.actions.edit',
      action: onEdit,
    },
    {
      icon: hasXml ? 'solar:copy-linear' : 'hugeicons:xml-01',
      label: hasXml ? 'activity.actions.copyXml' : 'activity.actions.generateXml',
      action: onGenerateXml,
    },
    {
      icon: 'mingcute:refresh-4-ai-line',
      label: 'activity.actions.regenerate',
      action: onRegenerate,
    },
    {
      icon: 'solar:trash-bin-minimalistic-linear',
      label: 'activity.actions.delete',
      action: onDelete,
      iconColor: 'text-destructive',
    },
  ];
  return (
    <div className="flex flex-col  p-2 border border-border rounded-lg h-fit">
      {actions.map((action) => (
        <TooltipProvider key={action.label} delayDuration={100}>
          <Tooltip delayDuration={100}>
            <TooltipTrigger>
              <Button
                key={action.icon}
                variant={'ghost'}
                size="icon"
                onClick={() => {
                  action.action();
                  setCurrentAction(action.label);
                }}
                disabled={isLoading}
                loading={isLoading && currentAction === action.label}
                className="gap-2"
              >
                <Icon icon={action.icon} width="24" height="24" className={action.iconColor} />
              </Button>
            </TooltipTrigger>
            <TooltipContent align="center" side="left" className="bg-gray-800 text-white text-sm">
              {t(action.label)}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      ))}
    </div>
  );
};

export default QuestionFloatingMenu;
