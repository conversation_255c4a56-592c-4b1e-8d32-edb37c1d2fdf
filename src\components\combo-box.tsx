import * as React from 'react';
import { Check, ChevronsUpDown } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';

interface IProps {
  placeholder: string;
  options: any;
  disabled?: boolean;
  onChange: (value: string) => void;
  value: string | number;
  name?: string;
}

export const Combobox = ({ placeholder, options, disabled, onChange, value, name }: IProps) => {
  const [open, setOpen] = React.useState(false);
  const { t } = useTranslation();

  const handleSelect = (value: string) => {
    onChange(value);
    setOpen(false);
  };

  // Check if any option has email data to determine display mode (Used for enhanced search in the table)
  const hasEmailData = options?.some((option: any) => option.email);

  return (
    <div className="space-y-2">
      {name && <Label htmlFor={name}>{t(name)}</Label>}
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            disabled={disabled}
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full rounded-md min-w-[170px] justify-between truncate max-w-full shadow"
          >
            {value ? (
              t(options.find((option: any) => option.value == value)?.label)
            ) : (
              <div className="text-muted-foreground truncate max-w-full">{placeholder}</div>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent align="start" className="p-0 max-h-[--radix-popover-content-available-height] w-full">
          <Command className="w-full">
            <CommandInput placeholder={placeholder} />
            <CommandList>
              <CommandEmpty>{t('comboBox.search')}</CommandEmpty>
              {options.length > 0 && (
                <ScrollArea style={options.length <= 5 ? { height: '100px' } : { height: '175px' }}>
                  <CommandGroup>
                    {options.map((option: any) => (
                      <CommandItem
                        key={option.value}
                        value={
                          hasEmailData
                            ? option.searchValue || `${option.label} ${option.email || ''}`.toLowerCase()
                            : option.label
                        }
                        onSelect={() => {
                          handleSelect(option.value);
                        }}
                      >
                        <Check
                          className={cn('mr-2 h-4 w-4 min-w-4', value === option.value ? 'opacity-100' : 'opacity-0')}
                        />
                        {hasEmailData ? (
                          <div className="flex flex-col items-start min-w-0 flex-1">
                            <span className="whitespace-nowrap max-w-64 truncate font-medium">{t(option.label)}</span>
                            {option.email && (
                              <span className="text-xs text-muted-foreground whitespace-nowrap max-w-64 truncate">
                                {option.email}
                              </span>
                            )}
                          </div>
                        ) : (
                          <span className="whitespace-nowrap max-w-64 truncate">{t(option.label)}</span>
                        )}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </ScrollArea>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  );
};
