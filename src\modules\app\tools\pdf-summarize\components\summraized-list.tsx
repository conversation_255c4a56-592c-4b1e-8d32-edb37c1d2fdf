import { Icon } from '@/components';
import { TableContent, TableContentBody } from '@/components/theTable';
import Table from '@/components/theTable/table';
import { useNotify } from '@/hooks';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

const SummarizedPdflist = ({ list }: { list: string[] }) => {
  const { t } = useTranslation();
  const { notify } = useNotify();

  const handelCopyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      notify.success(t('promptCopied'));
    } catch (err) {
      notify.error(t('copyFailed'));
    }
  };

  const columns: ITableColumn<any>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('summary.table.points'),
        width: '180px',
        cell: ({ row }) => <p>{row}</p>,
      },
      {
        accessorKey: 'actions',
        header: t('summary.table.actions'),
        width: '20px',
        cell: ({ row }) => (
          <div className="flex justify-start">
            <Icon
              icon="tabler:copy"
              width={25}
              className="text-primary cursor-pointer"
              onClick={() => handelCopyToClipboard(row)}
            />
          </div>
        ),
      },
    ];
  }, [t]);

  return (
    <div>
      <Table rows={list} columns={columns}>
        <TableContent>
          <TableContentBody />
        </TableContent>
      </Table>
    </div>
  );
};

export default SummarizedPdflist;
