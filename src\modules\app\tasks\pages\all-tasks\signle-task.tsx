import { useGetTaskByCodeAdmin, useReOpenTask } from '@/modules/app/tasks/apis/queries';
import { useParams } from 'react-router-dom';
import { Icon, ProtectedComponent } from '@/index';
import { ICourse, ICourseTopic } from '@/modules/app/course/types';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  dateAndTimeFormat,
  formatUserByNameAndEmail,
  getContentStatusStyle,
  getTaskStatusStyle,
} from '@/utils/helpers';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import EditDna from '@/modules/app/components/edit-dna';
import { ConditionalComponent } from '@/components/conditional-status-component';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Button } from '@/components/ui/button';
import AssignTaskDialog from '@/modules/app/course/components/assign-task-dialog';
import StatusClass from '@/utils/SystemStatusEnums';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import UpdateTaskDialog from '@/modules/app/course/components/update-task-dialog';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Table, TableContentBody, TableContent, TableContentHeader } from '@/components/theTable';
import TableColumsExpander from '@/components/theTable/table-colums-toggler';
import TableRowsExpander from '@/components/theTable/table-rows-expander';
import WorkflowStatusChecklist from '@/modules/app/components/work-flow-status-checklist';
import { IDNA } from '@/modules/app/dna/types';
import NotesDialog from '../../components/notes-dialog';
import { Separator } from '@/components/ui/separator';
import ListExpander from '@/modules/app/components/list-expander';
import { useApproveCoursesInReview } from '@/modules/app/course/apis/queries';
import { IUser } from '@/modules/auth/types';

const SignleTaskReviwer = () => {
  // State
  const { taskId } = useParams();
  const { data: task } = useGetTaskByCodeAdmin(taskId || '');
  const [editDialog, setEditDialog] = useState<any>(false);
  const [danData, setDanData] = useState<any>(null);
  const [notesDialog, setNotesDialog] = useState(false);
  const [dnaNotes, setDnaNotes] = useState<any>(null);
  const [isUpdateTaskDialogOpen, setIsUpdateTaskDialogOpen] = useState(false);
  const [isAssigningTaskDialogOpen, setIsAssigningTaskDialogOpen] = useState(false);
  const { confirm } = useConfirmation();
  const { mutate: reOpenTask, isPending: isReOpening, variables: reOpenTaskPayload } = useReOpenTask();
  const { t, i18n } = useTranslation();
  const { mutate: approveCourse, isPending: isApproving } = useApproveCoursesInReview();

  const allTopicsApproved = task?.content.tool_data?.topics?.every(
    (topic) => topic?.topic_status?.phase_with_status === 'Review: approved'
  );

  const columns: ITableColumn<ICourse & ICourseTopic & IDNA>[] = useMemo(() => {
    return [
      {
        accessorKey: 'title',
        header: t('CoursePlanCreationPage.table.title'),
        width: '350px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return (
                <div className="flex gap-2 items-start py-1 rtl:text-right">
                  <div>
                    <p className="font-medium">{row.title}</p>
                    <p className="text-gray-500">{row.learning_objectives}</p>
                  </div>
                </div>
              );
            case 1:
              const element = !row.dna_content ? (
                <span>{row.title}</span>
              ) : (
                <p
                  className="text-primary font-medium underline cursor-pointer"
                  onClick={() => navigate(`/app/all-tasks/course/${task?.code}/dna/${row.id}`)}
                >
                  {row.title}
                </p>
              );
              return (
                <div className="rtl:text-right">
                  <div className="flex gap-2 items-center p-0">
                    <div className={`break-words font-bold`}>{element}</div>
                  </div>
                  <p className="font-medium text-gray-500">{row.learning_objectives}</p>
                  <WorkflowStatusChecklist needsPermissions={false} dna={row} />
                </div>
              );
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'dnas',
        header: t('CoursePlanCreationPage.table.dnas'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return <div className="rtl:text-right">{row.dnas.length}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'length',
        header: t('CoursePlanCreationPage.table.lenght'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              const length = row.dnas.reduce(
                (acc, lesson) => acc + parseInt(lesson?.audio_length?.split('min')?.[0] || '0'),
                0
              );
              return (
                <div className="font-medium rtl:text-right">
                  <span>{length} min</span>
                </div>
              );
            case 1:
              return <div className="font-medium max-w-12 rtl:text-right">{row.audio_length}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'bloom_tax',
        header: t('CoursePlanCreationPage.table.bloomTax'),
        width: '100px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return null;
            case 1:
              return <div className="font-medium max-w-12 rtl:text-right">{row.bloom_tax?.name_en}</div>;
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'status',
        header: t('CoursePlanCreationPage.table.status'),
        width: '120px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              return getTaskStatusStyle(row?.topic_status);
            case 1:
              return getContentStatusStyle(row?.dna_status);
            default:
              return null;
          }
        },
      },
      {
        accessorKey: 'reviewer_notes',
        header: t('CoursePlanCreationPage.table.reviewerNotes'),
        width: '150px',
        cell: ({ row, level }) => {
          switch (level) {
            case 0:
              if (!row?.dnas) {
                return 'Loading...';
              }
              return row.dnas.filter((dna) => dna.reviewer_notes !== null).length;
            case 1:
              return (
                row.reviewer_notes && (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger>
                        <Icon
                          onClick={() => {
                            setNotesDialog(true), setDnaNotes(row);
                          }}
                          icon={'tabler:notes'}
                          width={25}
                          className="text-primary cursor-pointer"
                        />
                      </TooltipTrigger>
                      <TooltipContent> {t('myTaskTable.reviewerNotes')}</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )
              );
            default:
              return null;
          }
        },
      },
    ];
  }, [t, task]);

  // Hooks
  const navigate = useLanguageNavigate();

  const handelReopenTask = async () => {
    await confirm({
      variant: 'info',
      title: t('taskConfirmation.reopenTitle'),
      description: t('taskConfirmation.reopenDescription'),
      onConfirm: () => reOpenTask(task?.code as any),
    });
  };

  const handelApproveAllCousers = async () => {
    await confirm({
      variant: 'info',
      title: t('taskConfirmation.approveCourses'),
      description: t('taskConfirmation.approveCoursesDescription'),
      onConfirm: () => approveCourse(Number(task?.content?.tool_data?.id)),
    });
  };

  const infoItems = [
    {
      label: t('taskData.accordion.code'),
      value: task?.code,
    },
    {
      label: t('taskData.accordion.description'),
      value: task?.description,
    },
    {
      label: t('taskData.accordion.assignedTo'),
      value:
        formatUserByNameAndEmail(task?.assigned_to as Pick<IUser, 'name' | 'email'>) || task?.assigned_to?.name || '',
    },
    {
      label: t('taskData.accordion.priority'),
      value: task?.priority,
    },
    {
      label: t('taskData.accordion.dueDate'),
      value: dateAndTimeFormat(task?.due_date || '', i18n.language),
    },
    {
      label: t('taskData.accordion.taskStatus'),
      value: getContentStatusStyle(task?.task_status),
    },
    {
      label: t('taskData.accordion.initiatedBy'),
      value: task?.initiated_by ? formatUserByNameAndEmail(task?.initiated_by) : '',
    },
    {
      label: t('taskData.accordion.operations'),
      value: task?.operations ? (
        <ListExpander initialVisiableItems={2} list={task?.operations.map((data) => data.name)} />
      ) : (
        ''
      ),
    },
  ];

  return (
    <div>
      {/* {task?.content?.tool_data?.courseStatus.phase_with_status === StatusClass.COURSE.REVIEW.PRODUCTION && (
        <AlertCopmponent description={t('taskSinglePage.Singletask.taskAlert')} />
      )} */}
      <div className=" bg-white dark:bg-background shadow-sm rounded-md my-4">
        <Accordion type="single" collapsible>
          <AccordionItem className="border-none" value="item-1">
            <AccordionTrigger className="font-light hover:no-underline gap-5 bg-muted rounded-t-md border border-border p-4">
              <div className="flex justify-between w-full">
                <h3 className="text-xl flex capitalize items-center gap-1">{task?.content?.tool_data?.title}</h3>
                <p className="text-sm">{getContentStatusStyle(task?.content?.tool_data?.courseStatus)}</p>
              </div>
              <Separator orientation="vertical" className="h-8" />
            </AccordionTrigger>
            <AccordionContent className="p-4">
              <div className="grid grid-cols-2  md:grid-cols-3 gap-4">
                {infoItems.map((item, index) => (
                  <div key={index} className="space-y-1">
                    <p className="opacity-50">{item.label}</p>
                    <p>{item.value || '-'}</p>
                  </div>
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>

      <div>
        <Table
          rows={task?.content?.tool_data?.topics || []}
          columns={columns}
          nestedConfig={{
            enabled: true,
            childProperties: ['dnas'],
          }}
        >
          <TableContent>
            <TableContentHeader className="mt-0 flex justify-between">
              <div className="flex items-center gap-2">
                <TableColumsExpander />
                <p>{t('breadcrumb.myContentPage.DNAs')}</p>
                <TableRowsExpander />
              </div>
              <div className="flex items-center gap-2">
                <ProtectedComponent requiredPermissions={'task_edit'}>
                  {task?.can_reopen_task && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            loading={task?.code === reOpenTaskPayload && isReOpening}
                            size="icon"
                            variant="outline"
                            onClick={() => handelReopenTask()}
                            disabled={
                              task?.content?.tool_data?.courseStatus.phase_with_status ===
                              StatusClass.COURSE.REVIEW.PRODUCTION
                            }
                          >
                            <Icon
                              className="text-primary"
                              icon="material-symbols-light:assignment-return-rounded"
                              width={25}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('cousrePlanContentPage.reopen')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  {task?.content.tool_data.tasks.can_create_new_task && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="outline"
                            onClick={() => {
                              setIsAssigningTaskDialogOpen(true);
                            }}
                            disabled={
                              task?.content?.tool_data?.courseStatus.phase_with_status ===
                              StatusClass.COURSE.REVIEW.PRODUCTION
                            }
                          >
                            <Icon
                              className="text-primary"
                              icon="material-symbols:assignment-add-outline-rounded"
                              width={27}
                            />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('cousrePlanContentPage.assignTask')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  {task?.can_update_task && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            size="icon"
                            variant="outline"
                            onClick={() => {
                              setIsUpdateTaskDialogOpen(true);
                            }}
                          >
                            <Icon className="text-primary" icon="hugeicons:task-edit-01" width={27} />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t('cousrePlanContentPage.updateTask')}</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </ProtectedComponent>
                {allTopicsApproved && (
                  <ConditionalComponent
                    status={task?.content?.tool_data?.courseStatus}
                    wantedStatus={[StatusClass.COURSE.REVIEW.PRODUCTION]}
                  >
                    <Button onClick={handelApproveAllCousers} disabled={isApproving}>
                      {t('reviewTask.approveCourse')}
                    </Button>
                  </ConditionalComponent>
                )}
              </div>
            </TableContentHeader>
            <TableContentBody />
          </TableContent>
        </Table>
        {editDialog && (
          <EditDna
            onOpen={editDialog}
            onClose={() => {
              setEditDialog(false), setDanData(null);
            }}
            data={danData}
          />
        )}

        {isAssigningTaskDialogOpen && (
          <AssignTaskDialog
            onOpen={isAssigningTaskDialogOpen}
            onClose={() => {
              setIsAssigningTaskDialogOpen(false);
            }}
            course={task}
          />
        )}
        {isUpdateTaskDialogOpen && task && (
          <UpdateTaskDialog
            open={isUpdateTaskDialogOpen}
            onOpenChange={() => {
              setIsUpdateTaskDialogOpen(false);
            }}
            task={task}
          />
        )}
        {notesDialog && <NotesDialog isOpen={notesDialog} data={dnaNotes} setIsOpen={setNotesDialog} viewOnly />}
      </div>
    </div>
  );
};

export default SignleTaskReviwer;
