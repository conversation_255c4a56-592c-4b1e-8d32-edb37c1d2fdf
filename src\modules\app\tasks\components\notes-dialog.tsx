import { Modal, Form } from '@/index';
import { Editor } from '@/components/CKEditor';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useAddNotes } from '@/modules/app/tasks/apis/queries';
import { useState } from 'react';
import { IDNA } from '../../dna/types';
import { ITask } from '../types';
interface IProps {
  isOpen: boolean;
  setIsOpen: any;
  data: IDNA;
  tool?: ITask;
  viewOnly?: boolean;
}

const NotesDialog = ({ isOpen, setIsOpen, data, tool, viewOnly }: IProps) => {
  const { mutate, isPending } = useAddNotes();
  const [reviewerText, setReviewerText] = useState(data.reviewer_notes || '');

  const { t } = useTranslation();

  // Functions
  const handleSumbit = async () => {
    mutate(
      {
        code: tool?.code || '',
        payload: {
          dna_id: data.id,
          reviewer_notes: reviewerText,
        },
      },
      { onSuccess: () => setIsOpen(false) }
    );
  };

  return (
    <Modal width={1000} open={isOpen} onOpenChange={setIsOpen} modalHeader={t('addnotes.title')}>
      <Form className="space-y-5" onSubmit={handleSumbit}>
        <Editor
          editorContent={reviewerText}
          setEditorContent={setReviewerText}
          language={data?.language}
          readOnly={viewOnly}
        />
        {!viewOnly && (
          <Button type="submit" loading={isPending} disabled={isPending} className="min-w-[100px]">
            {t('submit')}
          </Button>
        )}
      </Form>
    </Modal>
  );
};

export default NotesDialog;
