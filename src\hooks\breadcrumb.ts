import { useMatches } from 'react-router-dom';
import useLanguageNavigate from './use-lang-navigation';

export const useBreadcrumb = () => {
  // Composables
  const navigate = useLanguageNavigate();
  const matches = useMatches();

  // Routes
  const routes = matches.filter((match) => !!match.data);

  // Methods
  const handleRouteClick = (e: any, path: any) => {
    e.preventDefault();

    navigate(path);
  };

  const currentRoute = routes[routes.length - 1];

  return {
    routes,
    currentRoute,
    handleRouteClick,
  };
};
