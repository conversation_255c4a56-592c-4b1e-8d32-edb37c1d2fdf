import { Form, Modal, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';

const ActivityDialog = ({
  isOpen,
  onOpenChange,
  contentId,
  onSubmit,
  isLoading,
}: {
  isOpen: boolean;
  onOpenChange: (x: boolean) => void;
  contentId: number | string;
  onSubmit: (x: any) => void;
  isLoading: boolean;
}) => {
  const { t } = useTranslation();

  const { form, setFieldValue } = useForm({
    case_study: 0,
    fill_in_blank: 0,
    content_id: contentId,
    mcq: 0,
    t_f: 0,
    content_type: 'Dna',
  });

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('activityModal.title')} width={400}>
      <Form onSubmit={() => onSubmit(form)}>
        <div className="grid grid-cols-1 items-center gap-4">
          <TextInput
            name="caseStudy"
            label={t('activityModal.caseStudy')}
            type="number"
            value={form.case_study}
            onChange={setFieldValue('case_study', Number)}
            min={0}
            max={50}
          />
          <TextInput
            name="trueOrFalse"
            label={t('activityModal.trueOrFalse')}
            placeholder="Enter duration.."
            type="number"
            value={form.t_f}
            onChange={setFieldValue('t_f', Number)}
            min={0}
            max={50}
          />
          <TextInput
            name="MultiChoise"
            label={t('activityModal.multipleChoiceQuestions')}
            placeholder="Enter duration.."
            type="number"
            value={form.mcq}
            onChange={setFieldValue('mcq', Number)}
            min={0}
            max={50}
          />
          <TextInput
            name="fitb"
            label={t('activityModal.fillintheBlank')}
            type="number"
            value={form.fill_in_blank}
            onChange={setFieldValue('fill_in_blank', Number)}
            min={0}
            max={50}
          />
        </div>
        <div className="flex justify-end gap-3 mt-2">
          <Button className="w-full mt-4" type="submit" disabled={isLoading} loading={isLoading}>
            {t('create')}
          </Button>
          <Button onClick={() => onOpenChange(false)} variant="outline" className="min-w-[100px] mt-4">
            {t('cancel')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ActivityDialog;
