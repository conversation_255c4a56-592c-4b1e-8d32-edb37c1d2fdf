import { useConfirmDialog, useNotify, DELETE_TOPICS, Api, DELETE_DNA, GENERATE_DNA_FORM_TOPIC } from '@/index';
import { Icon } from '@/index';
import { CoursePlanCustomeRow } from './course-plan-custome-row';
import { ICourseTopic, ICourseTopicDNA } from '@/modules/app/course/types';
import { useState } from 'react';
import { useGenerateAllDnasInTopic } from '@/modules/app/course/apis/queries';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { programPlanAtom } from '../../store';
import { useQueryClient } from '@tanstack/react-query';
import EditDna from '@/modules/app/components/edit-dna';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { getTaskStatusStyle } from '@/utils/helpers';
import { Button } from '@/components/ui/button';
const CoursePlanStep = ({ onFinish }: { onFinish: any }) => {
  // State
  const [isLoading, setIsLoading] = useState<any>(false);
  const [editDialog, setEditDialog] = useState<any>(false);
  const [danData, setDanData] = useState<any>(null);
  const [data] = useAtom(programPlanAtom);

  // Hooks
  const { showConfirm, hideConfirm } = useConfirmDialog();
  const { notify } = useNotify();
  const { mutate: generateAllTopicDnas, isPending, variables } = useGenerateAllDnasInTopic();
  const navigate = useLanguageNavigate();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const generateDna = async (id: any) => {
    try {
      setIsLoading((prev: any) => ({ ...prev, [id]: true }));
      await Api.put(GENERATE_DNA_FORM_TOPIC, {
        id: id,
      });

      queryClient.invalidateQueries({ queryKey: ['programPlan'] });
      notify.success('DNA generated successfully');
    } catch (error) {
      notify.error('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const TopicConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('topicCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeletTopic = async (id: any) => {
    showConfirm(TopicConfirmText(), {
      danger: true,
      async onConfirm() {
        try {
          hideConfirm();
          await Api.delete(`${DELETE_TOPICS}/${id}`);

          queryClient.invalidateQueries({ queryKey: ['programPlan'] });
          notify.success(t('notify.topicDelect'));
        } catch (error) {
          notify.error('An error occurred. Please try again.');
        }
      },
    });
  };

  const ConfirmText = () => {
    return (
      <div>
        <div className="flex justify-center items-center mx-auto mb-3 text-gray-400 dark:text-gray-200">
          <Icon icon={'fluent:info-16-regular'} width={55} />
        </div>
        <p>{t('dnaCreationPage.confirmationText')}</p>
      </div>
    );
  };

  const handleDeleteDna = async (dnaId: any) => {
    showConfirm(ConfirmText(), {
      danger: true,
      async onConfirm() {
        try {
          hideConfirm();
          await Api.delete(`${DELETE_DNA}/${dnaId}`);

          queryClient.invalidateQueries({ queryKey: ['programPlan'] });
          notify.success(t('notify.dnaDeleted'));
        } catch (error) {
          notify.error('An error occurred. Please try again.');
        }
      },
    });
  };

  return (
    <div>
      {data.courses.map((course: any) => (
        <div className="shadow-sm [&_.table-header-large]:!hidden [&_.table-header-actions]:hidden [&_.main-table-content]:border-t-0 mt-12">
          {/* <Table
            title={course.title}
            isMultiSelectTable={false}
            ready={true}
            count={course?.topics?.length}
            loading={false}
            rows={course.topics}
            slots={{
              title: (_: any, row: ICourseTopic) => {
                return (
                  <div className="flex gap-2 items-start py-1 rtl:text-right">
                    <Icon
                      icon="octicon:chevron-down-12"
                      width={15}
                      className="text-gray-400 cursor-pointer expand-arrow mt-0.5"
                    />
                    <div>
                      <p className="font-bold">{row.title}</p>
                      <p className="font-medium">{row.learning_objectives}</p>
                    </div>
                  </div>
                );
              },

              dnas: (_: any, row: ICourseTopic) => {
                return <div className="rtl:text-right">{row.dnas.length}</div>;
              },

              length: (_: any, row: ICourseTopic) => {
                const length = row.dnas.reduce(
                  (acc, lesson) => acc + parseInt(lesson?.audio_length?.split('min')?.[0] || '0'),
                  0
                );
                return (
                  <div className="font-medium rtl:text-right">
                    <span>{length} min</span>
                  </div>
                );
              },
              subStatus: (_: any, row: any) => getTaskStatusStyle(row?.status),
              subTitle: (_: any, row: ICourseTopicDNA) => {
                const element = !row.dna ? (
                  <span>{row.title}</span>
                ) : (
                  <p
                    className="text-primary font-bold underline cursor-pointer"
                    onClick={() => navigate(`/app/my-content/DNAs/${row.id}`)}
                  >
                    {row.title}
                  </p>
                );
                return (
                  <div className="ms-10 rtl:text-right">
                    <div className={`break-words font-bold`}>{element}</div>
                    <p className="font-medium">{row.learning_objectives}</p>
                  </div>
                );
              },
              subLength: (_: any, row: ICourseTopicDNA) => {
                return <div className="font-medium max-w-12 rtl:text-right">{row.audio_length}</div>;
              },
              actions: (_: any, row: ICourseTopic) => {
                const AllTopicsHasDna = row.dnas.every((dna: ICourseTopicDNA) => dna.status !== 'no_dna');
                return (
                  <div className="flex gap-2">
                    {!AllTopicsHasDna && (
                      <div>
                        {isPending && row.id === variables ? (
                          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={25} />
                        ) : (
                          <Button
                            onClick={() =>
                              generateAllTopicDnas(row.id, {
                                onSuccess: () => {
                                  queryClient.invalidateQueries({ queryKey: ['programPlan'] });
                                },
                              })
                            }
                            type="button"
                            className="flex gap-2 items-center"
                          >
                            <Icon icon="mage:stars-a-fill" width={22} className="cursor-pointer" />
                            {t('generate')}
                          </Button>
                        )}
                      </div>
                    )}
                    <div className="flex" onClick={() => handleDeletTopic(row.id)}>
                      <Icon icon="gg:trash" width={25} className="text-red-500 cursor-pointer" />
                    </div>
                  </div>
                );
              },
              subActions: (_: any, row: ICourseTopicDNA) => {
                return (
                  <div className="flex gap-2">
                    {!row.dna ? (
                      <div>
                        {isLoading[row.id] || (isPending && row.topic_id === variables) ? (
                          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={25} />
                        ) : (
                          <Button onClick={() => generateDna(row.id)} type="button" className="flex gap-2 items-center">
                            <Icon icon="mage:stars-a-fill" width={22} className="cursor-pointer" />
                            {t('generate')}
                          </Button>
                        )}
                      </div>
                    ) : (
                      <>
                        {isPending && row.topic_id === variables ? (
                          <Icon icon="svg-spinners:90-ring-with-bg" className="text-primary w-fit" width={25} />
                        ) : (
                          <div>
                            <Icon
                              onClick={() => {
                                setEditDialog(row.id), setDanData(row);
                              }}
                              icon="basil:edit-outline"
                              width={25}
                              className="text-primary cursor-pointer"
                            />
                          </div>
                        )}
                      </>
                    )}
                    <div>
                      <Icon
                        onClick={() => handleDeleteDna(row.id)}
                        icon="gg:trash"
                        width="25"
                        className="text-red-500 cursor-pointer"
                      />
                    </div>
                  </div>
                );
              },
            }}
            columns={[
              {
                key: 'title',
                label: t('CoursePlanCreationPage.table.title'),
                width: '350px',
              },
              {
                key: 'dnas',
                label: t('CoursePlanCreationPage.table.dnas'),
                width: '50px',
              },
              {
                key: 'length',
                label: t('CoursePlanCreationPage.table.lenght'),
                width: '150px',
              },
              {
                key: 'bloom_tax',
                label: t('CoursePlanCreationPage.table.bloomTax'),
                width: '150px',
              },
              {
                key: 'actions',
                label: t('CoursePlanCreationPage.table.actions'),
                width: '50px',
              },
            ]}
            customeRows={<CoursePlanCustomeRow courseId={course.id as any} />}
            subDetails={true}
            subColumns={[
              {
                key: 'subTitle',
                label: 'title',
                span: 2,
              },
              {
                key: 'subLength',
                label: 'Length',
              },
              {
                key: 'bloom_tax',
                label: 'Bloom Taxonomy',
                width: '150px',
              },
              {
                key: 'subActions',
                label: 'Length',
              },
            ]}
          /> */}

          {/* <div className="bg-background flex gap-4 justify-end p-5 border-t-0 rounded-b-md  -mt-1 border border-border">
        <Button type="button" variant={'outline'}>
          report
        </Button>
      </div> */}
        </div>
      ))}

      {editDialog && (
        <EditDna
          onOpen={editDialog}
          onClose={() => {
            setEditDialog(false), setDanData(null);
          }}
          data={danData}
        />
      )}
    </div>
  );
};

export default CoursePlanStep;
