import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useNotify } from '@/hooks';
import { summarizePdf } from './endpoints';
import { useTranslation } from 'react-i18next';

export const useSummarizePdf = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: summarizePdf,
    onSuccess: () => {
      notify.success(t('notify.pdfSummarized'));
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
