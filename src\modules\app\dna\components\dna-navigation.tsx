import { Icon } from '@/components';
import { Button } from '@/components/ui/button';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
const DnaNavigation = ({ ids }: { ids: number[] }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { i18n, t } = useTranslation();
  const { dnaId } = useParams();
  const navigate = useNavigate();
  useEffect(() => {
    if (dnaId) {
      const index = ids.indexOf(parseInt(dnaId));
      if (index !== -1) {
        setCurrentIndex(index);
      }
    }
  }, [ids, dnaId]);
  const handleNext = () => {
    if (ids.length === 0) return;
    if (currentIndex < ids.length - 1) {
      const nextIndex = currentIndex + 1;
      const newPath = location.pathname.replace(`/${dnaId}`, `/${ids[nextIndex]}`);
      navigate(newPath);
      setCurrentIndex(nextIndex);
    }
  };

  const handlePrevious = () => {
    if (ids.length === 0) return;
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      const newPath = location.pathname.replace(`/${dnaId}`, `/${ids[newIndex]}`);
      navigate(newPath);
      setCurrentIndex(newIndex);
    }
  };
  const canGoNext = useMemo(() => {
    if (ids.length === 0) return false;
    return currentIndex < ids.length - 1;
  }, [ids, currentIndex]);

  const canGoPrevious = useMemo(() => {
    if (ids.length === 0) return false;
    return currentIndex > 0;
  }, [ids, currentIndex]);

  return (
    <div>
      {ids.length > 0 && (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            className="flex items-center gap-3 text-sm hover:text-primary dark:hover:text-primary"
            onClick={handlePrevious}
            disabled={!canGoPrevious}
          >
            <Icon
              icon={i18n.language === 'en' ? 'line-md:chevron-small-left' : 'line-md:chevron-small-right'}
              className="mt-1"
              width="20"
            />
            <p>{t('dnaSingle.previousDna')}</p>
          </Button>
          <Button
            variant="ghost"
            className="flex items-center gap-3 text-sm  hover:text-primary dark:hover:text-primary"
            onClick={handleNext}
            disabled={!canGoNext}
          >
            <p>{t('dnaSingle.nextDna')}</p>
            <Icon
              icon={i18n.language === 'en' ? 'line-md:chevron-small-right' : 'line-md:chevron-small-left'}
              className="mt-1"
              width="20"
            />
          </Button>
        </div>
      )}
    </div>
  );
};

export default DnaNavigation;
