import { Outlet } from 'react-router-dom';
import { useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { userAtom } from '@/modules/auth/store';
import { AppHeader } from '@/modules/app/components/header';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
export const MainLayout = () => {
  // preventing the user from accessing the auth if he is already logged in
  const navigate = useLanguageNavigate();

  const user = useAtomValue(userAtom);
  const token = localStorage.getItem('token');
  useEffect(() => {
    if (user && token) {
      navigate('/app');
    }
  }, [user]);

  return (
    <div className="overflow-hidden h-full">
      <div className="border-b border-border py-1">
        <AppHeader showUserIcon={false} />
      </div>
      <Outlet />
    </div>
  );
};
