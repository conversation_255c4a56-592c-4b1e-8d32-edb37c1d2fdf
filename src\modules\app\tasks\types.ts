import { IUser } from '@/modules/auth/types';
import { ICourse } from '../course/types';
import { IStatus } from '../dashboard/modules-status/types';
import { IPermission } from '../roles/types';
interface IOperation {
  id: number;
  name: string;
  description: string;
  permissions: IPermission[];
}
interface ITaskContent {
  tool_id: number;
  tool: string;
  tool_data: ICourse;
  total_reviewer_notes: number;
}
export interface ITaskChangeDnaContentPayload {
  course_id: number;
  topic_id: number;
  dna_id: number;
  dna_content: string;
}
export interface ITaskChangeDnaStatusPayload {
  course_id: number;
  topic_id: number;
  dna_id: number;
  dna_status: 'ready for production' | string;
}
export interface ITask {
  // id: number;
  code: string;
  description: string;
  status: string;
  priority: string;
  content: ITaskContent;
  initiated_by: {
    email: string;
    name: string;
    id: number | string;
  };
  assigned_to: { id: number; name: string; email: string };
  due_date: string;
  operations: IOperation[];
  task_status: IStatus;
  invitation: { status: string; response_at: string };
  created_at: string;
  can_reopen_task: boolean;
  can_update_task: boolean;
}

export interface INotes {
  dna_id: number;
  reviewer_notes: string;
  //   course_id: number;
  //   topic_id: number;
}

export interface IGenerateActivityPayload {
  code: string;
  payload: {
    case_study: number;
    fill_in_blank: number;
    content_id: number;
    mcq: number;
    t_f: number;
    content_type: 'Dna' | 'Course' | 'Topic';
  };
}

export interface IActivityQuestion {
  id: number;
  content_type: string;
  content_id: number;
  type: 'true_false' | 'mcq' | 'fill_in_the_blank' | 'case_study';
  question: string;
  answer: string;
  feedback: string;
  xml: string | null;
  options: Record<string, string>;
  scenario: string | null;
  status: string;
}
