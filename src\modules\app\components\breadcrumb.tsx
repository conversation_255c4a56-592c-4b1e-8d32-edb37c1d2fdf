import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Bread<PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb';
import { Link, useLocation } from 'react-router-dom';
import { useGetDnaTitleById } from '../dna/apis/queries';
import { useGetCourseById } from '../course/apis/queries';
import { useGetTaskByCode, useGetTaskByCodeAdmin } from '../tasks/apis/queries';
import { useGetUserById } from '../users/apis/queries';
import { useGetRoleById } from '../roles/apis/queries';
import { useGetOperationById } from '../dashboard/operation/apis/queries';

export const AppBreadcrumb = () => {
  const { t, i18n } = useTranslation();
  const location = useLocation();
  const isTaskPath = location.pathname.includes('my-task') || location.pathname.includes('all-tasks');

  const originalPaths = location.pathname
    .replace(/\/$/, '')
    .split('/')
    .filter(Boolean)
    .filter((path) => !['en', 'ar', 'edit'].includes(path));

  const displayPaths = originalPaths.filter((path) => {
    if (isTaskPath) {
      return !['course', 'dna'].includes(path.toLowerCase());
    }
    return true;
  });

  const dnaId = originalPaths.find(
    (_, index) =>
      originalPaths[index - 1]?.toLowerCase() === 'dna' ||
      originalPaths[index - 1]?.toLowerCase() === 'dnas' ||
      originalPaths[index - 1]?.toLowerCase() === 'rewrite-dna-content' ||
      originalPaths[index - 2]?.toLowerCase() === 'courses'
  );
  const { data: dna } = useGetDnaTitleById(dnaId ? Number(dnaId) : undefined);

  const courseId = originalPaths.find((_, index) => originalPaths[index - 1]?.toLowerCase() === 'courses');
  const { data: course } = useGetCourseById(courseId ? courseId : undefined);

  const myTaskId = originalPaths.find((_, index) => originalPaths[index - 2]?.toLowerCase() === 'my-tasks');
  const { data: myTask } = useGetTaskByCode(myTaskId || '');

  const allTaskId = originalPaths.find((_, index) => originalPaths[index - 2]?.toLowerCase() === 'all-tasks');
  const { data: allTask } = useGetTaskByCodeAdmin(allTaskId || '');

  const userId = originalPaths.find((_, index) => originalPaths[index - 1]?.toLowerCase() === 'users');
  const { data: user } = useGetUserById(userId);

  const roleId = originalPaths.find((_, index) => originalPaths[index - 1]?.toLowerCase() === 'roles');
  const { data: role } = useGetRoleById(roleId);

  const operationId = originalPaths.find((_, index) => originalPaths[index - 1]?.toLowerCase() === 'operations');
  const { data: operation } = useGetOperationById(operationId);

  const getLabelForPath = (path: any, index: any, paths: any) => {
    const prevPath = index > 0 ? paths[index - 1].toLowerCase() : null;
    const prevPath2 = index > 1 ? paths[index - 2].toLowerCase() : null;

    let label = path
      .split('-')
      .map((word: any) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    if (path === 'app') {
      label = 'Home';
    } else if (prevPath && prevPath2) {
      if (prevPath === 'dnas') {
        label = typeof dna === 'object' && dna !== null ? 'Loading...' : dna ? dna : 'Loading...';
      } else if (prevPath.includes('course-review')) {
        label = typeof dna === 'object' && dna !== null ? 'Loading...' : dna ? dna : 'Loading...';
      } else if (prevPath === 'courses') {
        label = course?.title || 'Loading...';
      } else if (prevPath2 === 'courses') {
        label = typeof dna === 'object' && dna !== null ? 'Loading...' : dna ? dna : 'Loading...';
      } else if (prevPath === 'my-tasks') {
        label = myTask?.content?.tool_data?.title || 'Loading...';
      } else if (prevPath === 'all-tasks') {
        label = allTask?.content?.tool_data?.title || 'Loading...';
      } else if (prevPath === 'users') {
        label = user?.name || 'Loading...';
      } else if (prevPath === 'roles') {
        label = role?.name || 'Loading...';
      } else if (prevPath === 'rewrite-dna-content') {
        label = typeof dna === 'object' && dna !== null ? 'Loading...' : dna ? dna : 'Loading...';
      } else if (prevPath === 'operations') {
        label = operation?.name || 'Loading...';
      }
    }
    document.title = /\d/.test(path) ? label : t(`breadcrumb.myContentPage.${path}`);
    return /\d/.test(path) ? label : t(`breadcrumb.myContentPage.${path}`);
  };

  const breadcrumbs = displayPaths.map((path, index) => {
    const pathsForUrl = originalPaths.slice(0, originalPaths.indexOf(path) + 1);
    const url = '/' + i18n.language + '/' + pathsForUrl.join('/');
    const label = getLabelForPath(path, index, displayPaths);

    return {
      label,
      path: url,
    };
  });

  if (breadcrumbs.length <= 1) return null;

  const labelKey = i18n.language === 'en';

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((crumb, index) => (
          <React.Fragment key={crumb.path}>
            <BreadcrumbItem className="mb-1">
              {index === breadcrumbs.length - 1 ? (
                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link to={crumb.path}>{crumb.label}</Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {index < breadcrumbs.length - 1 && <BreadcrumbSeparator className={`${!labelKey ? 'rotate-180' : ''}`} />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};
