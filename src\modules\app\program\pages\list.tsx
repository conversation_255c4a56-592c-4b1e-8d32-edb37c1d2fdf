import { useFetchList, ProtectedComponent, Icon } from '@/index';
import { useMemo } from 'react';
import { formatDateByYear, formatDateByHour, getAllNames } from '@/utils/helpers';
import {
  TableContentHeader,
  TableContentBody,
  TableContent,
  Table,
  TableFilters,
  TablePagination,
  TableSearch,
} from '@/components/theTable';
import { useHasPermission } from '@/modules/auth/store';
import { useTranslation } from 'react-i18next';
import LanguageLink from '@/components/language-link';
import { Button } from '@/components/ui/button';
function ProgramPlanList() {
  //  State
  const { t, i18n } = useTranslation();
  // Computed
  const { loading, list, count, search, filters, pagination } = useFetchList('/program-plan', 'programs', {
    search: '',
    pagination: {
      page_num: 1,
      page_size: 30,
    },
    filters: {
      ...(useHasPermission('user_index')
        ? {
            user_id: {
              api: 'user/lookup',
              placeholder: 'dnaConentPgae.userFIlter',
            },
          }
        : {}),
    },
  });

  // Computed values
  const columns = useMemo((): ITableColumn<any>[] => {
    return [
      {
        accessorKey: 'title',
        header: t('programPlan.table.Title'),
        width: '250px',
      },
      {
        accessorKey: 'subject',
        header: t('dnaConentPgae.table.subject'),
        width: '180px',
        cell: ({ row }) => getAllNames(row.subject, i18n.language),
      },
      {
        accessorKey: 'audience',
        header: t('topicContentPage.table.audience'),
        width: '200px',
        cell: ({ row }) => getAllNames(row.audience, i18n.language),
      },
      {
        accessorKey: 'language',
        header: t('topicContentPage.table.language'),
        width: '100px',
        cell: ({ row }) => getAllNames(row.language, i18n.language),
      },
      {
        accessorKey: 'created_at',
        header: t('dnaConentPgae.table.date'),
        width: '100px',
        cell: ({ row }) => (
          <div className="rtl:text-right">
            <p>{formatDateByYear(row.updated_at, i18n.language)}</p>
            <p>{formatDateByHour(row.updated_at, i18n.language)}</p>
          </div>
        ),
      },
    ];
  }, []);
  return (
    <Table rows={list} columns={columns} loading={loading}>
      <TableContent>
        <TableContentHeader>
          <div className="flex justify-between items-center w-full">
            <div className="flex gap-3 items-center">
              <TableSearch className="w-fit" search={search} placeholder={t('search')} />
              <TableFilters filters={filters} />
            </div>
            <div className="flex  gap-3">
              <ProtectedComponent requiredPermissions={'program_plan_create'}>
                <LanguageLink to={'/app/tools/program-plan'}>
                  <Button>
                    <span className="flex items-center gap-2">
                      <Icon icon="ic:round-add" width={17} />
                      <span className="text-sm">{t('topicContentPage.createNewProgram')}</span>
                    </span>
                  </Button>
                </LanguageLink>
              </ProtectedComponent>
            </div>
          </div>
        </TableContentHeader>
        <TableContentBody />
        <TablePagination count={count} pagination={pagination} />
      </TableContent>
    </Table>
  );
}

export default ProgramPlanList;
