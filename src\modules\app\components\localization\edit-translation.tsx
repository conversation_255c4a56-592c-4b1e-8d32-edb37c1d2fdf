import { useEffect, useState } from 'react';
import { Modal, Form, useForm, TextInput } from '@/index';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { useUpdateTransaltion, useUpdateTransaltionStatus } from '@/modules/app/dna/apis/queries';
import { useParams } from 'react-router-dom';
import { ProtectedTaskComponent } from '../../tasks/components/protected-task-component';
import { useConfirmation } from '@/components/confirmation-popup/hooks';
import { Editor } from '@/components/CKEditor';

const EditTranslation = ({
  onOpen,
  onOpenChange,
  data,
  details,
  viewOnly = false,
}: {
  onOpen: boolean;
  onOpenChange: () => void;
  data: any;
  details: any;
  viewOnly?: boolean;
}) => {
  //  State
  const [localStatus, setLocalStatus] = useState(data.status);
  const [count, setCount] = useState(data.words_count);
  // Hooks
  const { t, i18n } = useTranslation();
  const { taskId } = useParams();
  const { confirm } = useConfirmation();

  const { form, setFieldValue } = useForm({
    id: details.id,
    translation_id: data.id,
    content: data.content,
    title: '',
    learning_objective: '',
    code: taskId as string,
  });

  useEffect(() => {
    if (data) {
      setFieldValue('title')(data.title);
      setFieldValue('learning_objective')(data.learning_objective);
    }
  }, [data]);

  const labelKey = i18n.dir() === 'ltr';

  const { mutate, variables, isPending } = useUpdateTransaltion();

  const { mutate: changeStatus, isPending: isLodading } = useUpdateTransaltionStatus();
  const handleSubmit = () => {
    mutate(
      {
        id: details.id,
        translation_id: data.id,
        content: form.content,
        title: form.title,
        code: taskId as string,
        learning_objective: form.learning_objective,
      },
      {
        onSuccess: (data) => {
          setCount(data[0].words_count);
          setLocalStatus(data.status);
        },
      }
    );
  };

  const handleFinishTranslation = () => {
    confirm({
      variant: 'info',
      title: t('translation.finish.confirmation.title'),
      description: t('translation.finish.confirmation.description'),
      onConfirm: () => {
        changeStatus(
          {
            id: details.id,
            translation_id: data.id,
            status: 'reviewed',
            code: taskId as string,
            content: form.content,
            title: form.title,
            learning_objective: form.learning_objective,
          },
          {
            onSuccess: () => {
              setLocalStatus('reviewed'), onOpenChange();
            },
          }
        );
      },
    });
  };

  return (
    <Modal width={1500} open={onOpen} onOpenChange={onOpenChange} modalHeader={t('editTranslation.title')}>
      <div className="grid grid-cols-2 gap-5">
        <div className="space-y-4">
          <div className=" flex gap-1 font-medium">
            <p>{t('translation.orignal')}</p>
            <p>({labelKey ? details.language.name_en : details.language.name_ar})</p>
          </div>
          <div className={`bg-muted dark:bg-background py-2 px-3 ${viewOnly ? 'space-y-4' : 'space-y-10'}`}>
            <div className="space-y-2">
              <p className={`${viewOnly ? '' : 'text-sm font-medium'}`}>{t('dnaCreationPage.form.title')}</p>
              <p className="text-xl font-medium">{details.title}</p>
            </div>
            <div className="space-y-1">
              <p className={`${viewOnly ? '' : 'text-sm font-medium'}`}>
                {t('dnaCreationPage.form.learningObjectives')}
              </p>
              <p className="opacity-[0.5]">{details.learning_objectives}</p>
            </div>
          </div>
          <p className="opacity-[0.5] text-left ltr:text-right">
            {details.word_count} {t('dnaCreationPage.word')}
          </p>
          <div className="overflow-y-auto h-[450px] p-4 border border-border rounded-lg shadow-sm bg-muted dark:bg-background">
            <Editor editorContent={details.dna_content} language={details.language} readOnly />
          </div>
        </div>
        {viewOnly ? (
          <div className="space-y-4">
            <div className="font-medium text-primary flex gap-1">
              <p>{t('translation.transaltedContent')}</p>
              <p>({labelKey ? data.language.name_en : data.language.name_ar})</p>
            </div>
            <div className="bg-muted dark:bg-background py-2 px-3 space-y-4">
              <div className="space-y-1.5">
                <p>{t('dnaCreationPage.form.title')}</p>
                <p className="text-xl font-medium">{form.title}</p>
              </div>
              <div className="space-y-1.5">
                <p>{t('dnaCreationPage.form.learningObjectives')}</p>
                <p className="opacity-[0.5]">{form.learning_objective || '___'}</p>
              </div>
            </div>
            <p className="opacity-[0.5] text-left ltr:text-right">
              {count} {t('dnaCreationPage.word')}
            </p>
            <div className="overflow-y-auto h-[450px] p-4 border border-border rounded-lg shadow-sm bg-muted dark:bg-background">
              <Editor editorContent={form.content} language={data.language} readOnly />
            </div>
          </div>
        ) : (
          <Form className="space-y-4">
            <div className="font-medium text-primary flex gap-1">
              <p>{t('translation.transaltedContent')}</p>({labelKey ? data.language.name_en : data.language.name_ar})
            </div>
            <TextInput
              name="title"
              label={t('dnaCreationPage.form.title')}
              placeholder={t('dnaCreationPage.form.title')}
              value={form.title}
              onChange={setFieldValue('title')}
              disabled={viewOnly}
            />
            <TextInput
              name="learningObjectives"
              label={t('dnaCreationPage.form.learningObjectives')}
              placeholder={t('dnaCreationPage.form.learningObjectives')}
              value={form.learning_objective}
              onChange={setFieldValue('learning_objective')}
              disabled={viewOnly}
            />
            <p className="opacity-[0.5] text-right rtl:text-left">
              {count} {t('dnaCreationPage.word')}
            </p>
            <div className=" overflow-y-auto rounded-md">
              <Editor
                editorContent={form.content}
                setEditorContent={setFieldValue('content')}
                language={data.language}
                disabled={viewOnly}
              />
            </div>
            <div className="flex gap-3 pt-5" dir={i18n.language === 'en' ? 'rtl' : 'ltr'}>
              <div className="flex gap-3">
                {!viewOnly && (
                  <ProtectedTaskComponent requiredPermissions={'translation_change_status'}>
                    <Button
                      type="button"
                      className="min-w-[130px]"
                      onClick={handleFinishTranslation}
                      loading={isLodading}
                      disabled={isLodading}
                    >
                      {t('dnaCreationPage.translate.approve')}
                    </Button>
                  </ProtectedTaskComponent>
                )}
                <ProtectedTaskComponent requiredPermissions={'translation_edit'}>
                  <Button
                    type="submit"
                    className="min-w-[100px]"
                    onClick={handleSubmit}
                    loading={isPending}
                    disabled={viewOnly || isPending}
                    variant={'outline'}
                  >
                    {t('save')}
                  </Button>
                </ProtectedTaskComponent>
              </div>

              {/* <ProtectedTaskComponent requiredPermissions={'dna_download'}>
                <Button variant={'ghost'} onClick={() => handelExportToWord(data.content, data.title, i18n.language)}>
                  <Icon icon="material-symbols:download-rounded" width={25} />
                </Button>
              </ProtectedTaskComponent> */}
            </div>
          </Form>
        )}
      </div>
    </Modal>
  );
};

export default EditTranslation;
