import React, { useEffect, useState } from 'react';
import { useCreateUser } from '../apis/queries';
import { Form, TextInput, useForm, Api, useValidate, useNotify, Regex, Icon } from '@/index';
import { ComboboxInput } from '@/components/form/inputs/combo-box';
import { useTranslation } from 'react-i18next';
import useLanguageNavigate from '@/hooks/use-lang-navigation';
import { Button } from '@/components/ui/button';

const UserCrerationPage = () => {
  // State
  const [options, setOptions] = useState<any>({});
  const [showPassword, setShowPassword] = useState(false);
  // Hooks
  const { isRequired, maxLength, validatePasswordRegex } = useValidate();
  const { notify } = useNotify();
  const navigate = useLanguageNavigate();
  const { mutate: createUser, isPending } = useCreateUser();

  //Form
  const { form, setFieldValue } = useForm({
    name: '',
    password: '',
    email: '',
    role: '',
  });
  const { t } = useTranslation();

  const handleGetRoles = async () => {
    try {
      const response = await Api.get('roles');
      setOptions(response.data.data.items);
    } catch (error: any) {
      notify.error(error.message);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword((prevShowPassword) => !prevShowPassword);
  };

  const handleCreate = async () => {
    createUser(
      { payload: form },
      {
        onSuccess: () => {
          navigate('/app/users');
        },
      }
    );
  };

  useEffect(() => {
    handleGetRoles();
  }, []);

  return (
    <div>
      <Form onSubmit={handleCreate}>
        <div className="space-y-4 w-[350px]">
          <TextInput
            name="name"
            label={t('name')}
            placeholder={t('name')}
            value={form.name}
            onChange={setFieldValue('name')}
            isRequired
            validators={[isRequired()]}
          />
          <div className="flex w-full">
            <div className="w-full">
              <TextInput
                name={'password'}
                label={t('password')}
                placeholder={t('password')}
                type={showPassword ? 'text' : 'password'}
                value={form.password}
                onChange={setFieldValue('password')}
                validators={[isRequired(), maxLength(50), validatePasswordRegex(Regex.password, 8)]}
              />
            </div>

            <div className="mt-8" onClick={() => togglePasswordVisibility()}>
              <Icon
                className="ltr:ml-3 rtl:mr-3 p-5 w-8 h-8 rounded-md cursor-pointer bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400"
                width="25"
                icon={!showPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
              />
            </div>
          </div>
          <TextInput
            name="email"
            label={t('email')}
            placeholder={t('email')}
            value={form.email}
            onChange={setFieldValue('email')}
            validators={[isRequired()]}
          />
          <ComboboxInput
            label={t('userPage.role')}
            placeholder={t('userPage.role')}
            options={options}
            value={form.role}
            onChange={setFieldValue('role')}
            optionLabelKey="name"
            optionValueKey="name"
            validators={[isRequired()]}
            customOptionLabelKey={true}
          />
        </div>
        <div className="my-6">
          <Button loading={isPending} disabled={isPending} className="min-w-[100px] mt-4" type="submit">
            {t('userPage.buttons.createUser')}
          </Button>
        </div>
      </Form>
    </div>
  );
};

export default UserCrerationPage;
