import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useRef, useState } from 'react';
import { ImageIcon } from 'lucide-react';
import { Form, Modal } from '@/components';
import { t } from 'i18next';
import { useReplaceImage } from '../../modules/app/dna/apis/media/queries';
import { useForm } from '@/hooks';
import { useParams } from 'react-router-dom';
import { IMedia } from '../../modules/app/common/types';

const ImageReplaceDialog = ({
  open,
  onOpenChange,
  selectedImage,
}: {
  open: boolean;
  onOpenChange: any;
  selectedImage?: IMedia;
}) => {
  // States
  const [uploadPreview, setUploadPreview] = useState<string | null>(null);

  // Ref
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  //Hooks
  const { dnaId } = useParams();
  const { form, setFieldValue } = useForm({
    new_file: null,
    dna_id: dnaId,
    media_uuid: selectedImage?.uuid,
  });
  const { mutate: replaceImage, isPending: isReplacingImage } = useReplaceImage();

  // Functions
  const handleFileUpload = (file: File) => {
    setFieldValue('new_file')(file);
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setUploadPreview(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadImage = () => {
    if (form.new_file) {
      replaceImage(form, {
        onSuccess: () => {
          onOpenChange(false);
        },
      });
    } else {
      onOpenChange(false);
    }
  };
  return (
    <Modal open={open} onOpenChange={onOpenChange}>
      <Form onSubmit={handleUploadImage}>
        <div className="grid grid-rows-2 pt-3">
          <div className="border-2 mb-3 row-span-2 h-[400px] border-dashed border-muted-foreground/20 rounded-lg w-full flex flex-col items-center justify-center">
            {/* hidden input */}
            <Input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              className="hidden"
              onChange={(e) => {
                if (e.target.files?.[0]) {
                  handleFileUpload(e.target.files[0]);
                }
              }}
            />
            {uploadPreview || selectedImage?.url ? (
              <div className="relative w-full h-full overflow-hidden group">
                <img
                  src={uploadPreview || `${selectedImage?.url}?${selectedImage?.uuid}`}
                  alt="Upload preview"
                  className=" w-full h-full rounded-lg object-contain"
                />
                <div className="absolute opacity-0 transition-all inset-0 z-10 bg-black/50 flex items-center justify-center rounded-lg group-hover:opacity-100">
                  <Button
                    variant="secondary"
                    className="mx-auto mt-2"
                    size="sm"
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    {t('media.replace')}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="text-center">
                <ImageIcon className="!h-8 !w-8 mx-auto text-muted-foreground" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">Drop your image here or click to browse</p>
                  <p className="text-xs text-muted-foreground">Supports JPG, PNG, GIF up to 10MB</p>
                  <Button
                    variant="secondary"
                    className="mx-auto mt-2"
                    size="sm"
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    Browse Files
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
        <div className="pt-4 mr-auto border-t border-border flex justify-end items-center">
          <Button type="submit" size={'sm'} className="min-w-32" disabled={isReplacingImage} loading={isReplacingImage}>
            {t('media.replace')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};

export default ImageReplaceDialog;
