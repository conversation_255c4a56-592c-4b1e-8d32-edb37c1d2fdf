interface IMetaData {
  id: number;
  type: string;
  name_en: string;
  name_ar: string;
  description: string;
  deleted_at: string | null;
}

interface IPivot {
  role_id: number;
  permission_id: number;
}

interface IPermission {
  id: number;
  name: string;
  pivot: IPivot;
}

interface IRolePivot {
  model_type: string;
  model_id: number;
  role_id: number;
}

interface IRole {
  id: number;
  name: string;
  pivot: IRolePivot;
  permissions: IPermission[];
}

interface IMedia {
  id: number;
  model_type: string;
  model_id: number;
  uuid: string;
  collection_name: string;
  name: string;
  file_name: string;
  mime_type: string;
  disk: string;
  conversions_disk: string;
  size: number;
  manipulations: any[];
  custom_properties: any[];
  generated_conversions: any[];
  responsive_images: any[];
  order_column: number;
  created_at: string;
  updated_at: string;
  original_url: string;
  preview_url: string;
}

export interface IUser {
  id: number;
  name: string;
  email: string;
  email_verified_at: string | null;
  info: string;
  phone: string | null;
  birth_date: string;
  sex: 'male' | 'female' | null;
  nationality_id: number;
  nationally_number: string;
  profession: string;
  education_id: number;
  university_id: number;
  personal_skills: string | null;
  account_status: 'active' | 'pending' | 'blocked';
  created_at: string;
  updated_at: string;
  image: string;
  cv: string;
  nationality: IMetaData;
  education: IMetaData;
  university: IMetaData;
  roles: IRole[];
  media: IMedia[];
}

export interface IAuthResponse {
  access_token: string;
  user: IUser;
}

export interface IDashboardData {
  completed_tasks_count: number;
  courses_count: number;
  declined_tasks_count: number;
  dnas_count: number;
  hold_tasks_count: number;
  in_progress_tasks_count: number;
  open_tasks_count: number;
  overdue_no_response_tasks_count: number;
  overdue_tasks_count: number;
  tasks_count: number;
  topics_count: number;
}
