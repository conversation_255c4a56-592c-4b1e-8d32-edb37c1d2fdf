import { Modal, Icon, Form, TextInput, useForm, useValidate, Textarea, useNotify, MultiSelect } from '@/index';
import { useTranslation } from 'react-i18next';

import { useState, useEffect } from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useGetCoursesSimplified } from '@/modules/app/course-group/api/queries';
import { Button } from '@/components/ui/button';
import { useCreateCourseGroup, useUpdateCourseGroup } from '@/modules/app/course-group/api/queries';
import { useGetSingleMetadata } from '../../dashboard/metadata/apis/queries';
import { generateEnum } from '@/utils/helpers';

export const CourseGroupDialog = ({
  onOpen,
  opOpenChange,
  editData,
  isCourseList = false,
}: {
  onOpen: boolean;
  opOpenChange: () => void;
  editData?: any;
  isCourseList: boolean;
}) => {
  const { t, i18n } = useTranslation();
  const [selectedRows, setSelectedRows] = useState<any>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const { data: coursesSimplified } = useGetCoursesSimplified();
  const { isRequired, isNotEmptyArray } = useValidate();
  const { mutate: createCourseGroup, isPending: isCreatePending } = useCreateCourseGroup();
  const { mutate: updateCourseGroup, isPending: isUpdatePending } = useUpdateCourseGroup();
  const { data: allmetaData } = useGetSingleMetadata('');
  const tagsOptions = allmetaData?.filter((item) => item.type === 'tags') || [];

  const labelKey = i18n.dir() === 'ltr' ? 'name_en' : 'name_ar';

  const { notify } = useNotify();
  const { form, setFieldValue, setFormValue } = useForm({
    title: '',
    description: '',
    tags_ids: null,
  });

  useEffect(() => {
    if (editData) {
      setFormValue({
        title: editData.title,
        description: editData.description,
        tags_ids: editData.tags.map((tag: any) => tag.id),
      });
      setSelectedRows(editData.courses?.map((course: any) => course.id) || []);
    }
  }, [editData]);

  const filteredCourses = editData
    ? coursesSimplified
        ?.sort((courseA: any, courseB: any) => {
          const inEditDataA = editData.courses.some((ed: any) => ed.id === courseA.id);
          const inEditDataB = editData.courses.some((ed: any) => ed.id === courseB.id);
          return Number(inEditDataB) - Number(inEditDataA); // Sorts true first
        })
        .filter((course) => course.title.toLowerCase().includes(searchQuery.toLowerCase()))
    : coursesSimplified?.filter((course) => course.title.toLowerCase().includes(searchQuery.toLowerCase()));

  const handleSubmit = () => {
    if (selectedRows.length === 0) {
      notify.error(t('courseGroup.selectAtLeastOneCourse'));
      return;
    }

    const payload = {
      title: form.title,
      description: form.description,
      courses_ids: selectedRows,
      tags_ids: form.tags_ids,
    };

    if (editData && !isCourseList) {
      updateCourseGroup(
        { id: editData.id, ...payload },
        {
          onSuccess: () => {
            opOpenChange();
          },
        }
      );
    } else {
      createCourseGroup(payload, {
        onSuccess: () => {
          opOpenChange();
        },
      });
    }
  };

  return (
    <Modal
      width="1200px"
      open={onOpen}
      onOpenChange={opOpenChange}
      modalHeader={editData ? t('courseGroup.editCourseGroup') : t('courseGroup.createNewCourseGroup')}
    >
      <Form className="space-y-5" onSubmit={handleSubmit}>
        <TextInput
          name="title"
          label={t('cousreGroup.title')}
          placeholder={t('cousreGroup.title')}
          value={form.title}
          onChange={setFieldValue('title')}
          validators={[isRequired()]}
        />
        <Textarea
          name="description"
          label={t('cousreGroup.description')}
          placeholder={t('cousreGroup.description')}
          value={form.description}
          onChange={setFieldValue('description')}
        />
        <MultiSelect
          name="tags_ids"
          label={t('cousreGroup.tags_ids')}
          options={generateEnum(tagsOptions, 'id', labelKey)}
          onChange={setFieldValue('tags_ids')}
          value={form.tags_ids || []}
          placeholder={t('cousreGroup.tags_ids')}
          variant="secondary"
          maxCount={50}
        />
        <div>
          <div className=" pb-2 border-b border-border flex justify-between items-center">
            <h5 className="mb-2 text-sm font-medium">
              {t('selectCourses')} <span>({selectedRows?.length})</span>
            </h5>
            <div className="mb-3 w-[400px]">
              <TextInput
                placeholder={t('search')}
                value={searchQuery}
                onChange={(value: string) => setSearchQuery(value)}
                prefix={<Icon name="search" className="w-4 h-4 text-gray-500" />}
              />
            </div>
          </div>
          <ScrollArea className="h-[350px] ">
            <div className="space-y-2">
              {filteredCourses?.map((course) => (
                <div
                  key={course.id}
                  className="flex items-center rtl:text-end  gap-3  p-3 rounded-lg hover:bg-gray-50 cursor-pointer border border-transparent hover:border-border"
                  onClick={() => {
                    if (selectedRows.includes(course.id)) {
                      setSelectedRows(selectedRows.filter((id: string) => id !== course.id));
                    } else {
                      setSelectedRows([...selectedRows, course.id]);
                    }
                  }}
                >
                  <div
                    className={`w-5 h-5 min-w-5 rounded border flex items-center justify-center ${
                      selectedRows.includes(course.id) ? 'bg-primary border-primary' : 'border-gray-300'
                    }`}
                  >
                    {selectedRows.includes(course.id) && <Icon name="check" className="w-4 h-4 text-white" />}
                  </div>
                  <div className="flex flex-col">
                    <div className="font-medium">{course.title}</div>
                    {/* <div className="text-sm text-gray-500">{course.info}</div> */}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
        <div className="flex justify-end pt-4 border-t border-border">
          <Button type="submit" loading={isCreatePending || isUpdatePending}>
            {editData && !isCourseList ? t('courseGroup.update') : t('courseGroup.create')}
          </Button>
        </div>
      </Form>
    </Modal>
  );
};
