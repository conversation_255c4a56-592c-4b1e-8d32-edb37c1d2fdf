import React, { useState, useEffect } from 'react';

import { Icon } from '@/index';
import AboutStep from '../components/content-need-about';
import MainGoalsStep from '../components/content-need-goals';
import JobsStep from '../components/content-need-jobs';
import SkillsStep from '../components/content-need-skills';
import OutcomeStep from '../components/content-need-outcome';
import { useTranslation } from 'react-i18next';
import { useAtom } from 'jotai';
import { contentNeedsAtom, currentStepAtom } from '../../tools/store/contnet-need';
import { useGetContentNeedsById } from '@/modules/app/content-need/apis/queries';
import { useSearchParams } from 'react-router-dom';
import ReportStep from '../components/content-need-report';

type Step = {
  id: number;
  label: string;
  description: string;
};

const ContentNeedPage: React.FC = () => {
  const [searchParams] = useSearchParams();

  const { data } = useGetContentNeedsById(searchParams.get('contentId') || '');
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [currentStep, setCurrentStep] = useAtom<number>(currentStepAtom);
  const [contentNeeds, setContentNeeds] = useAtom(contentNeedsAtom);
  const { t } = useTranslation();

  // effects
  useEffect(() => {
    if (data) {
      setContentNeeds(data);
      setCurrentStep(currentStep !== 1 ? currentStep : data.step);
    }
  }, [data, setCurrentStep]);

  const steps: Step[] = [
    { id: 1, label: t('contentNeed.steps.about'), description: t('contentNeed.steps.about.des') },
    { id: 2, label: t('contentNeed.steps.mainGoals'), description: t('contentNeed.steps.mainGoals.des') },
    { id: 3, label: t('contentNeed.steps.jops'), description: t('contentNeed.steps.jobs.des') },
    { id: 4, label: t('contentNeed.steps.skills'), description: t('contentNeed.steps.skills.des') },
    { id: 5, label: t('contentNeed.steps.outcome'), description: t('contentNeed.steps.outcome.dse') },
    { id: 6, label: t('contentNeed.steps.report'), description: t('contentNeed.steps.report.des') },
  ];
  // State for storing API data for each step
  const [stepData, setStepData] = useState<{ [key: number]: any }>({});
  const handleNextStep = (step: number) => {
    setCurrentStep(step);
    setCompletedSteps((prev) => [...prev, step, step - 1]);
  };

  const components: { [key: number]: JSX.Element } = {
    1: <AboutStep moveToNextStep={() => handleNextStep(2)} />,
    2: <MainGoalsStep moveToNextStep={() => handleNextStep(3)} />,
    3: <JobsStep moveToNextStep={() => handleNextStep(4)} />,
    4: <SkillsStep moveToNextStep={() => handleNextStep(5)} />,
    5: <OutcomeStep onComplete={() => handleNextStep(6)} />,
    6: <ReportStep onComplete={() => handleNextStep(7)} />,
  };

  return (
    <div>
      <div className="grid grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-12 items-center my-12 cursor-pointer">
        {steps.map((step) => (
          <div
            key={step.id}
            className={`flex gap-4 transition-all items-start ${
              currentStep === step.id ? 'text-primary' : step.id < currentStep ? 'text-green-500' : 'text-gray-500'
            }`}
            onClick={() => {
              if (step.id === currentStep || step.id <= (data?.step || currentStep)) {
                setCurrentStep(step.id);
              }
            }}
          >
            {step.id < (data?.step || 1) || currentStep === 6 ? (
              <Icon className="self-start" icon={`icon-park-solid:check-one`} width={25} />
            ) : (
              <Icon className="self-start" icon={`fluent:number-circle-${step.id}-20-regular`} width={25} />
            )}
            <div className="w-[200px] min-h-[100px] space-y-2">
              <p className="text-lg -mt-1 font-medium">{step.label}</p>
              <p className="text-sm">{step.description}</p>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-10 transition-all">{components[currentStep]}</div>
    </div>
  );
};

export default ContentNeedPage;
