import { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import QuestionTypeMcq from './question-type-mcq';
import QuestionTypeBlank from './question-type-blank';
import QuestionTypeCase from './question-type-case';
import { IDNA } from '../../dna/types';
import { IActivityQuestion } from '../../tasks/types';
import ActivityFilter from './activityFilter';
import withSettings from './with-settings';

const ViewOnlyActivity = ({ details }: { details: IDNA }) => {
  const [selectedQuestionType, setSelectedQuestionType] = useState<
    'case_study' | 'mcq' | 'fill_in_the_blank' | 'true_false' | null
  >(null);

  const { t } = useTranslation();

  const QuestionTypesObject = useMemo(
    () => ({
      true_false: {
        title: t('dnaSinglePage.activity.trueOrFalse'),
        component: withSettings(QuestionTypeMcq),
      },
      mcq: {
        title: t('dnaSinglePage.activity.multipleChoiceQuestions'),
        component: withSettings(QuestionTypeMcq),
      },
      fill_in_the_blank: {
        title: t('dnaSinglePage.activity.fillintheBlank'),
        component: withSettings(QuestionTypeBlank),
      },
      case_study: {
        title: t('dnaSinglePage.activity.caseStudy'),
        component: withSettings(QuestionTypeCase),
      },
    }),
    [t]
  );

  if (details?.activities_count === 0) {
    return (
      <div className="flex flex-col justify-center items-center ms-2 min-h-96">
        <img src="/empty-activity.svg" alt="" />
        <div className="flex flex-col gap-2 my-5">
          <h1 className="self-center text-slate-800 text-2xl">{t('dnaSinglePage.activity.noActivityTitle')}</h1>
          <p className="self-center text-slate-500">{t('dnaSinglePage.activity.noActivityDescription')}</p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center -mb-5">
        <div className="flex gap-3">
          <ActivityFilter selectedType={selectedQuestionType} setSelectedType={setSelectedQuestionType} />
        </div>
      </div>
      {Object.entries(details?.activities || {})
        .filter(([key]) => !selectedQuestionType || key === selectedQuestionType)
        .map(([key, value]) => (
          <div key={key} className="mb-1.5">
            <div className="bg-secondary text-secondary-foreground mx-auto w-fit text-sm px-4 py-2 font-medium mt-14 mb-10 rounded-full">
              {QuestionTypesObject[key as keyof typeof QuestionTypesObject]?.title}
            </div>
            <div className="flex flex-col gap-10">
              {value?.map((item: IActivityQuestion, index: number) => {
                const Component = QuestionTypesObject[key as keyof typeof QuestionTypesObject]?.component;
                return Component ? <Component key={index} question={item} questionNumber={index + 1} /> : null;
              })}
            </div>
          </div>
        ))}
    </div>
  );
};

export default ViewOnlyActivity;
