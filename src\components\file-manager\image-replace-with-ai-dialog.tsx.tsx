import { Card, ComboboxInput, Icon, Modal, Textarea, TextInput } from '@/components';
import { Button } from '@/components/ui/button';
import { t } from 'i18next';
import { useEffect, useState } from 'react';
import { Form } from '@/components';
import { useForm, useNotify } from '@/hooks';
import { useEditImage, useGenerateImage, useReplaceImage } from '../../modules/app/dna/apis/media/queries';
import { cn } from '@/lib/utils';
import { IMedia } from '../../modules/app/common/types';
import { useGetSingleMetadata } from '@/modules/app/dashboard/metadata/apis/queries';
import { useTranslation } from 'react-i18next';
import { generateEnum } from '@/utils/helpers';
import { DnaEnums } from '@/services';
import { Label } from '../ui/label';

interface IProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedImage: IMedia;
  isEditing?: boolean;
}

const ImageEditOrReplaceDialog = ({ open, onOpenChange, selectedImage }: IProps) => {
  // States
  const [history, setHistory] = useState<{ prompt: string; url: string; meta_data?: any; uuid?: string }[]>([
    {
      prompt: selectedImage?.meta_data?.prompt || '__',
      url: selectedImage.url,
      meta_data: selectedImage?.meta_data,
      uuid: selectedImage.uuid,
    },
  ]);
  const [activeTab, setActiveTab] = useState('generate');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [imagePreviewModal, setImagePreviewModal] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState('');
  const { t, i18n } = useTranslation();

  // Hooks
  const { mutate: generateImage, isPending: isGenerating } = useGenerateImage();
  const { mutate: editImage, isPending: isEditing } = useEditImage();
  const { mutate: replaceImage, isPending: isReplacing } = useReplaceImage();
  // Metadata states
  const { data: allmetaData } = useGetSingleMetadata('');
  const subjectOptions = allmetaData?.filter((item) => item.type === 'subject') || [];
  const audienceOptions = allmetaData?.filter((item) => item.type === 'audience') || [];
  const languageOptions = allmetaData?.filter((item) => item.type === 'language') || [];
  const drawingStyles = allmetaData?.filter((item) => item.type === 'drawing_style') || [];

  const { notify } = useNotify();

  const { form, setFieldValue } = useForm({
    prompt: '',
    drawing_style_id: Number(selectedImage?.meta_data?.drawing_style_id),
    dimensions: selectedImage?.meta_data?.dimensions,
    quality: selectedImage?.meta_data?.quality,
    model: '',
    media_name: selectedImage?.name || '',
    language_id: selectedImage?.meta_data?.language?.id || '',
    subject_id: selectedImage?.meta_data?.subject?.id || '',
    audience_id: selectedImage?.meta_data?.audience?.id || '',
  });

  useEffect(() => {
    setFieldValue('prompt')(selectedImage?.meta_data?.prompt || '');
    setFieldValue('drawing_style_id')(Number(selectedImage?.meta_data?.drawing_style_id || ''));
    setFieldValue('dimensions')(selectedImage?.meta_data?.dimensions || '');
    setFieldValue('quality')(selectedImage?.meta_data?.quality || '');
    setFieldValue('model')(selectedImage?.meta_data?.model);
    setFieldValue('media_name')(selectedImage?.name || '');
    setFieldValue('language_id')(selectedImage?.meta_data?.language?.id || '');
    setFieldValue('subject_id')(selectedImage?.meta_data?.subject?.id || '');
    setFieldValue('audience_id')(selectedImage?.meta_data?.audience?.id || '');
  }, [selectedImage]);

  const handleImageGeneration = (promptText: string) => {
    if (!promptText.trim()) {
      notify.error(t('media.promptRequired'));
      return;
    }

    generateImage(
      {
        prompt: form.prompt,
        model: form.model,
        drawing_style_id: form.drawing_style_id,
        dimensions: form.dimensions,
        quality: form.quality,
      },
      {
        onSuccess: (data, { prompt }) => {
          setHistory((prev) => [
            ...prev,
            {
              prompt,
              url: data,
              meta_data: {
                prompt,
                drawing_style_id: form.drawing_style_id,
                model: form.model,
                dimensions: form.dimensions,
                quality: form.quality,
              },
            },
          ]);
          setFieldValue('prompt')(prompt);
          setCurrentIndex(history.length);
        },
      }
    );
  };

  const handleSelectTab = (tab: string) => {
    if (tab === 'edit') {
      setActiveTab(tab);
      setFieldValue('model')('gpt-image-1');
    } else {
      setActiveTab(tab);
    }
  };

  const tabs = [
    {
      title: t('Generate'),
      mode: 'generate',
    },
    {
      title: t('Edit'),
      mode: 'edit',
    },
  ];

  const handleImageEdit = (promptText: string) => {
    if (!promptText.trim()) {
      notify.error(t('media.promptRequired'));
      return;
    }

    editImage(
      {
        prompt: promptText,
        drawing_style_id:
          history[currentIndex]?.meta_data?.drawing_style_id || selectedImage?.meta_data?.drawing_style_id,
        image_url: history[currentIndex]?.url || selectedImage.url,
        image_uuid: history[currentIndex]?.uuid || selectedImage.uuid,
      },
      {
        onSuccess: (data, { prompt }) => {
          setHistory((prev) => [
            ...prev,
            {
              prompt,
              url: data,
              meta_data: history[currentIndex]?.meta_data,
              uuid: history[currentIndex]?.uuid,
            },
          ]);
          setFieldValue(prompt)(prompt);
          setFieldValue('url')(data);
          setCurrentIndex(history.length);
        },
      }
    );
  };

  const handleEditImage = () => {
    handleImageEdit(form.prompt);
  };
  const handleGenerateImage = () => {
    handleImageGeneration(form.prompt);
  };

  const handleImageClick = (index: number, prompt: string, url: string) => {
    setCurrentIndex(index);
    setFieldValue('prompt')(prompt);
  };

  const handleDeleteHistoryItem = (index: number, e: React.MouseEvent) => {
    e.stopPropagation();
    if (index === 0) return;

    const newHistory = history.filter((_, i) => i !== index);
    setHistory(newHistory);

    if (currentIndex === index) {
      setCurrentIndex(0);
      setFieldValue('prompt')(newHistory[0]?.prompt || '');
    } else if (currentIndex > index) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleImagePreview = (imageUrl: string, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent triggering the card click
    setPreviewImageUrl(imageUrl);
    setImagePreviewModal(true);
  };

  const handleSubmit = () => {
    replaceImage(
      {
        media_uuid: selectedImage.uuid,
        dna_id: selectedImage.model_id,
        url: history[currentIndex]?.url || '',
        prompt: history[currentIndex]?.prompt || '',
        model: form.model,
        drawing_style_id: form.drawing_style_id,
        dimensions: form.dimensions,
        quality: form.quality,
        language_id: form.language_id,
        subject_id: form.subject_id,
        audience_id: form.audience_id,
        media_name: form.media_name,
      },
      {
        onSuccess: () => {
          notify.success(t('media.uploadSuccess'));
          onOpenChange(false);
        },
        onError: (error) => {
          notify.error(error.message || t('common.error'));
        },
      }
    );
  };

  return (
    <Modal open={open} onOpenChange={onOpenChange} width={'1200px'}>
      <Form id="image-generation-form" onSubmit={handleSubmit}>
        <Textarea
          value={form.prompt}
          onChange={setFieldValue('prompt')}
          placeholder="Enter a creative prompt..."
          className="p-3 border rounded-lg shadow-sm resize-none"
          rows={4}
        />
        <div className="grid grid-cols-8 gap-5 w-full py-5">
          {/* Sidebar */}
          <div className="col-span-2 max-h-[500px] overflow-y-auto border border-border rounded-md  p-4">
            <div className="flex flex-col-reverse gap-1.5">
              {history.map((item, index) => (
                <Card
                  key={index}
                  className={cn('p-2 rounded-lg flex items-center gap-2 cursor-pointer relative group', {
                    'outline-1 outline outline-primary': currentIndex === index,
                  })}
                  onClick={() => handleImageClick(index, item.prompt, item.url)}
                >
                  <img src={item.url} alt={`History ${index}`} className="w-10 h-10 rounded-md" />
                  <p className="text-sm truncate w-full">{item.prompt}</p>
                  {index !== 0 && (
                    <Button
                      variant="ghost"
                      type="button"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600"
                      onClick={(e) => handleDeleteHistoryItem(index, e)}
                    >
                      <Icon icon="gg:trash" width={12} />
                    </Button>
                  )}
                </Card>
              ))}
            </div>
          </div>
          <div className="w-full col-span-2 border border-border rounded-md flex flex-col h-full">
            <div className="p-4 space-y-2 flex-1 overflow-auto">
              <ComboboxInput
                name="model"
                label={t('analysis.models')}
                placeholder={t('analysis.dimensions.models')}
                options={DnaEnums.aiImageModels}
                value={form.model}
                disabled={activeTab === 'edit'}
                onChange={setFieldValue('model')}
                dropIcon
              />
              <ComboboxInput
                name={t('analysis.drawingStyles')}
                label={t('analysis.drawingStyles')}
                placeholder={t('analysis.drawingStyles.placeholder')}
                options={generateEnum(drawingStyles || [], 'id', 'name_en')}
                value={form.drawing_style_id}
                onChange={setFieldValue('drawing_style_id')}
              />
              <ComboboxInput
                name="dimensions"
                label={t('analysis.dimensions')}
                placeholder={t('analysis.dimensions.placeholder')}
                options={form.model === 'gpt-image-1' ? DnaEnums.gptImageDimensions : DnaEnums.dallImageDimensions}
                value={form.dimensions}
                onChange={setFieldValue('dimensions')}
                dropIcon
              />
              <ComboboxInput
                name="quality"
                label={t('analysis.quality')}
                placeholder={t('analysis.quality.placeholder')}
                options={
                  form.model === 'gpt-image-1'
                    ? DnaEnums.gptImageQualityOptions
                    : i18n.language === 'en'
                    ? DnaEnums.dallImageQualityOptionsEnglish
                    : DnaEnums.dallImageQualityOptionsArabic
                }
                value={form.quality}
                onChange={setFieldValue('quality')}
                dropIcon
              />
              <div className="flex flex-col gap-2 justify-start mt-2">
                <Label>{t('fileManager.tabs.mode')}</Label>
                <div className="w-fit border border-border flex rounded-md">
                  {tabs.map((tab, index) => (
                    <div
                      key={tab.title}
                      onClick={() => handleSelectTab(tab.mode)}
                      className={cn(
                        'cursor-pointer p-2.5 truncate px-5 text-sm font-medium relative after:opacity-0 transition-all duration-300 after:content-[""] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:h-0.5 after:w-8 after:bg-primary',
                        activeTab === tab.mode ? 'bg-primary/5 text-primary after:opacity-100' : 'text-[#6B7280]',
                        index > 0 && 'border-s border-border'
                      )}
                    >
                      {t(tab.title)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <div className="p-4 border-t border-border">
              <Button
                type="button"
                loading={isGenerating || isEditing}
                onClick={() => (activeTab === 'generate' ? handleGenerateImage() : handleEditImage())}
                className="w-full"
              >
                {activeTab === 'generate' ? t('media.generate') : t('media.edit')}
              </Button>
            </div>
          </div>
          {/* Main Content */}
          <div className="w-full col-span-4 h-[500px] border border-border rounded-md p-5">
            <div className="grid grid-rows-5 h-full">
              <div className="row-span-3 border border-border rounded-lg relative group">
                <img
                  src={history[currentIndex]?.url || '/placeholder.png'}
                  alt="Generated"
                  className="rounded-lg w-full h-full object-contain"
                />
                {history[currentIndex]?.url && (
                  <Button
                    variant="ghost"
                    size="sm"
                    type="button"
                    className="absolute top-2 right-2 h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity text-primary"
                    onClick={(e) => handleImagePreview(history[currentIndex]?.url, e)}
                  >
                    <Icon icon="lucide:expand" width={16} />
                  </Button>
                )}
              </div>
              <div className="grid gap-2 row-span-2 items-end">
                <div className="grid grid-cols-2 gap-2">
                  <TextInput
                    placeholder={t('media.name')}
                    name="media_name"
                    label={t('media.name')}
                    value={form?.media_name}
                    onChange={setFieldValue('media_name')}
                  />
                  <ComboboxInput
                    placeholder={t('media.language')}
                    name="language_id"
                    label={t('media.language')}
                    options={generateEnum(languageOptions, 'id', 'name_en')}
                    value={form.language_id}
                    onChange={setFieldValue('language_id')}
                  />
                </div>
                <div className="grid grid-cols-2 gap-2 items-center">
                  <ComboboxInput
                    placeholder={t('media.subject')}
                    name="subject_id"
                    label={t('media.subject')}
                    options={generateEnum(subjectOptions, 'id', 'name_en')}
                    value={form.subject_id}
                    onChange={setFieldValue('subject_id')}
                  />
                  <ComboboxInput
                    placeholder={t('media.audience')}
                    name="audience_id"
                    label={t('media.audience')}
                    options={generateEnum(audienceOptions, 'id', 'name_en')}
                    value={form.audience_id}
                    onChange={setFieldValue('audience_id')}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex items-center justify-end border-t border-border pt-5">
          <Button type="submit" className="min-w-28 flex items-center gap-2" loading={isReplacing}>
            <Icon icon="solar:upload-minimalistic-bold" width={19} />
            {selectedImage.url === history[currentIndex]?.url ? t('media.img.edit') : t('media.replace')}
          </Button>
        </div>
      </Form>

      {/* Image Preview Modal */}
      <Modal open={imagePreviewModal} onOpenChange={setImagePreviewModal} width="1000px" className="max-w-none">
        <div className="flex flex-col items-center justify-center p-4">
          <div className="relative max-w-full max-h-[80vh] overflow-hidden">
            <img src={previewImageUrl} alt="Full Size Preview" className="max-w-full max-h-full object-contain" />
          </div>
        </div>
      </Modal>
    </Modal>
  );
};

export default ImageEditOrReplaceDialog;
