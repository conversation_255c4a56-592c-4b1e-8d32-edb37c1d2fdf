import { asField } from '../hocs/field';
import { Textarea as ShadcnTextarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  errorMessage?: string;
  rows?: number;
  value?: string | any;
  onChange?: any;
  name: string;
}
export const Textarea = asField(
  ({ value, name, label = '', errorMessage, onChange = () => {}, rows = 5, ...props }: TextareaProps) => {
    return (
      <div>
        {label && (
          <div className="mb-2">
            <Label htmlFor={name}>{label}</Label>
          </div>
        )}

        <ShadcnTextarea
          className="resize-none"
          rows={rows}
          id={name}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          {...props}
        />

        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);
