'use client';

import { useEffect, useState } from 'react';
import { asField } from '@/components/form/hocs/field';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslation } from 'react-i18next';

export const MultiCheckbox = asField(
  ({
    name,
    label,
    options,
    value,
    onChange,
    optionLabelKey = 'label',
    optionValueKey = 'value',
    errorMessage,
    validatorsScroll,
    customOptionLabelKey = false,
  }: any) => {
    const [selectedValues, setSelectedValues] = useState<string[]>(value || []);
    const { t } = useTranslation();

    useEffect(() => {
      if (validatorsScroll && errorMessage) {
        (document.getElementById(name) as any)?.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    }, [validatorsScroll, errorMessage, name]);

    const handleValueChange = (optionValue: string) => {
      const updatedValues = selectedValues.includes(optionValue)
        ? selectedValues.filter((v) => v !== optionValue)
        : [...selectedValues, optionValue];
      setSelectedValues(updatedValues);
      onChange(updatedValues);
    };

    return (
      <div className="space-y-4 -mt-4">
        <Label htmlFor={name} className="text-base font-medium">
          {label}
        </Label>
        <ScrollArea className="h-[200px] w-full rounded-md m-1">
          <div className="space-y-4 m-1">
            {options.map((option: any) => (
              <div key={option[optionValueKey]} className="flex items-center space-x-2">
                <Checkbox
                  id={`${name}-${option[optionValueKey]}`}
                  checked={selectedValues.includes(option[optionValueKey])}
                  onCheckedChange={() => handleValueChange(option[optionValueKey])}
                />
                <Label
                  htmlFor={`${name}-${option[optionValueKey]}`}
                  className="text-sm font-normal leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {customOptionLabelKey ? t(`options.${option[optionLabelKey]}`) : option[optionLabelKey]}
                </Label>
              </div>
            ))}
          </div>
        </ScrollArea>
        {errorMessage && <div className="mt-2 text-sm text-red-600 dark:text-red-500">{errorMessage}</div>}
      </div>
    );
  }
);
