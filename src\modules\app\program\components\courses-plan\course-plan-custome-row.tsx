import { useForm } from '@/index';
import { Button } from '@/components/ui/button';
import { useAddTopic } from '@/modules/app/course/apis/queries';
import { useTranslation } from 'react-i18next';
import { useQueryClient } from '@tanstack/react-query';

interface IProps {
  courseId: string;
}
const CoursePlanCustomeRow = ({ courseId }: IProps) => {
  // hooks
  const { mutate: addTopic, isPending, data } = useAddTopic();
  const { form } = useForm({
    new_topic_info: '',
  });

  const { t } = useTranslation();
  //ref
  const queryClient = useQueryClient();

  const handleSubmit = () => {
    addTopic(
      { id: data?.id || courseId, payload: form },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ['programPlan'] });
        },
      }
    );
  };

  return (
    <tr>
      <td colSpan={5} className="p-4 border-b border-border">
        <Button
          type="button"
          className="border-primary"
          loading={isPending}
          disabled={isPending}
          onClick={handleSubmit}
          variant={'outline'}
          size={'sm'}
        >
          + {t('programPlan.steps.coursesPlan.addUnit')}
        </Button>
      </td>
    </tr>
  );
};

export { CoursePlanCustomeRow };
