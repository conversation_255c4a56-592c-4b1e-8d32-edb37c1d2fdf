import { useQuery, useMutation, useQueryClient, useSuspenseQuery } from '@tanstack/react-query';
import {
  addSlide,
  addTransaltionSlideshow,
  deleteSlide,
  deleteSlideshow,
  generateSlideshow,
  getAllowedLanguages,
  getSharedSlideshow,
  getTranslatedSlideshowAllowedLanguages,
  listSlideshow,
  singleSlideShow,
  updateSlideshow,
} from './endpoints';
import { useNotify } from '@/index';
import { t } from 'i18next';

// hook to get slideshow

export const useGetSharedSlideshow = (id: string | number | undefined) => {
  return useQuery({
    queryKey: ['Shered-slideshow', id],
    queryFn: () => getSharedSlideshow(id || ''),
    enabled: !!id,
  });
};

export const useAddNewSlide = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: addSlide,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['slideshow'] });
      notify.success(t('dna.slideshow.slideAddedSuccessfully'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideAdditionFailed'));
    },
  });
};

export const useListSlideshow = (id: number) => {
  return useQuery({
    queryKey: ['slideshow', id],
    queryFn: () => listSlideshow(id),
    enabled: !!id,
  });
};
export const useGetSlideShowById = (id: number, slideshowId: number) => {
  return useQuery({
    queryKey: ['slideshow', id, slideshowId],
    queryFn: () => singleSlideShow(id, slideshowId),
    enabled: !!id,
  });
};
export const useGenerateSlideshow = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: generateSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideshowGeneratedSuccessfully'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};

export const useDeleteSlide = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteSlide,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideDeleted'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useDleteSlideshow = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: deleteSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideDeleted'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useUpdateSlideshowData = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: updateSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideUpdated'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};
export const useAddTransaltionSlideshow = () => {
  const queryClient = useQueryClient();
  const { notify } = useNotify();
  return useMutation({
    mutationFn: addTransaltionSlideshow,
    onSuccess: () => {
      queryClient.invalidateQueries();
      notify.success(t('dna.slideshow.slideUpdated'));
    },
    onError: () => {
      notify.error(t('dna.slideshow.slideshowGenerationFailed'));
    },
  });
};

export const useGetAllowedLanguages = (id: number | string) => {
  return useQuery({
    queryKey: ['allowed-languages', id],
    queryFn: () => getAllowedLanguages(id),
    enabled: !!id,
  });
};

export const useGetTranslatedSlideshowAllowedLanguages = ({
  dnaId,
  slideshowId,
}: {
  dnaId: number | string;
  slideshowId: number | string;
}) => {
  return useQuery({
    queryKey: ['translated-slideshow-allowed-languages', dnaId, slideshowId],
    queryFn: () => getTranslatedSlideshowAllowedLanguages({ dnaId, slideshowId }),
    enabled: !!dnaId && !!slideshowId,
  });
};
