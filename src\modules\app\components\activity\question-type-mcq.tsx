import { IActivityQuestion } from '../../tasks/types';
import { useTranslation } from 'react-i18next';
const QuestionTypeMcq = ({
  question,
  questionNumber,
}: {
  question: IActivityQuestion;
  questionNumber: number;
  setSelectedQuestions: any;
}) => {
  const { t } = useTranslation();
  return (
    <div>
      <h2 className="my-2 text-lg font-medium">
        {questionNumber}. {question.question}
      </h2>
      <ul className="text-slate-600">
        {Object.entries(question.options).map(([key, value]) => (
          <li key={key} className="">
            {key}) {value}
          </li>
        ))}
      </ul>
      <div className="pt-2">
        {t('dnaSinglePage.activity.answer')}: <strong>{question.answer}</strong>
      </div>
    </div>
  );
};

export default QuestionTypeMcq;
