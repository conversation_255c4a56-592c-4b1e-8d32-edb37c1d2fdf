import React, { useEffect } from 'react';
import { TextInput, Modal, Form } from '@/components';
import { t } from 'i18next';
import { Button } from '@/components/ui/button';
import { useForm, useValidate } from '@/hooks';

const RenameImageDialog = ({
  onOpenChange,
  isOpen,
  onSubmit,
  initialName,
}: {
  onOpenChange: (isOpen: boolean) => void;
  isOpen: boolean;
  onSubmit: (name: string) => void;
  initialName: string;
}) => {
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    name: initialName,
  });
  useEffect(() => {
    setFieldValue('name')(initialName);
  }, [initialName]);

  return (
    <Modal open={isOpen} onOpenChange={onOpenChange} modalHeader={t('media.renameMedia')}>
      <Form onSubmit={() => onSubmit(form.name)} className="my-3 flex flex-col">
        <TextInput
          label="Name"
          placeholder="Enter name"
          value={form.name}
          onChange={setFieldValue('name')}
          validators={[isRequired()]}
        />
        <Button className="mt-5 ms-auto"> {t('media.save')} </Button>
      </Form>
    </Modal>
  );
};

export default RenameImageDialog;
