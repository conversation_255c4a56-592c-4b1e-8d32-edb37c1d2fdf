import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  createTopic,
  deleteTopic,
  deleteTopics,
  updateTopic,
  generateDna,
  generateDNASingleTopic,
  generateDNATopic,
  getTopicById,
  updateTopicOrder,
} from './endpoints';
import { useNotify } from '@/hooks';
import { ITopicPayload } from '../types';
import { useTranslation } from 'react-i18next';
import { use } from 'marked';
export const useDeleteTopics = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteTopics,
    onSuccess: () => {
      notify.success('Topics Deleted Successfully!');
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useDeleteTopic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteTopic,
    onSuccess: () => {
      notify.success('Topic Deleted Successfully!');
      queryClient.invalidateQueries({ queryKey: ['topic'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useCreateTopic = () => {
  const { notify } = useNotify();
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: ITopicPayload) => createTopic(payload),
    onSuccess: () => {
      notify.success(t('notify.topicCreated'));
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useCreateDna = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateDna,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGenerateDNATopic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateDNATopic,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topic'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGenerateDNASingleTopic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: generateDNASingleTopic,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['topic'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useGetTopicById = (id: string | undefined) => {
  return useQuery({
    queryKey: ['topic', id],
    queryFn: () => (id ? getTopicById(id) : Promise.resolve(null)),
    enabled: !!id, // Only runs the query if `id` is not undefined or null  });
    refetchInterval: 3000,
  });
};

export const useUpdateTopic = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateTopic,
    onSuccess: () => {
      notify.success('Topic Updated Successfully!');
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};

export const useUpdateTopicOrder = () => {
  const { notify } = useNotify();
  const queryClient = useQueryClient();
  const { t } = useTranslation();
  return useMutation({
    mutationFn: updateTopicOrder,
    onSuccess: () => {
      notify.success(t('topic.notify.topicOrderUpdated'));
      queryClient.invalidateQueries({ queryKey: ['dna'] });
      queryClient.invalidateQueries({ queryKey: ['dnas'] });
      queryClient.invalidateQueries({ queryKey: ['course'] });
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['topics'] });
      queryClient.invalidateQueries({ queryKey: ['topic'] });
    },
    onError: (error: any) => {
      notify.error(error.message);
    },
  });
};
