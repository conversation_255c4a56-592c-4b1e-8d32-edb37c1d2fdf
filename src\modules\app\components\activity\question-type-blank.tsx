import { IActivityQuestion } from '../../tasks/types';
import { useTranslation } from 'react-i18next';
const QuestionTypeBlank = ({ question, questionNumber }: { question: IActivityQuestion; questionNumber: number }) => {
  const { t } = useTranslation();
  return (
    <div>
      <h2 className="my-2 text-lg font-medium">
        {questionNumber}. {question.question}
      </h2>
      <div className="pt-4">
        {t('dnaSinglePage.activity.answer')}: <strong>{question.answer}</strong>
      </div>
    </div>
  );
};

export default QuestionTypeBlank;
