.custom-slideshow-container {
  .ck.ck-toolbar {
    @apply bg-background !important;
  }

  /* Editor full height for 16:9 aspect ratio */
  .ck-editor {
    height: 100% !important;
  }

  .ck-editor__editable {
    height: 100% !important;
    min-height: 100% !important;
  }

  /* Responsive Global Styles */
  .slide-title {
    font-size: clamp(0.8rem, 3vw, 2rem);
    font-weight: bold;
    margin-bottom: clamp(0.5rem, 2vw, 1.5rem);
    text-align: center;
    line-height: 1.2;
  }

  .card {
    flex: 1;
    border: 1px solid #eee;
    padding: clamp(0.8rem, 2vw, 1.5rem);
    border-radius: clamp(4px, 0.5vw, 8px);
    box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  }

  .list {
    list-style-type: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: clamp(0.5rem, 1.5vw, 1rem);
    margin: 0;
  }

  .fixed-144px img {
    width: clamp(60px, 12vw, 144px);
    height: clamp(60px, 12vw, 144px);
    max-width: clamp(60px, 12vw, 144px);
    max-height: clamp(60px, 12vw, 144px);
    object-fit: cover;
  }

  .fixed-320px img {
    width: clamp(120px, 25vw, 320px);
    height: clamp(120px, 25vw, 320px);
    max-width: clamp(120px, 25vw, 320px);
    max-height: clamp(120px, 25vw, 320px);
    object-fit: cover;
  }

  /* Pros and Cons Section */
  .comparison-slide {
    width: 100%;

    .pros-cons-container {
      display: flex;
      justify-content: space-between;
      gap: clamp(0.8rem, 2vw, 1.25rem);
      margin-top: clamp(0.5rem, 1vw, 0.625rem);
      width: 100%;

      ol.list {
        list-style: none;
        counter-reset: item;
        padding: 0;
        width: clamp(120px, 25vw, 320px);
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: clamp(0.5rem, 1.5vw, 1rem);
      }

      ol.list > li {
        counter-increment: item;
        position: relative;
        display: flex;
        gap: clamp(0.4rem, 1vw, 0.625rem);
        font-size: clamp(0.7rem, 1.5vw, 1rem);
        line-height: 1.3;
      }

      ol.list > li::before {
        content: counter(item);
        transform: translateY(0.2em);
        width: clamp(16px, 2.5vw, 24px);
        height: clamp(16px, 2.5vw, 24px);
        min-width: clamp(16px, 2.5vw, 24px);
        min-height: clamp(16px, 2.5vw, 24px);
        background: hsl(var(--primary));
        color: #fff;
        font-size: clamp(0.6rem, 1.2vw, 0.75rem);
        border-radius: 50%;
        text-align: center;
        font-weight: bold;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .pros li::before {
        background: green;
      }

      .cons li::before {
        background: red !important;
      }

      .pros-title {
        color: rgb(0, 165, 0);
        font-size: clamp(0.9rem, 2vw, 1.25rem);
        font-weight: bold;
      }

      .cons-title {
        color: red;
        font-size: clamp(0.9rem, 2vw, 1.25rem);
        font-weight: bold;
      }
    }
  }

  /* Hierarchy Slide */
  .hierarchy-slide {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: clamp(1rem, 3vw, 2rem);
    margin: clamp(1rem, 3vw, 2rem);

    .hierarchy-title h2 {
      font-size: clamp(1.2rem, 4vw, 2rem);
      font-weight: bold;
      text-align: center;
      background: linear-gradient(to right, #1d4ed8, #3b82f6);
      -webkit-background-clip: text;
      color: transparent;
    }

    .hierarchy-level {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .node,
    .node-child {
      padding: clamp(0.5rem, 1.5vw, 1rem);
      border-radius: clamp(4px, 0.5vw, 8px);
      width: clamp(200px, 18vw, 240px);
      text-align: center;
      font-weight: 600;
      font-size: clamp(0.7rem, 1.5vw, 1rem);
      transition: all 0.3s ease;
      background: #fff;
      border: 2px solid #e5e7eb;
      line-height: 1.2;
    }

    .root-node {
      background: #3b82f6;
      color: #fff;
      border: 2px solid #3b82f6;
      box-shadow: 0 0 6px rgba(59, 130, 246, 0.2);
    }

    .vertical-line {
      width: clamp(1px, 0.2vw, 2px);
      height: clamp(1rem, 2.5vw, 2rem);
      background: linear-gradient(to bottom, #93c5fd, #d1d5db);
      margin: clamp(0.25rem, 0.8vw, 0.5rem) auto;
    }

    .children-row {
      position: relative;
      display: flex;
      gap: clamp(1rem, 3vw, 2rem);
      margin-top: clamp(0.5rem, 1.5vw, 1rem);
    }

    .children-row .horizontal-line {
      position: absolute;
      top: clamp(0.5rem, 1.5vw, 1rem);
      left: 0;
      right: 0;
      height: clamp(1px, 0.2vw, 2px);
      background: linear-gradient(to right, #d1d5db, #d1d5db);
      z-index: -1;
    }
  }

  .definition-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: clamp(0.5rem, 1.5vw, 1rem);
    width: 100%;

    .definition-content {
      display: flex;
      /* flex-direction: column; */
      align-items: center;
      gap: clamp(1.5rem, 4vw, 3rem);
    }

    /* @media (min-width: 1024px) {
      .definition-content {
        flex-direction: row;
      }
    } */

    img {
      width: clamp(120px, 25vw, 320px) !important;
      max-width: clamp(120px, 25vw, 320px) !important;
      height: clamp(120px, 25vw, 320px) !important;
      max-height: clamp(120px, 25vw, 320px) !important;
      object-fit: cover;
      border-radius: clamp(4px, 0.5vw, 8px);
    }

    .definition-text {
      display: flex;
      flex-direction: column;
      gap: clamp(0.75rem, 2vw, 1.25rem);
      padding: clamp(0.125rem, 0.5vw, 0.25rem);
    }

    .definition-title {
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: bold;
      margin: 0;
    }

    .definition-text p {
      font-size: clamp(0.8rem, 2vw, 1rem);
      line-height: 1.5;
      margin: 0;
    }
  }

  /* Explanation Slide */
  .explanation-slide {
    width: 100%;
    margin: 0 auto;

    .explanation-img-wrapper {
      display: flex;
      justify-content: center;
      margin-bottom: clamp(1.5rem, 4vw, 2.5rem);
    }

    .explanation-text {
      text-align: center;
      display: flex;
      justify-content: space-around;
      align-items: center;
      gap: clamp(1.5rem, 4vw, 3rem);
      font-size: clamp(0.8rem, 2vw, 1rem);
      line-height: 1.4;
    }
  }

  /* Header Slide */
  .header-slide {
    width: 100%;

    .header-title {
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: bold;
    }
  }

  /* List Slide */
  .list-slide {
    text-align: center;
    width: 100%;

    .list-slide-title {
      margin-bottom: clamp(2rem, 5vw, 4rem);
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: bold;
    }

    .list-slide-items {
      gap: clamp(1.5rem, 4vw, 3rem);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
    }

    @media (min-width: 768px) {
      .list-slide-items {
        flex-direction: row;
        flex-wrap: nowrap;
      }
    }

    .list-slide-item {
      display: flex;
      flex-direction: column;
      gap: clamp(0.75rem, 2vw, 1.25rem);
      align-items: center;
    }

    .list-slide-item p {
      font-size: clamp(0.7rem, 1.5vw, 1rem);
      line-height: 1.3;
      margin: 0;
    }

    img {
      width: clamp(60px, 12vw, 144px) !important;
      max-width: clamp(60px, 12vw, 144px) !important;
      height: clamp(60px, 12vw, 144px) !important;
      max-height: clamp(60px, 12vw, 144px) !important;
      border-radius: clamp(4px, 0.5vw, 8px);
      object-fit: cover;
    }
  }

  /* Main Ideas Slide */
  .main-ideas-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: clamp(0.75rem, 2vw, 1.25rem);
    justify-content: center;
    align-items: center;

    .main-ideas-title {
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: bold;
    }

    .main-ideas-points ul li {
      margin-bottom: clamp(0.25rem, 0.8vw, 0.5rem);
      font-size: clamp(0.8rem, 2vw, 1rem);
      line-height: 1.4;
    }
  }

  /* Summary Slide */
  .summary-slide {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: clamp(0.5rem, 1.5vw, 0.75rem);
    width: 100%;

    .summary-slide-title {
      font-size: clamp(1rem, 3vw, 1.5rem);
      font-weight: bold;
    }

    .summary-slide-description {
      font-size: clamp(0.9rem, 2.5vw, 1.125rem);
      line-height: 1.4;
    }
  }

  /* Global responsive text styling */
  p,
  span,
  div {
    font-size: clamp(0.8rem, 2vw, 1rem);
    line-height: 1.4;
  }

  /* Responsive margins and padding */
  .slide-content {
    padding: clamp(1rem, 3vw, 2rem);
    margin: clamp(0.5rem, 1vw, 1rem);
  }
}

.fullscreen-slideshow-container {
  /* Fullscreen base setup */
  width: 100vw;
  height: calc(100vh - 152px);
  margin: 0;
  padding: 2rem;

  box-sizing: border-box;

  /* Global Styles */
  .slide-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 2rem;
  }

  .card {
    flex: 1;
    border: 1px solid #eee;
    padding: 2rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    min-height: 400px;
    transition: box-shadow 0.3s ease;
  }

  .list {
    list-style-type: none;
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin: 0;
  }

  /* Image size adjustments */

  .fixed-320px img {
    width: 400px;
    height: 400px;
    max-width: 400px;
    max-height: 400px;
    object-fit: cover;
    border-radius: 12px;
  }

  /* Pros and Cons Section */
  .comparison-slide {
    padding-left: 4rem;
    padding-right: 4rem;
    width: 100%;
    .slide-title {
      margin-bottom: 5rem;
    }
    .pros-cons-container {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-top: 10px;
      width: 100%;

      ol.list {
        list-style: none;
        counter-reset: item;
        padding: 0;
        margin: 0;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      ol.list > li {
        font-size: 25px;
        counter-increment: item;
        position: relative;
        display: flex;
        gap: 10px;
      }

      ol.list > li::before {
        content: counter(item);
        transform: translateY(0.2em);
        width: 24px;
        height: 24px;
        min-width: 24px;
        min-height: 24px;
        background: hsl(var(--primary));
        color: #fff;
        font-size: 12px;
        border-radius: 50%;
        margin-top: 7px;
        text-align: center;
        font-weight: bold;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }
      .pros li::before {
        background: green;
      }
      .cons li::before {
        background: red !important;
      }
      .pros-title {
        color: rgb(0, 165, 0);
      }

      .cons-title {
        color: red;
      }
    }
  }

  /* Hierarchy Slide */
  .hierarchy-slide {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 2rem;
    margin: 2rem;

    .hierarchy-title h2 {
      font-size: 2rem;
      font-weight: bold;
      text-align: center;
      background: linear-gradient(to right, #1d4ed8, #3b82f6);
      -webkit-background-clip: text;
      color: transparent;
    }

    .hierarchy-level {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .node,
    .node-child {
      padding: 1rem;
      border-radius: 8px;
      width: 300px;
      text-align: center;
      font-weight: 600;
      transition: all 0.3s ease;
      background: #fff;
      border: 2px solid #e5e7eb;
    }

    .root-node {
      background: #3b82f6;
      color: #fff;
      border: 2px solid #3b82f6;
      box-shadow: 0 0 6px rgba(59, 130, 246, 0.2);
    }

    .vertical-line {
      width: 5px;
      height: 2rem;
      background: linear-gradient(to bottom, #93c5fd, #d1d5db);
      margin: 0.5rem auto;
    }

    .children-row {
      position: relative;
      display: flex;
      gap: 2rem;
      margin-top: 1rem;
    }

    .children-row .horizontal-line {
      position: absolute;
      top: 1rem;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(to right, #d1d5db, #d1d5db);
      z-index: -1;
    }
  }

  /* Definition Slide */
  .definition-slide {
    padding: 2rem;

    .definition-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4rem;
    }

    img {
      max-width: 450px !important;
      max-height: 450px !important;
      border-radius: 1rem;
    }

    .definition-text {
      gap: 2rem;
    }

    .definition-title {
      font-size: 2.5rem;
    }

    .definition-text p {
      font-size: 1.25rem !important;
      line-height: 1.7;
    }
  }

  /* Explanation Slide */
  .explanation-slide {
    width: 100% !important;
    .explanation-text {
      gap: 4rem;
      font-size: 1.25rem;
    }
  }
  /* Header Slide */
  .header-slide {
    .header-title {
      font-size: 3rem;
    }
  }
  .explanation-img-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100% !important;
    border-radius: 1rem;
    margin-bottom: 4rem;

    .image {
      img {
        max-width: 500px !important;
        max-height: 500px !important;
      }
    }
  }
  /* List Slide */
  .list-slide {
    .list-slide-title {
      margin-bottom: 6rem;
      font-size: 2.5rem;
    }

    .list-slide-items {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      align-items: center;
      gap: 3rem;
      .list-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }
    }

    .list-slide-item {
      gap: 2rem;
    }

    img {
      max-width: 220px !important;
      max-height: 220px !important;
    }
  }

  /* Main Ideas Slide */
  .main-ideas-slide {
    gap: 2.5rem;

    .main-ideas-title {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }

    .main-ideas-points {
      font-size: 1.25rem;

      ul li {
        margin-bottom: 1.5rem;
      }
    }
  }

  /* Summary Slide */
  .summary-slide {
    gap: 2rem;

    .summary-slide-title {
      font-size: 2.5rem;
      margin-bottom: 1.5rem;
    }

    .summary-slide-description {
      font-size: 1.5rem;
      line-height: 1.7;
    }
  }

  /* Global Fullscreen Slide Styles */
  .slide {
    width: 100% !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
    justify-content: center !important;
    align-items: center !important;
    padding: 2rem !important;
  }

  /* Scale adjustments for better fullscreen display */
  .pros-cons-container,
  .hierarchy-slide,
  .definition-content,
  .list-slide-items {
    transform: scale(1.1);
    transform-origin: center;
  }

  /* Media query for ultra-wide screens */
  @media (min-width: 1920px) {
    padding: 3rem;

    .slide-title {
      font-size: 3rem;
    }

    .card {
      padding: 2.5rem;
    }
  }
}

.voice-over-dialog .rhap_main {
  display: flex;
  gap: 5px;
  align-items: center;
}
.voice-over-dialog .rhap_play-pause-button {
  width: 20px !important;
  height: 20px !important;
}
.voice-over-dialog .rhap_main > :not([hidden]) ~ :not([hidden]) {
  flex: none !important;
  margin: 0 !important;
  margin-top: 0 !important;
}
.voice-over-dialog .rhap_time {
  font-weight: 600 !important;
  font-size: 14px;
  color: #6b7280 !important;
  margin-bottom: 2px;
}
.voice-over-dialog .rhap_progress-bar {
  margin-top: 3px;
}
.voice-over-dialog .rhap_progress-filled {
  background-color: hsl(var(--primary)) !important;
}
.voice-over-dialog .rhap_progress-indicator {
  display: none !important;
}
.voice-over-dialog .rhap_progress-section {
  display: flex;
  align-items: center;
}
.voice-over-dialog .rhap_controls-section {
  width: 20px;
  display: block !important;
}
