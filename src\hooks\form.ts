import { useImmer as useState } from 'use-immer';
import { set } from 'object-path';

export const useForm = (payload: any) => {
  const backup = JSON.parse(JSON.stringify(payload));
  const [form, setFormValue] = useState(payload);

  const setFieldValue =
    (path: any, type = (a: any) => a) =>
    (value: any) => {
      setFormValue((draft: any) => {
        set(draft, path, type(value));
      });
    };

  const resetForm = () => {
    setFieldValue(backup);
  };

  return {
    form,
    resetForm,
    setFormValue,
    setFieldValue,
  };
};
