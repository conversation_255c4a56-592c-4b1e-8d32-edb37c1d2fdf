import { Icon } from '@/components';
import { Combobox } from '@/components/combo-box';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { t } from 'i18next';
import { useAtom } from 'jotai';
import { useRef, useState } from 'react';
import {
  captionsAtom,
  isControlsVisibleAtom,
  isFullscreenAtom,
  selectedSlideshowAudioLanguageAtom,
  selectedSlideshowSlidesLanguageAtom,
  selectedSlideshowSubtitleLanguageAtom,
  slideshowPlayDataAtom,
} from '../../../store';
import { Slider } from '@/components/ui/slider';
import { generateEnum } from '@/utils/helpers';
const icons = {
  play: <Icon icon="solar:play-bold" className="text-primary" width={20} />,
  pause: <Icon icon="solar:pause-bold" className="text-primary" width={20} />,
  volumeHigh: <Icon icon="ph:speaker-high-bold" width={20} />,
  volumeLow: <Icon icon="ph:speaker-low-bold" width={20} />,
  volumeMute: <Icon icon="ph:speaker-x-bold" width={20} />,
};
const PlayerSettings = ({ toggleFullscreen, audioRef }: { toggleFullscreen: any; audioRef: any }) => {
  const [isVolumeHovered, setIsVolumeHovered] = useState(false);
  const [isControlsVisible, setIsControlsVisible] = useAtom(isControlsVisibleAtom);
  const [volume, setVolume] = useState(1);
  const [previousVolume, setPreviousVolume] = useState(1);
  const volumeTimeoutRef = useRef<NodeJS.Timeout>();
  const [captions, setCaptions] = useAtom(captionsAtom);
  const [isFullscreen] = useAtom(isFullscreenAtom);
  const [slideshowPlayData, setSlideshowPlayData] = useAtom(slideshowPlayDataAtom);
  const [selectedSlideshowSlidesLanguage, setSelectedSlideshowSlidesLanguage] = useAtom(
    selectedSlideshowSlidesLanguageAtom
  );
  const [selectedSlideshowAudioLanguage, setSelectedSlideshowAudioLanguage] = useAtom(
    selectedSlideshowAudioLanguageAtom
  );
  const [selectedSlideshowSubtitleLanguage, setSelectedSlideshowSubtitleLanguage] = useAtom(
    selectedSlideshowSubtitleLanguageAtom
  );

  const handleVolumeChange = (value: number[]) => {
    const newVolume = value[0];
    setVolume(newVolume);
    if (audioRef.current) {
      setIsControlsVisible(true);
      audioRef.current.volume = newVolume;
    }
  };
  const getVolumeIcon = () => {
    if (volume === 0) return icons.volumeMute;
    if (volume < 0.5) return icons.volumeLow;
    return icons.volumeHigh;
  };

  const toggleMute = () => {
    if (volume > 0) {
      setPreviousVolume(volume);
      setVolume(0);
      if (audioRef.current) {
        audioRef.current.volume = 0;
      }
    } else {
      setVolume(previousVolume);
      if (audioRef.current) {
        audioRef.current.volume = previousVolume;
      }
    }
  };

  return (
    <>
      <div
        className="relative flex items-center gap-1"
        onMouseEnter={() => {
          if (volumeTimeoutRef.current) {
            clearTimeout(volumeTimeoutRef.current);
          }
          setIsVolumeHovered(true);
        }}
        onMouseLeave={() => {
          volumeTimeoutRef.current = setTimeout(() => {
            setIsVolumeHovered(false);
          }, 300);
        }}
      >
        <Button variant="ghost" size="sm" className="p-0 h-auto hover:text-primary" onClick={toggleMute}>
          {getVolumeIcon()}
        </Button>
        <div
          className={`
                    h-8 flex items-center
                    transition-opacity duration-200
                    ${isVolumeHovered ? 'w-24 opacity-100' : 'w-0 opacity-0 pointer-events-none'}
                  `}
        >
          <Slider value={[volume]} max={1} step={0.01} onValueChange={handleVolumeChange} className="w-full" />
        </div>
      </div>
      <Icon
        onClick={() => setCaptions(!captions)}
        className={`cursor-pointer ${captions ? 'text-primary' : 'text-gray-400'}`}
        icon="gg:captions"
        width={25}
      />

      <Popover>
        <PopoverTrigger>
          <Icon
            icon="material-symbols:settings-outline-rounded"
            width={25}
            className="cursor-pointer data-[state=open]:text-primary data-[state=closed]:text-gray-400"
          />
        </PopoverTrigger>
        <PopoverContent className="w-56" align="end" side="bottom">
          <h5 className="text-gray-500 text-sm">{t('slideshow.player.settings.languageSettings')}</h5>
          <div className="space-y-3 mt-3">
            <Combobox
              value={selectedSlideshowSlidesLanguage}
              options={generateEnum(slideshowPlayData?.additional_data || [], 'id', 'name_en')}
              placeholder={t('slideshow.player.settings.slides')}
              name={t('slideshow.player.settings.slides')}
              onChange={setSelectedSlideshowSlidesLanguage}
            />
            <Combobox
              value={selectedSlideshowAudioLanguage}
              options={generateEnum(slideshowPlayData?.additional_data || [], 'id', 'name_en')}
              placeholder={t('slideshow.player.settings.audio')}
              name={t('slideshow.player.settings.audio')}
              onChange={setSelectedSlideshowAudioLanguage}
            />
            <Combobox
              value={selectedSlideshowSubtitleLanguage}
              options={generateEnum(slideshowPlayData?.additional_data || [], 'id', 'name_en')}
              placeholder={t('slideshow.player.settings.subtitles')}
              name={t('slideshow.player.settings.subtitles')}
              onChange={setSelectedSlideshowSubtitleLanguage}
            />
          </div>
        </PopoverContent>
      </Popover>

      <Icon
        icon={isFullscreen ? 'hugeicons:arrow-collapse' : 'hugeicons:arrow-expand'}
        width={25}
        onClick={toggleFullscreen}
        className={`cursor-pointer ${isFullscreen ? 'text-primary' : 'text-gray-400'}`}
      />
    </>
  );
};

export default PlayerSettings;
