import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { t } from 'i18next';
import { IActivityQuestion } from '@/modules/app/tasks/types';
import { Label } from '@/components/ui/label';
import { useUpdateActivity } from '../../tasks/apis/queries';
import { useParams } from 'react-router-dom';

interface EditBlankQuestionDialogProps {
  question: IActivityQuestion;
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
}

const EditBlankQuestionDialog: React.FC<EditBlankQuestionDialogProps> = ({ question, isOpen, onOpenChange }) => {
  const [questionText, setQuestionText] = useState(question.question);
  const [answerText, setAnswerText] = useState(question.answer);
  const { mutate, isPending } = useUpdateActivity();
  const { taskId } = useParams();
  const handleSave = () => {
    mutate(
      {
        activity_id: question.id,
        code: taskId || '',
        payload: {
          question: questionText,
          answer: answerText,
          feedback: question.feedback,
        },
      },
      {
        onSuccess: () => {
          onOpenChange(false);
        },
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button variant="outline">{t('activity.editBlankQuestion')}</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogTitle>{t('activity.editBlankQuestion')}</DialogTitle>
        <div className="space-y-4">
          {/* Question Text */}
          <div className="space-y-2">
            <Label>{t('activity.question')}</Label>
            <Textarea
              value={questionText}
              onChange={(e) => setQuestionText(e.target.value)}
              placeholder={t('activity.enterTheQuestion')}
              className="w-full"
            />
          </div>
          {/* Answer */}
          <div className="space-y-2">
            <Label>{t('activity.answer')}</Label>
            <Textarea
              value={answerText}
              onChange={(e) => setAnswerText(e.target.value)}
              placeholder={t('activity.enterTheAnswer')}
              className="w-full"
            />
          </div>
        </div>
        <DialogFooter>
          <Button loading={isPending} onClick={handleSave} className="px-6">
            {t('activity.save')}
          </Button>
          <Button onClick={() => onOpenChange(false)} variant="outline">
            {t('activity.cancel')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBlankQuestionDialog;
