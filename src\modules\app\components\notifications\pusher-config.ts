import Pusher from 'pusher-js';

interface PusherConfig {
  cluster: string;
  authEndpoint?: string;
  auth: {
    headers: {
      Authorization: string;
    };
  };
  ensureTLS: boolean;
}
let finalUrl = (import.meta as any).env.VITE_API_BASE_URL.replace('/api', '');
const config: PusherConfig = {
  cluster: 'ap2',
  authEndpoint: `${finalUrl}/broadcasting/auth`, // <PERSON><PERSON>'s endpoint
  ensureTLS: true,
  auth: {
    headers: {
      // For API authentication:
      Authorization: `Bearer ${localStorage.getItem('token')}`,
    },
  },
};

const pusher = new Pusher('fca72d9f3a95fd5be3bf', config);

pusher.connection.bind('state_change', (state: any) => {
  console.log('Pusher connection state changed to:', state.current);
});

pusher.connection.bind('connected', () => {
  console.log('✅ Successfully connected to Pusher');
});

pusher.connection.bind('failed', () => {
  console.error('❌ Pusher connection failed');
});

export default pusher;
