import { ADD_TITLE, Api, GENERATE_DNA_FORM_TOPIC } from '@/services';
import { ITopic, ITopicPayload } from '../types';

export const createTopic = async (payload: ITopicPayload) => {
  const { data } = await Api.post(`/topic/generate`, payload);
  return data.data;
};

export const generateDna = async (form: { id: number; audio_length: string }) => {
  const { data } = await Api.put(GENERATE_DNA_FORM_TOPIC, form);
  return data.data;
};

export const deleteTopic = async (id: number) => {
  const { data } = await Api.delete(`/topic/${id}`);
  return data.data;
};
export const deleteTopics = async (ids: number[]) => {
  const { data } = await Api.delete(`/topic/topics/delete`, {
    _method: 'delete',
    ids: ids,
  });
  return data.data;
};

export const getTopicById = async (id: string): Promise<ITopic> => {
  const { data } = await Api.get(`/topic/get-topic/${id}`);
  return data.data;
};

export const generateDNATopic = async (id: string) => {
  const { data } = await Api.post(ADD_TITLE, { topic_id: id });
  return data.data;
};

export const generateDNASingleTopic = async ({ id, title }: { id: string; title: string }) => {
  const { data } = await Api.post(ADD_TITLE, { topic_id: id, title: title });
  return data.data;
};

export const updateTopic = async ({ id, payload }: any) => {
  const { data } = await Api.put(`/topic/update/${id}`, payload);
  return data.data;
};

export const updateTopicOrder = async ({ id, next_topic_id }: any) => {
  const { data } = await Api.put(`/topic/${id}/move-to-course`, { next_topic_id });
  return data.data;
};
