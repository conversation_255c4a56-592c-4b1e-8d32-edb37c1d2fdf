import { useForm, TextInput, useValidate, Form } from '@/index';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useUpdateCourseTitle } from '../apis/queries';
import { ITask } from '../types';
interface IProps {
  task: ITask | null;
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
}
const EditCourseDialog = ({ task, open, setOpen }: IProps) => {
  // hooks
  const { isRequired } = useValidate();
  const { form, setFieldValue } = useForm({
    course_title: task?.content?.tool_data?.title || '',
    course_id: task?.content?.tool_id,
  });

  const { mutate: updateCourse, isPending } = useUpdateCourseTitle();
  const { t } = useTranslation();
  const handleSubmit = (e: Event) => {
    e.preventDefault();
    updateCourse(
      { code: task?.code || '', payload: form },
      {
        onSuccess: () => {
          setOpen(false);
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{t('updateCourse')}</DialogTitle>
          <DialogDescription>{t('updateCourseDescription')}</DialogDescription>
        </DialogHeader>

        <Form onSubmit={handleSubmit} className={`pt-5 flex flex-col`}>
          <TextInput
            name="title"
            label={t('title')}
            value={form.course_title}
            onChange={setFieldValue('course_title')}
            isRequired
            validators={[isRequired()]}
          />
          <Button type="submit" className="mt-4 ms-auto" loading={isPending} disabled={isPending}>
            {t('CoursePlanCreationPage.submit')}
          </Button>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export { EditCourseDialog };
