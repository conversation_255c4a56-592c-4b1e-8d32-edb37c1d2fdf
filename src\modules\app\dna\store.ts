import { atom } from 'jotai';
import { ILanguage } from '../dashboard/transaltion/types';
import { ISlide } from './apis/slideshow/types';

export const slideshowPlayDataAtom = atom<{ items: any; additional_data: ILanguage[] }>();
export const selectedSlideshowSlidesLanguageAtom = atom<string | number>('0');
export const selectedSlideshowAudioLanguageAtom = atom<string | number>('0');
export const selectedSlideshowSubtitleLanguageAtom = atom<string | number>('0');
export const selectedSlideshowIdAtom = atom<number | string>('0');

export const finalSlidesAtom = atom((get) => {
  const slideshows = get(slideshowPlayDataAtom)?.items;
  const contentLang = get(selectedSlideshowSlidesLanguageAtom);
  const audioLang = get(selectedSlideshowAudioLanguageAtom);
  const subtitleLang = get(selectedSlideshowSubtitleLanguageAtom);
  const initialId = get(selectedSlideshowIdAtom);

  const baseSlides: ISlide[] =
    slideshows?.find((item: any) => item.id === initialId || item.uuid === initialId)?.slides || [];

  const slides: ISlide[] = baseSlides.map((slide: ISlide, index: number) => {
    return {
      ...slide,
      content: slideshows.find((item: any) => item.language_id == contentLang)?.slides?.[index].content || '',
      voice_over_url:
        slideshows.find((item: any) => item.language_id == audioLang)?.slides?.[index].voice_over_url || '',
      voice_over: slideshows.find((item: any) => item.language_id == subtitleLang)?.slides?.[index].voice_over || '',
    };
  });
  return slides;
});

export const currentTimeAtom = atom(0);
export const currentTrackIndexAtom = atom(0);
export const totalDurationAtom = atom(0);
export const previousVolumeAtom = atom(1);
export const isFullscreenAtom = atom(false);
export const captionsAtom = atom(false);
export const isControlsVisibleAtom = atom(false);
export const atomsCleaner = atom(null, (get, set, update) => {
  set(currentTimeAtom, 0);
  set(currentTrackIndexAtom, 0);
  set(totalDurationAtom, 0);
  set(previousVolumeAtom, 1);
  set(isFullscreenAtom, false);
  set(captionsAtom, false);
  set(isControlsVisibleAtom, false);
});
