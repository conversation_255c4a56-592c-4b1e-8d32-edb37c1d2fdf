import { Api } from '@/index';
import { IeditCourse, IeditLearning, IeditSkill } from '../types';

export const findProgramPlanById = async (id: string): Promise<any> => {
  const { data } = await Api.get(`/program-plan/show/${id}`);
  return data.data;
};

export const programInfoStart = async (payload: any): Promise<any> => {
  const { data } = await Api.post(`/program-plan/step-one`, payload);
  return data?.data;
};

export const programStepTwo = async (id: any): Promise<any> => {
  const { data } = await Api.post(`/program-plan/step-two/${id}`);
  return data?.data;
};

export const addNewLearningOutcome = async (id: any) => {
  const { data } = await Api.post(`/program-plan/add-learning-outcome/${id}`);
  return data.data;
};

export const addNewSkill = async (id: any) => {
  const { data } = await Api.post(`/program-plan/add-skill/${id}`);
  return data.data;
};

export const addNewCourse = async ({ id, courses }: { id: any; courses: any }) => {
  const { data } = await Api.post(`/program-plan/add-course/${id}`, { courses });
  return data.data;
};

export const setProgramPlanStep = async ({ id, step }: { id: any; step: any }) => {
  const { data } = await Api.post(`/program-plan/update-step/${id}`, {
    step,
  });
  return data.data;
};

export const deleteLearningOutcome = async ({
  id,
  learning_outcome_id,
}: {
  id: number;
  learning_outcome_id: number;
}) => {
  const { data } = await Api.post(`/program-plan/delete-learning-outcome/${id}`, {
    _method: 'delete',
    learning_outcome_id: learning_outcome_id,
  });
  return data.data;
};

export const editLearningOutcome = async ({ id, form }: { id: number; form: IeditLearning }) => {
  const { data } = await Api.post(`/program-plan/update-learning-outcome/${id}`, {
    _method: 'put',
    learning_outcome_id: form.learning_outcome_id,
    outcome: form.outcome,
    learning_skill_type: form.learning_skill_type,
  });
  return data.data;
};

export const deleteSkill = async ({ id, program_skill_id }: { id: number; program_skill_id: number }) => {
  const { data } = await Api.post(`/program-plan/delete-skill/${id}`, {
    _method: 'delete',
    program_skill_id: program_skill_id,
  });
  return data.data;
};

export const editSkill = async ({ id, form }: { id: number; form: IeditSkill }) => {
  const { data } = await Api.post(`/program-plan/update-skill/${id}`, {
    _method: 'put',
    program_skill_id: form.program_skill_id,
    name: form.name,
    description: form.description,
    skill_type: form.skill_type,
  });
  return data.data;
};

export const deleteCourse = async ({ id, course_id }: { id: number; course_id: number }) => {
  const { data } = await Api.post(`/program-plan/delete-course/${id}`, {
    _method: 'delete',
    course_id: course_id,
  });
  return data.data;
};

export const editCourse = async ({ id, form }: { id: number; form: IeditCourse }) => {
  const { data } = await Api.post(`/program-plan/update-course/${id}`, {
    _method: 'put',
    course_id: form.course_id,
    title: form.title,
    difficulty_level_id: form.difficulty_level_id,
    learning_hours: form.learning_hours,
  });
  return data.data;
};

export const addCourseLearningOutcome = async ({ id, course_id }: { id: any; course_id: any }) => {
  const { data } = await Api.post(`/program-plan/course/add-learning-outcome/${id}`, {
    course_id,
  });
  return data.data;
};

export const deleteCourseLearningOutcome = async ({
  id,
  course_id,
  learning_outcome_id,
}: {
  id: any;
  course_id: any;
  learning_outcome_id: any;
}) => {
  const { data } = await Api.delete(`/program-plan/course/delete-learning-outcome/${id}`, {
    course_id,
    learning_outcome_id,
  });
  return data.data;
};

export const updateCourseLearningOutcome = async ({
  id,
  course_id,
  learning_outcome_id,
  outcome,
  learning_skill_type,
}: {
  id: any;
  course_id: any;
  learning_outcome_id: any;
  outcome: any;
  learning_skill_type: any;
}) => {
  const { data } = await Api.put(`/program-plan/course/update-learning-outcome/${id}`, {
    course_id,
    learning_outcome_id,
    outcome,
    learning_skill_type,
  });
  return data.data;
};
