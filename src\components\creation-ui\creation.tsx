import React from 'react';
import { ScrollArea } from '@/components/ui/scroll-area';
import i18n from '@/utils/i18n';

const ToolCreation = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex flex-col h-full max-h-[calc(100dvh-152px)] bg-white dark:bg-background border border-1 shadow-sm rounded-lg relative overflow-hidden">
      {children}
    </div>
  );
};

const Header = ({ children }: { children: React.ReactNode }) => {
  return <div className="flex-none p-3 border-b">{children}</div>;
};

const Content = ({ children }: { children: React.ReactNode }) => {
  return (
    <div className="flex-1 min-h-0 relative">
      <ScrollArea className="h-full">
        <div style={{ direction: i18n.dir() }} className="px-4 pb-10 my-3">
          {children}
        </div>
      </ScrollArea>
      <div className="absolute bottom-0 left-3 right-3 h-[40px] bg-gradient-to-t from-background backdrop-blur-sm pointer-events-none" />
    </div>
  );
};

const Buttons = ({ children }: { children: React.ReactNode }) => {
  return <div className="flex-none mb-3 relative px-4">{children}</div>;
};

ToolCreation.Header = Header;
ToolCreation.Content = Content;
ToolCreation.Buttons = Buttons;

export default ToolCreation;
